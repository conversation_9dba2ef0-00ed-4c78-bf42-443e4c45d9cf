'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  Target, 
  Download, 
  RefreshCw, 
  AlertTriangle,
  CheckCircle2,
  Clock,
  Zap,
  Info
} from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { SortingResult, BatteryDevice, BatteryChannel } from '@/lib/types';
import { useTranslations } from 'next-intl';

interface SortingResultsProps {
  devices: BatteryDevice[];
  results: SortingResult[];
  selectedDeviceId?: string;
  selectedChannelId?: string;
  isLoading?: boolean;
  onRefresh: () => void;
  onExport: () => void;
  onResultSelect?: (result: SortingResult) => void;
}

export function SortingResults({
  devices,
  results,
  selectedDeviceId,
  selectedChannelId,
  isLoading = false,
  onRefresh,
  onExport,
  onResultSelect
}: SortingResultsProps) {
  const t = useTranslations('batterySorting.results');
  const [sortBy, setSortBy] = useState<'time' | 'grade' | 'device'>('time');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');

  // 获取设备信息
  const getDeviceInfo = (deviceId: string) => {
    return devices.find(d => d.id === deviceId);
  };

  // 获取通道信息
  const getChannelInfo = (channelId: string, deviceId: string) => {
    const device = getDeviceInfo(deviceId);
    return device?.channels.find(c => c.id === channelId);
  };

  // 过滤和排序结果
  const filteredAndSortedResults = React.useMemo(() => {
    let filtered = results;

    // 按选中的设备过滤
    if (selectedDeviceId) {
      filtered = filtered.filter(result => result.deviceId === selectedDeviceId);
    }

    // 按选中的通道过滤
    if (selectedChannelId) {
      filtered = filtered.filter(result => result.channelId === selectedChannelId);
    }

    // 排序
    return filtered.sort((a, b) => {
      let comparison = 0;
      
      switch (sortBy) {
        case 'time':
          comparison = a.sortingTime.getTime() - b.sortingTime.getTime();
          break;
        case 'grade':
          const gradeA = a.grade?.name || 'Z';
          const gradeB = b.grade?.name || 'Z';
          comparison = gradeA.localeCompare(gradeB);
          break;
        case 'device':
          const deviceA = getDeviceInfo(a.deviceId)?.name || '';
          const deviceB = getDeviceInfo(b.deviceId)?.name || '';
          comparison = deviceA.localeCompare(deviceB);
          break;
      }
      
      return sortOrder === 'asc' ? comparison : -comparison;
    });
  }, [results, selectedDeviceId, selectedChannelId, sortBy, sortOrder, devices]);

  // 计算统计信息
  const statistics = React.useMemo(() => {
    const total = filteredAndSortedResults.length;
    const graded = filteredAndSortedResults.filter(r => r.grade).length;
    const errors = filteredAndSortedResults.filter(r => r.errorMessage).length;
    
    // 等级分布
    const gradeDistribution: Record<string, number> = {};
    filteredAndSortedResults.forEach(result => {
      if (result.grade) {
        gradeDistribution[result.grade.name] = (gradeDistribution[result.grade.name] || 0) + 1;
      }
    });

    return {
      total,
      graded,
      errors,
      ungraded: total - graded - errors,
      gradeDistribution
    };
  }, [filteredAndSortedResults]);

  // 获取等级颜色
  const getGradeColor = (grade: string) => {
    const colorMap: Record<string, string> = {
      'A级': '#22c55e',
      'B级': '#3b82f6',
      'C级': '#f59e0b',
      'D级': '#f97316',
      'E级': '#ef4444'
    };
    return colorMap[grade] || '#6b7280';
  };

  // 切换排序
  const handleSort = (field: typeof sortBy) => {
    if (sortBy === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(field);
      setSortOrder('asc');
    }
  };

  return (
    <Card className="h-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Target className="h-5 w-5" />
            {t('title')}
            <Badge variant="secondary">{statistics.total}</Badge>
          </CardTitle>
          
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={onRefresh}
              disabled={isLoading}
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
              {t('refresh')}
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={onExport}
              disabled={statistics.total === 0}
            >
              <Download className="h-4 w-4 mr-2" />
              {t('export')}
            </Button>
          </div>
        </div>

        {/* 统计信息 */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">{statistics.total}</div>
            <div className="text-sm text-muted-foreground">{t('stats.total')}</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">{statistics.graded}</div>
            <div className="text-sm text-muted-foreground">{t('stats.graded')}</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-yellow-600">{statistics.ungraded}</div>
            <div className="text-sm text-muted-foreground">{t('stats.ungraded')}</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-red-600">{statistics.errors}</div>
            <div className="text-sm text-muted-foreground">{t('stats.errors')}</div>
          </div>
        </div>

        {/* 等级分布 */}
        {Object.keys(statistics.gradeDistribution).length > 0 && (
          <div className="flex flex-wrap gap-2 mt-4">
            <span className="text-sm font-medium">{t('gradeDistribution')}:</span>
            {Object.entries(statistics.gradeDistribution).map(([grade, count]) => (
              <Badge
                key={grade}
                variant="outline"
                style={{ 
                  borderColor: getGradeColor(grade),
                  color: getGradeColor(grade)
                }}
              >
                {grade}: {count}
              </Badge>
            ))}
          </div>
        )}
      </CardHeader>
      
      <CardContent className="p-0">
        {isLoading ? (
          <div className="flex items-center justify-center h-64">
            <RefreshCw className="h-8 w-8 animate-spin text-muted-foreground" />
            <span className="ml-2 text-muted-foreground">{t('loading')}</span>
          </div>
        ) : filteredAndSortedResults.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-64 text-muted-foreground">
            <Target className="h-12 w-12 mb-4" />
            <p>{t('noResults')}</p>
            <p className="text-sm mt-2">{t('noResultsHint')}</p>
          </div>
        ) : (
          <ScrollArea className="h-[500px]">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead 
                    className="cursor-pointer"
                    onClick={() => handleSort('device')}
                  >
                    {t('device')} {sortBy === 'device' && (sortOrder === 'asc' ? '↑' : '↓')}
                  </TableHead>
                  <TableHead>{t('channel')}</TableHead>
                  <TableHead 
                    className="cursor-pointer"
                    onClick={() => handleSort('grade')}
                  >
                    {t('grade')} {sortBy === 'grade' && (sortOrder === 'asc' ? '↑' : '↓')}
                  </TableHead>
                  <TableHead>{t('errorMessage')}</TableHead>
                  <TableHead 
                    className="cursor-pointer"
                    onClick={() => handleSort('time')}
                  >
                    {t('sortingTime')} {sortBy === 'time' && (sortOrder === 'asc' ? '↑' : '↓')}
                  </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredAndSortedResults.map((result) => {
                  const device = getDeviceInfo(result.deviceId);
                  const channel = getChannelInfo(result.channelId, result.deviceId);
                  
                  return (
                    <TableRow 
                      key={result.id}
                      className={`cursor-pointer hover:bg-muted/50 ${
                        result.channelId === selectedChannelId ? 'bg-primary/5' : ''
                      }`}
                      onClick={() => onResultSelect?.(result)}
                    >
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <div>
                            <div className="font-medium">{device?.name || result.deviceId}</div>
                            <div className="text-sm text-muted-foreground">
                              {device?.location}
                            </div>
                          </div>
                        </div>
                      </TableCell>
                      
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Zap className="h-4 w-4 text-muted-foreground" />
                          <span>{t('channelNumber')} {channel?.channelNumber || '?'}</span>
                          {result.batteryId && (
                            <Badge variant="outline" className="text-xs">
                              {result.batteryId}
                            </Badge>
                          )}
                        </div>
                      </TableCell>
                      
                      <TableCell>
                        {result.grade ? (
                          <Badge
                            style={{
                              backgroundColor: result.grade.color + '20',
                              borderColor: result.grade.color,
                              color: result.grade.color
                            }}
                          >
                            {result.grade.name}
                          </Badge>
                        ) : result.errorMessage ? (
                          <Badge variant="destructive">
                            <AlertTriangle className="h-3 w-3 mr-1" />
                            {t('error')}
                          </Badge>
                        ) : (
                          <Badge variant="outline">
                            <Clock className="h-3 w-3 mr-1" />
                            {t('pending')}
                          </Badge>
                        )}
                      </TableCell>
                      
                      <TableCell>
                        {result.errorMessage ? (
                          <div className="max-w-xs">
                            <Alert variant="destructive" className="p-2">
                              <AlertTriangle className="h-3 w-3" />
                              <AlertDescription className="text-xs">
                                {result.errorMessage}
                              </AlertDescription>
                            </Alert>
                          </div>
                        ) : (
                          <span className="text-muted-foreground">-</span>
                        )}
                      </TableCell>
                      
                      <TableCell>
                        <div className="text-sm">
                          {result.sortingTime.toLocaleString()}
                        </div>
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          </ScrollArea>
        )}
      </CardContent>
    </Card>
  );
}

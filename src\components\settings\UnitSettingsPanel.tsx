'use client';

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { 
  Zap, 
  Gauge, 
  Battery, 
  Thermometer,
  Clock,
  Hash
} from 'lucide-react';
import { useTranslations } from 'next-intl';
import { UnitSettings } from '@/lib/types';

interface UnitSettingsPanelProps {
  settings: UnitSettings;
  onSettingsChange: (settings: UnitSettings) => void;
}

export function UnitSettingsPanel({
  settings,
  onSettingsChange
}: UnitSettingsPanelProps) {
  const t = useTranslations('settings.units');

  const updateSetting = <K extends keyof UnitSettings>(
    key: K,
    value: UnitSettings[K]
  ) => {
    onSettingsChange({
      ...settings,
      [key]: value
    });
  };

  const unitCategories = [
    {
      title: t('electrical.title'),
      icon: Zap,
      description: t('electrical.description'),
      units: [
        {
          key: 'voltage' as const,
          label: t('electrical.voltage'),
          options: [
            { value: 'V', label: 'V (伏特)' },
            { value: 'mV', label: 'mV (毫伏)' }
          ]
        },
        {
          key: 'current' as const,
          label: t('electrical.current'),
          options: [
            { value: 'A', label: 'A (安培)' },
            { value: 'mA', label: 'mA (毫安)' }
          ]
        },
        {
          key: 'power' as const,
          label: t('electrical.power'),
          options: [
            { value: 'W', label: 'W (瓦特)' },
            { value: 'mW', label: 'mW (毫瓦)' }
          ]
        },
        {
          key: 'resistance' as const,
          label: t('electrical.resistance'),
          options: [
            { value: 'Ω', label: 'Ω (欧姆)' },
            { value: 'mΩ', label: 'mΩ (毫欧)' },
            { value: 'kΩ', label: 'kΩ (千欧)' }
          ]
        }
      ]
    },
    {
      title: t('energy.title'),
      icon: Battery,
      description: t('energy.description'),
      units: [
        {
          key: 'capacity' as const,
          label: t('energy.capacity'),
          options: [
            { value: 'Ah', label: 'Ah (安时)' },
            { value: 'mAh', label: 'mAh (毫安时)' }
          ]
        },
        {
          key: 'energy' as const,
          label: t('energy.energy'),
          options: [
            { value: 'Wh', label: 'Wh (瓦时)' },
            { value: 'mWh', label: 'mWh (毫瓦时)' }
          ]
        }
      ]
    },
    {
      title: t('environmental.title'),
      icon: Thermometer,
      description: t('environmental.description'),
      units: [
        {
          key: 'temperature' as const,
          label: t('environmental.temperature'),
          options: [
            { value: '°C', label: '°C (摄氏度)' },
            { value: '°F', label: '°F (华氏度)' },
            { value: 'K', label: 'K (开尔文)' }
          ]
        },
        {
          key: 'time' as const,
          label: t('environmental.time'),
          options: [
            { value: 's', label: 's (秒)' },
            { value: 'min', label: 'min (分钟)' },
            { value: 'h', label: 'h (小时)' }
          ]
        }
      ]
    }
  ];

  return (
    <div className="space-y-6">
      {/* 小数点设置 */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-base">
            <Hash className="h-4 w-4" />
            {t('precision.title')}
          </CardTitle>
          <CardDescription>
            {t('precision.description')}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <Label htmlFor="decimalPlaces">{t('precision.decimalPlaces')}</Label>
            <Input
              id="decimalPlaces"
              type="number"
              min="0"
              max="6"
              value={settings.decimalPlaces}
              onChange={(e) => updateSetting('decimalPlaces', parseInt(e.target.value) || 0)}
              className="w-24"
            />
            <p className="text-sm text-muted-foreground">
              {t('precision.example')}: {(3.14159).toFixed(settings.decimalPlaces)}
            </p>
          </div>
        </CardContent>
      </Card>

      {/* 单位设置 */}
      {unitCategories.map((category) => {
        const Icon = category.icon;
        return (
          <Card key={category.title}>
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center gap-2 text-base">
                <Icon className="h-4 w-4" />
                {category.title}
              </CardTitle>
              <CardDescription>
                {category.description}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {category.units.map((unit) => (
                  <div key={unit.key} className="space-y-2">
                    <Label htmlFor={unit.key}>{unit.label}</Label>
                    <Select
                      value={settings[unit.key] as string}
                      onValueChange={(value) => updateSetting(unit.key, value as any)}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {unit.options.map((option) => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        );
      })}

      {/* 单位转换预览 */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-base">
            <Gauge className="h-4 w-4" />
            {t('preview.title')}
          </CardTitle>
          <CardDescription>
            {t('preview.description')}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div className="space-y-1">
              <p className="font-medium">{t('electrical.voltage')}</p>
              <p className="text-muted-foreground">
                3.7 {settings.voltage}
              </p>
            </div>
            <div className="space-y-1">
              <p className="font-medium">{t('electrical.current')}</p>
              <p className="text-muted-foreground">
                1.5 {settings.current}
              </p>
            </div>
            <div className="space-y-1">
              <p className="font-medium">{t('energy.capacity')}</p>
              <p className="text-muted-foreground">
                2.5 {settings.capacity}
              </p>
            </div>
            <div className="space-y-1">
              <p className="font-medium">{t('environmental.temperature')}</p>
              <p className="text-muted-foreground">
                25 {settings.temperature}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

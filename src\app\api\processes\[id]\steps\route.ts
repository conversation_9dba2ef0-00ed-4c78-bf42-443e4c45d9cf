import { NextRequest, NextResponse } from 'next/server';
import { ProcessService } from '@/lib/process/services/processService';
import { Step } from '@/lib/process/types/step';

// POST /api/processes/[id]/steps - 向工艺流程添加工步
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const resolvedParams = await params;
  try {
    const body = await request.json();
    const stepData: Step = body;

    const processService = new ProcessService();
    const processStep = await processService.addStepToProcess(resolvedParams.id, stepData);

    return NextResponse.json({
      success: true,
      data: processStep
    });
  } catch (error) {
    console.error('添加工步到工艺流程失败:', error);
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : '添加工步到工艺流程失败'
      },
      { status: 500 }
    );
  }
}

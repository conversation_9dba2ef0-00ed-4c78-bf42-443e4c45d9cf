import { useEffect, useState } from 'react';
import { device<PERSON>pi, channel<PERSON>pi, alarm<PERSON>pi, systemApi, wsManager } from '@/lib/api/deviceApi';
import { useDeviceStore } from '@/stores/deviceStore';
import { Device, Channel, Alarm, DeviceStatus, AlarmLevel } from '@/lib/types';

// 数据服务类 - 统一管理数据获取和同步
export class DataService {
  private static instance: DataService;
  private isInitialized = false;
  private syncInterval: NodeJS.Timeout | null = null;

  private constructor() {}

  static getInstance(): DataService {
    if (!DataService.instance) {
      DataService.instance = new DataService();
    }
    return DataService.instance;
  }

  // 初始化数据服务
  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      // 尝试连接WebSocket
      await this.connectWebSocket();
      
      // 初始化数据
      await this.loadInitialData();
      
      // 启动定期同步
      this.startPeriodicSync();
      
      this.isInitialized = true;
      console.log('Data service initialized successfully');
    } catch (error) {
      console.error('Failed to initialize data service:', error);
      // 如果API连接失败，回退到模拟数据
      this.fallbackToSimulation();
    }
  }

  // 连接WebSocket
  private async connectWebSocket(): Promise<void> {
    try {
      await wsManager.connect();
      
      // 订阅设备数据更新
      wsManager.subscribe('device_update', (data: Device) => {
        const store = useDeviceStore.getState();
        const devices = store.devices.map(device => 
          device.deviceName === data.deviceName ? data : device
        );
        store.setDevices(devices);
      });

      // 订阅通道数据更新
      wsManager.subscribe('channel_update', (data: Channel) => {
        const store = useDeviceStore.getState();
        const channels = store.channels.map(channel => 
          channel.deviceName === data.deviceName && channel.channelNumber === data.channelNumber 
            ? data : channel
        );
        store.setChannels(channels);
      });

      // 订阅报警更新
      wsManager.subscribe('alarm_update', (data: Alarm) => {
        const store = useDeviceStore.getState();
        store.addAlarm(data);
      });

    } catch (error) {
      console.warn('WebSocket connection failed, will use polling instead:', error);
    }
  }

  // 加载初始数据
  private async loadInitialData(): Promise<void> {
    const store = useDeviceStore.getState();

    try {
      // 并行加载设备和通道数据
      const [devicesResponse, channelsResponse, alarmsResponse] = await Promise.all([
        deviceApi.getDevices(),
        channelApi.getChannels(),
        alarmApi.getActiveAlarms()
      ]);

      if (devicesResponse.success && devicesResponse.data) {
        store.setDevices(devicesResponse.data);
      }

      if (channelsResponse.success && channelsResponse.data) {
        store.setChannels(channelsResponse.data);
      }

      if (alarmsResponse.success && alarmsResponse.data) {
        // 清空现有报警并添加新的
        store.alarms.length = 0;
        alarmsResponse.data.forEach(alarm => store.addAlarm(alarm));
      }

    } catch (error) {
      console.error('Failed to load initial data:', error);
      throw error;
    }
  }

  // 启动定期同步
  private startPeriodicSync(): void {
    // 每30秒同步一次数据
    this.syncInterval = setInterval(async () => {
      await this.syncData();
    }, 30000);
  }

  // 同步数据
  private async syncData(): Promise<void> {
    try {
      const store = useDeviceStore.getState();
      
      // 获取设备实时数据
      const devicePromises = store.devices.map(device => 
        deviceApi.getDeviceRealTimeData(device.deviceName)
      );

      const deviceResponses = await Promise.allSettled(devicePromises);
      
      const updatedDevices = store.devices.map((device, index) => {
        const response = deviceResponses[index];
        if (response.status === 'fulfilled' && response.value.success && response.value.data) {
          return response.value.data;
        }
        return device;
      });

      store.setDevices(updatedDevices);

      // 同步报警数据
      const alarmsResponse = await alarmApi.getActiveAlarms();
      if (alarmsResponse.success && alarmsResponse.data) {
        // 更新报警列表
        store.alarms.length = 0;
        alarmsResponse.data.forEach(alarm => store.addAlarm(alarm));
      }

    } catch (error) {
      console.error('Data sync failed:', error);
    }
  }

  // 回退到模拟数据
  private fallbackToSimulation(): void {
    console.log('Falling back to simulation mode');
    const store = useDeviceStore.getState();
    store.initializeData();
    store.startSimulation();
  }

  // 停止数据服务
  stop(): void {
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
      this.syncInterval = null;
    }
    
    wsManager.disconnect();
    this.isInitialized = false;
  }

  // 设备操作方法
  async updateDeviceStatus(deviceName: string, status: DeviceStatus): Promise<boolean> {
    try {
      const response = await deviceApi.updateDeviceStatus(deviceName, status);
      if (response.success && response.data) {
        const store = useDeviceStore.getState();
        const devices = store.devices.map(device => 
          device.deviceName === deviceName ? response.data! : device
        );
        store.setDevices(devices);
        return true;
      }
    } catch (error) {
      console.error('Failed to update device status:', error);
    }
    return false;
  }

  // 通道操作方法
  async toggleChannel(deviceName: string, channelNumber: number, enabled: boolean): Promise<boolean> {
    try {
      const response = await channelApi.toggleChannel(deviceName, channelNumber, enabled);
      if (response.success && response.data) {
        const store = useDeviceStore.getState();
        const channels = store.channels.map(channel => 
          channel.deviceName === deviceName && channel.channelNumber === channelNumber 
            ? response.data! : channel
        );
        store.setChannels(channels);
        return true;
      }
    } catch (error) {
      console.error('Failed to toggle channel:', error);
    }
    return false;
  }

  // 报警操作方法
  async acknowledgeAlarm(alarmId: string, acknowledgedBy: string): Promise<boolean> {
    try {
      const response = await alarmApi.acknowledgeAlarm(alarmId, acknowledgedBy);
      if (response.success) {
        const store = useDeviceStore.getState();
        store.acknowledgeAlarm(alarmId, acknowledgedBy);
        return true;
      }
    } catch (error) {
      console.error('Failed to acknowledge alarm:', error);
    }
    return false;
  }

  async clearAlarm(alarmId: string): Promise<boolean> {
    try {
      const response = await alarmApi.clearAlarm(alarmId);
      if (response.success) {
        const store = useDeviceStore.getState();
        store.clearAlarm(alarmId);
        return true;
      }
    } catch (error) {
      console.error('Failed to clear alarm:', error);
    }
    return false;
  }

  // 获取系统健康状态
  async getSystemHealth(): Promise<boolean> {
    try {
      const response = await systemApi.healthCheck();
      return response.success;
    } catch (error) {
      console.error('Health check failed:', error);
      return false;
    }
  }
}

// 导出单例实例
export const dataService = DataService.getInstance();

// 自动初始化数据服务的Hook
export function useDataService() {
  const [isInitialized, setIsInitialized] = useState(false);
  const [isHealthy, setIsHealthy] = useState(false);

  useEffect(() => {
    const initializeService = async () => {
      await dataService.initialize();
      setIsInitialized(true);
      
      // 检查系统健康状态
      const healthy = await dataService.getSystemHealth();
      setIsHealthy(healthy);
    };

    initializeService();

    // 清理函数
    return () => {
      dataService.stop();
    };
  }, []);

  return {
    isInitialized,
    isHealthy,
    dataService
  };
}

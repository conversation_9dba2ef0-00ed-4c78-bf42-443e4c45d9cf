import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import {
  Event,
  EventFilter,
  EventStatistics,
  EventPagination,
  EventQueryResult,
  EventSortBy,
  EventViewMode,
  QuickTimeRange,
  EventType,
  EventLevel,
  EventCategory,
  EventSource,
  EventData,
} from '@/types/events';

interface EventState {
  // 事件数据
  events: Event[];
  filteredEvents: Event[];
  selectedEvent: Event | null;
  
  // 筛选和搜索
  filter: EventFilter;
  sortBy: EventSortBy;
  viewMode: EventViewMode;
  quickTimeRange: QuickTimeRange;
  
  // 分页
  pagination: EventPagination;
  
  // 统计
  statistics: EventStatistics;
  
  // UI状态
  isLoading: boolean;
  isRealtime: boolean;
  showFilters: boolean;
  selectedEventIds: string[];
  
  // Actions
  // 事件管理
  addEvent: (event: Omit<Event, 'id' | 'timestamp'>) => void;
  addEvents: (events: Event[]) => void;
  updateEvent: (id: string, updates: Partial<Event>) => void;
  deleteEvent: (id: string) => void;
  clearEvents: () => void;
  acknowledgeEvent: (id: string, acknowledgedBy: string) => void;
  acknowledgeEvents: (ids: string[], acknowledgedBy: string) => void;
  
  // 筛选和搜索
  setFilter: (filter: Partial<EventFilter>) => void;
  clearFilter: () => void;
  setSortBy: (sortBy: EventSortBy) => void;
  setViewMode: (viewMode: EventViewMode) => void;
  setQuickTimeRange: (range: QuickTimeRange) => void;
  searchEvents: (keyword: string) => void;
  
  // 分页
  setPage: (page: number) => void;
  setPageSize: (pageSize: number) => void;
  
  // 选择
  selectEvent: (event: Event | null) => void;
  toggleEventSelection: (id: string) => void;
  selectAllEvents: () => void;
  clearSelection: () => void;
  
  // UI状态
  setLoading: (loading: boolean) => void;
  setRealtime: (realtime: boolean) => void;
  toggleFilters: () => void;
  
  // 数据处理
  applyFilters: () => void;
  updateStatistics: () => void;
  generateMockEvents: (count: number) => void;
}

// 生成唯一ID
const generateId = () => `event_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

// 获取时间范围
const getTimeRange = (range: QuickTimeRange): { start: number; end: number } => {
  const now = Date.now();
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  
  switch (range) {
    case QuickTimeRange.LAST_HOUR:
      return { start: now - 60 * 60 * 1000, end: now };
    case QuickTimeRange.TODAY:
      return { start: today.getTime(), end: now };
    case QuickTimeRange.YESTERDAY:
      const yesterday = new Date(today);
      yesterday.setDate(yesterday.getDate() - 1);
      return { start: yesterday.getTime(), end: today.getTime() };
    case QuickTimeRange.LAST_7_DAYS:
      return { start: now - 7 * 24 * 60 * 60 * 1000, end: now };
    case QuickTimeRange.LAST_30_DAYS:
      return { start: now - 30 * 24 * 60 * 60 * 1000, end: now };
    case QuickTimeRange.THIS_MONTH:
      const thisMonth = new Date(today.getFullYear(), today.getMonth(), 1);
      return { start: thisMonth.getTime(), end: now };
    case QuickTimeRange.LAST_MONTH:
      const lastMonth = new Date(today.getFullYear(), today.getMonth() - 1, 1);
      const lastMonthEnd = new Date(today.getFullYear(), today.getMonth(), 0, 23, 59, 59, 999);
      return { start: lastMonth.getTime(), end: lastMonthEnd.getTime() };
    default:
      return { start: 0, end: now };
  }
};

// 初始状态
const initialState = {
  events: [],
  filteredEvents: [],
  selectedEvent: null,
  filter: {},
  sortBy: EventSortBy.TIMESTAMP_DESC,
  viewMode: EventViewMode.LIST,
  quickTimeRange: QuickTimeRange.TODAY,
  pagination: {
    page: 1,
    pageSize: 50,
    total: 0,
    totalPages: 0,
  },
  statistics: {
    total: 0,
    byCategory: {
      [EventCategory.DEVICE]: 0,
      [EventCategory.CHANNEL]: 0,
      [EventCategory.USER]: 0,
      [EventCategory.SYSTEM]: 0,
    },
    byLevel: {
      [EventLevel.INFO]: 0,
      [EventLevel.WARNING]: 0,
      [EventLevel.ERROR]: 0,
      [EventLevel.CRITICAL]: 0,
    },
    byDevice: {},
    recentCount: 0,
  },
  isLoading: false,
  isRealtime: true,
  showFilters: false,
  selectedEventIds: [],
};

export const useEventStore = create<EventState>()(
  persist(
    (set, get) => ({
      ...initialState,
      
      // 事件管理
      addEvent: (eventData) => {
        const event: Event = {
          ...eventData,
          id: generateId(),
          timestamp: Date.now(),
        };
        
        set((state) => {
          const newEvents = [event, ...state.events];
          return { events: newEvents };
        });
        
        get().applyFilters();
        get().updateStatistics();
      },
      
      addEvents: (events) => {
        set((state) => ({
          events: [...events, ...state.events].sort((a, b) => b.timestamp - a.timestamp),
        }));
        get().applyFilters();
        get().updateStatistics();
      },
      
      updateEvent: (id, updates) => {
        set((state) => ({
          events: state.events.map((event) =>
            event.id === id ? { ...event, ...updates } : event
          ),
        }));
        get().applyFilters();
        get().updateStatistics();
      },
      
      deleteEvent: (id) => {
        set((state) => ({
          events: state.events.filter((event) => event.id !== id),
          selectedEventIds: state.selectedEventIds.filter((selectedId) => selectedId !== id),
        }));
        get().applyFilters();
        get().updateStatistics();
      },
      
      clearEvents: () => {
        set({ events: [], filteredEvents: [], selectedEventIds: [] });
        get().updateStatistics();
      },
      
      acknowledgeEvent: (id, acknowledgedBy) => {
        get().updateEvent(id, {
          acknowledged: true,
          acknowledgedBy,
          acknowledgedAt: Date.now(),
        });
      },
      
      acknowledgeEvents: (ids, acknowledgedBy) => {
        ids.forEach((id) => {
          get().acknowledgeEvent(id, acknowledgedBy);
        });
      },
      
      // 筛选和搜索
      setFilter: (newFilter) => {
        set((state) => ({
          filter: { ...state.filter, ...newFilter },
        }));
        get().applyFilters();
      },
      
      clearFilter: () => {
        set({ filter: {} });
        get().applyFilters();
      },
      
      setSortBy: (sortBy) => {
        set({ sortBy });
        get().applyFilters();
      },
      
      setViewMode: (viewMode) => {
        set({ viewMode });
      },
      
      setQuickTimeRange: (range) => {
        set({ quickTimeRange: range });
        if (range !== QuickTimeRange.CUSTOM) {
          const { start, end } = getTimeRange(range);
          get().setFilter({ startTime: start, endTime: end });
        }
      },
      
      searchEvents: (keyword) => {
        get().setFilter({ searchKeyword: keyword });
      },
      
      // 分页
      setPage: (page) => {
        set((state) => ({
          pagination: { ...state.pagination, page },
        }));
      },
      
      setPageSize: (pageSize) => {
        set((state) => ({
          pagination: { ...state.pagination, pageSize, page: 1 },
        }));
        get().applyFilters();
      },
      
      // 选择
      selectEvent: (event) => {
        set({ selectedEvent: event });
      },
      
      toggleEventSelection: (id) => {
        set((state) => {
          const isSelected = state.selectedEventIds.includes(id);
          return {
            selectedEventIds: isSelected
              ? state.selectedEventIds.filter((selectedId) => selectedId !== id)
              : [...state.selectedEventIds, id],
          };
        });
      },
      
      selectAllEvents: () => {
        set((state) => ({
          selectedEventIds: state.filteredEvents.map((event) => event.id),
        }));
      },
      
      clearSelection: () => {
        set({ selectedEventIds: [] });
      },
      
      // UI状态
      setLoading: (loading) => {
        set({ isLoading: loading });
      },
      
      setRealtime: (realtime) => {
        set({ isRealtime: realtime });
      },
      
      toggleFilters: () => {
        set((state) => ({ showFilters: !state.showFilters }));
      },
      
      // 数据处理
      applyFilters: () => {
        const { events, filter, sortBy, pagination } = get();
        
        let filtered = [...events];
        
        // 应用筛选条件
        if (filter.startTime || filter.endTime) {
          filtered = filtered.filter((event) => {
            if (filter.startTime && event.timestamp < filter.startTime) return false;
            if (filter.endTime && event.timestamp > filter.endTime) return false;
            return true;
          });
        }
        
        if (filter.categories?.length) {
          filtered = filtered.filter((event) => filter.categories!.includes(event.category));
        }
        
        if (filter.types?.length) {
          filtered = filtered.filter((event) => filter.types!.includes(event.type));
        }
        
        if (filter.levels?.length) {
          filtered = filtered.filter((event) => filter.levels!.includes(event.level));
        }
        
        if (filter.deviceNames?.length) {
          filtered = filtered.filter((event) => 
            event.source.deviceName && filter.deviceNames!.includes(event.source.deviceName)
          );
        }
        
        if (filter.channelNumbers?.length) {
          filtered = filtered.filter((event) => 
            event.source.channelNumber && filter.channelNumbers!.includes(event.source.channelNumber)
          );
        }
        
        if (filter.searchKeyword) {
          const keyword = filter.searchKeyword.toLowerCase();
          filtered = filtered.filter((event) =>
            event.title.toLowerCase().includes(keyword) ||
            event.description.toLowerCase().includes(keyword) ||
            event.source.deviceName?.toLowerCase().includes(keyword)
          );
        }
        
        if (filter.acknowledgedOnly) {
          filtered = filtered.filter((event) => event.acknowledged);
        }
        
        if (filter.unacknowledgedOnly) {
          filtered = filtered.filter((event) => !event.acknowledged);
        }
        
        // 应用排序
        filtered.sort((a, b) => {
          switch (sortBy) {
            case EventSortBy.TIMESTAMP_DESC:
              return b.timestamp - a.timestamp;
            case EventSortBy.TIMESTAMP_ASC:
              return a.timestamp - b.timestamp;
            case EventSortBy.LEVEL_DESC:
              const levelOrder = { critical: 4, error: 3, warning: 2, info: 1 };
              return levelOrder[b.level] - levelOrder[a.level];
            case EventSortBy.LEVEL_ASC:
              const levelOrderAsc = { critical: 4, error: 3, warning: 2, info: 1 };
              return levelOrderAsc[a.level] - levelOrderAsc[b.level];
            case EventSortBy.CATEGORY:
              return a.category.localeCompare(b.category);
            case EventSortBy.DEVICE:
              return (a.source.deviceName || '').localeCompare(b.source.deviceName || '');
            default:
              return b.timestamp - a.timestamp;
          }
        });
        
        // 更新分页信息
        const total = filtered.length;
        const totalPages = Math.ceil(total / pagination.pageSize);
        
        set({
          filteredEvents: filtered,
          pagination: {
            ...pagination,
            total,
            totalPages,
          },
        });
      },
      
      updateStatistics: () => {
        const { events } = get();
        const now = Date.now();
        const last24Hours = now - 24 * 60 * 60 * 1000;
        
        const statistics: EventStatistics = {
          total: events.length,
          byCategory: {
            [EventCategory.DEVICE]: 0,
            [EventCategory.CHANNEL]: 0,
            [EventCategory.USER]: 0,
            [EventCategory.SYSTEM]: 0,
          },
          byLevel: {
            [EventLevel.INFO]: 0,
            [EventLevel.WARNING]: 0,
            [EventLevel.ERROR]: 0,
            [EventLevel.CRITICAL]: 0,
          },
          byDevice: {},
          recentCount: 0,
        };
        
        events.forEach((event) => {
          statistics.byCategory[event.category]++;
          statistics.byLevel[event.level]++;
          
          if (event.source.deviceName) {
            statistics.byDevice[event.source.deviceName] = 
              (statistics.byDevice[event.source.deviceName] || 0) + 1;
          }
          
          if (event.timestamp > last24Hours) {
            statistics.recentCount++;
          }
        });
        
        set({ statistics });
      },
      
      generateMockEvents: (count) => {
        const mockEvents: Event[] = [];
        const deviceNames = ['设备A1', '设备B2', '设备C3', '设备D4', '设备E5'];
        const userNames = ['张三', '李四', '王五', '赵六'];

        for (let i = 0; i < count; i++) {
          const timestamp = Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000; // 过去7天内
          const deviceName = deviceNames[Math.floor(Math.random() * deviceNames.length)];
          const channelNumber = Math.floor(Math.random() * 64) + 1;
          const userName = userNames[Math.floor(Math.random() * userNames.length)];

          // 随机选择事件类型
          const eventTypes = [
            {
              type: EventType.DEVICE_ONLINE,
              category: EventCategory.DEVICE,
              level: EventLevel.INFO,
              title: `设备${deviceName}上线`,
              description: `设备${deviceName}已成功连接到系统`,
              source: { type: 'device' as const, deviceName },
            },
            {
              type: EventType.DEVICE_OFFLINE,
              category: EventCategory.DEVICE,
              level: EventLevel.WARNING,
              title: `设备${deviceName}离线`,
              description: `设备${deviceName}失去连接，请检查网络状态`,
              source: { type: 'device' as const, deviceName },
            },
            {
              type: EventType.CHANNEL_ENABLED,
              category: EventCategory.CHANNEL,
              level: EventLevel.INFO,
              title: `通道${channelNumber}已启用`,
              description: `设备${deviceName}的通道${channelNumber}已被启用`,
              source: { type: 'channel' as const, deviceName, channelNumber },
            },
            {
              type: EventType.CHANNEL_DISABLED,
              category: EventCategory.CHANNEL,
              level: EventLevel.WARNING,
              title: `通道${channelNumber}已禁用`,
              description: `设备${deviceName}的通道${channelNumber}已被禁用`,
              source: { type: 'channel' as const, deviceName, channelNumber },
            },
            {
              type: EventType.CHANNEL_PARAMETER_EXCEEDED,
              category: EventCategory.CHANNEL,
              level: EventLevel.ERROR,
              title: `通道${channelNumber}参数超限`,
              description: `设备${deviceName}的通道${channelNumber}电压超过安全阈值`,
              source: { type: 'channel' as const, deviceName, channelNumber },
              data: {
                currentValue: 4.5 + Math.random() * 0.5,
                threshold: 4.5,
                parameter: 'voltage',
              },
            },
            {
              type: EventType.USER_LOGIN,
              category: EventCategory.USER,
              level: EventLevel.INFO,
              title: `用户${userName}登录`,
              description: `用户${userName}成功登录系统`,
              source: { type: 'user' as const, userId: `user_${i}`, userName },
            },
            {
              type: EventType.SYSTEM_ERROR,
              category: EventCategory.SYSTEM,
              level: EventLevel.CRITICAL,
              title: '系统错误',
              description: '数据库连接异常，请联系管理员',
              source: { type: 'system' as const, systemComponent: 'database' },
              data: {
                errorCode: 'DB_CONNECTION_FAILED',
                errorMessage: 'Connection timeout after 30 seconds',
              },
            },
          ];

          const eventTemplate = eventTypes[Math.floor(Math.random() * eventTypes.length)];

          const event: Event = {
            id: generateId(),
            timestamp,
            ...eventTemplate,
          };

          mockEvents.push(event);
        }

        get().addEvents(mockEvents);
      },
    }),
    {
      name: 'event-store',
      partialize: (state) => ({
        viewMode: state.viewMode,
        quickTimeRange: state.quickTimeRange,
        pagination: state.pagination,
        isRealtime: state.isRealtime,
        showFilters: state.showFilters,
      }),
    }
  )
);

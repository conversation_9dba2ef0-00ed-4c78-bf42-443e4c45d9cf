'use client';

import React, { useState, useEffect } from 'react';
import { useTranslations } from 'next-intl';
import { Ta<PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Settings,
  Target,
  Play,
  Pause,
  RotateCcw
} from 'lucide-react';

import { SortingRuleEditor } from '@/components/battery-sorting/SortingRuleEditor';
import { DeviceList } from '@/components/battery-sorting/DeviceList';
import { SortingFilterComponent } from '@/components/battery-sorting/SortingFilter';
import { SortingResults } from '@/components/battery-sorting/SortingResults';
import { TestSessionList } from '@/components/battery-sorting/TestSessionList';
import { BatteryResultsTable } from '@/components/battery-sorting/BatteryResultsTable';

import {
  SortingRule,
  SortingFilter,
  SortingResult,
  BatteryDevice,
  <PERSON><PERSON>han<PERSON>,
  BatteryDataR<PERSON>ord,
  StepType,
  ModuleSwitch
} from '@/lib/types';
import { TestSession, BatteryResult } from '@/lib/types/battery-sorting';
import { BatterySortingEngine } from '@/lib/utils/batterySorting';
import { getTestSessions, getBatteryResultsBySession } from '@/lib/services/batterySortingService';

export default function BatterySortingPage() {
  const t = useTranslations('batterySorting');


  // 状态管理
  const [currentRule, setCurrentRule] = useState<SortingRule | null>(null);
  const [devices, setDevices] = useState<BatteryDevice[]>([]);
  const [sortingResults, setSortingResults] = useState<SortingResult[]>([]);
  const [filter, setFilter] = useState<SortingFilter>({});
  const [selectedDeviceId, setSelectedDeviceId] = useState<string>();
  const [selectedChannelId, setSelectedChannelId] = useState<string>();
  const [isAutoSorting, setIsAutoSorting] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  // 分选结果查看状态
  const [testSessions, setTestSessions] = useState<TestSession[]>([]);
  const [selectedSession, setSelectedSession] = useState<TestSession | null>(null);
  const [batteryResults, setBatteryResults] = useState<BatteryResult[]>([]);
  const [isLoadingSessions, setIsLoadingSessions] = useState(false);
  const [isLoadingResults, setIsLoadingResults] = useState(false);

  // 模拟设备数据
  useEffect(() => {
    const mockDevices: BatteryDevice[] = [
      {
        id: 'device-1',
        name: '电池测试设备-01',
        type: 'BTS-8000',
        location: '测试车间A',
        status: 'online',
        lastUpdate: new Date(),
        channels: Array.from({ length: 8 }, (_, i) => ({
          id: `channel-${i + 1}`,
          deviceId: 'device-1',
          channelNumber: i + 1,
          batteryId: `BAT-${String(i + 1).padStart(3, '0')}`,
          status: ['idle', 'testing', 'completed', 'error'][Math.floor(Math.random() * 4)] as any,
          currentStep: Math.floor(Math.random() * 10) + 1,
          currentCycle: Math.floor(Math.random() * 100) + 1,
          lastUpdate: new Date(),
          testData: generateMockTestData()
        }))
      },
      {
        id: 'device-2',
        name: '电池测试设备-02',
        type: 'BTS-8000',
        location: '测试车间B',
        status: 'online',
        lastUpdate: new Date(),
        channels: Array.from({ length: 8 }, (_, i) => ({
          id: `channel-${i + 9}`,
          deviceId: 'device-2',
          channelNumber: i + 1,
          batteryId: `BAT-${String(i + 9).padStart(3, '0')}`,
          status: ['idle', 'testing', 'completed', 'error'][Math.floor(Math.random() * 4)] as any,
          currentStep: Math.floor(Math.random() * 10) + 1,
          currentCycle: Math.floor(Math.random() * 100) + 1,
          lastUpdate: new Date(),
          testData: generateMockTestData()
        }))
      }
    ];

    setDevices(mockDevices);

    // 加载测试会话数据
    loadTestSessions();
  }, []);

  // 生成模拟测试数据
  function generateMockTestData(): BatteryDataRecord[] {
    const data: BatteryDataRecord[] = [];
    const cycles = 3;
    const stepsPerCycle = 4;

    for (let cycle = 1; cycle <= cycles; cycle++) {
      for (let step = 1; step <= stepsPerCycle; step++) {
        const stepType = step === 1 ? StepType.REST :
                        step === 2 ? StepType.CC_CHARGE :
                        step === 3 ? StepType.REST : StepType.CC_DISCHARGE;

        for (let i = 0; i < 10; i++) {
          data.push({
            dataSequence: data.length + 1,
            cycleNumber: cycle,
            stepNumber: step,
            stepType,
            time: `${String(Math.floor(i / 60)).padStart(2, '0')}:${String(i % 60).padStart(2, '0')}:00`,
            totalTime: `${String(Math.floor((data.length * 60) / 3600)).padStart(2, '0')}:${String(Math.floor((data.length * 60) % 3600 / 60)).padStart(2, '0')}:00`,
            absoluteTime: new Date(Date.now() - (data.length * 60000)),
            current: stepType === StepType.CC_CHARGE ? 1.0 : stepType === StepType.CC_DISCHARGE ? -1.0 : 0,
            voltage: 3.2 + Math.random() * 0.8,
            power: Math.random() * 4,
            contactResistance: 5 + Math.random() * 2,
            capacity: Math.random() * 3.5,
            specificCapacity: Math.random() * 150,
            chargeCapacity: stepType === StepType.CC_CHARGE ? Math.random() * 3.5 : 0,
            chargeSpecificCapacity: stepType === StepType.CC_CHARGE ? Math.random() * 150 : 0,
            dischargeCapacity: stepType === StepType.CC_DISCHARGE ? Math.random() * 3.2 : 0,
            dischargeSpecificCapacity: stepType === StepType.CC_DISCHARGE ? Math.random() * 140 : 0,
            energy: Math.random() * 12,
            specificEnergy: Math.random() * 500,
            chargeEnergy: stepType === StepType.CC_CHARGE ? Math.random() * 12 : 0,
            chargeSpecificEnergy: stepType === StepType.CC_CHARGE ? Math.random() * 500 : 0,
            dischargeEnergy: stepType === StepType.CC_DISCHARGE ? Math.random() * 11 : 0,
            dischargeSpecificEnergy: stepType === StepType.CC_DISCHARGE ? Math.random() * 480 : 0,
            dQdV: Math.random() * 100,
            dQmdV: Math.random() * 1000,
            moduleSwitch: ModuleSwitch.ON,
            v1: 3.2 + Math.random() * 0.8,
            temperature: 25 + Math.random() * 10,
            auxVoltageDiff: 0,
            auxTempDiff: 0
          });
        }
      }
    }

    return data;
  }

  // 加载测试会话
  const loadTestSessions = async () => {
    try {
      setIsLoadingSessions(true);
      const sessions = await getTestSessions();
      setTestSessions(sessions);

      // 自动选择第一个会话
      if (sessions.length > 0) {
        handleSessionSelect(sessions[0]);
      }
    } catch (error) {
      console.error('加载测试会话失败:', error);
    } finally {
      setIsLoadingSessions(false);
    }
  };

  // 选择测试会话
  const handleSessionSelect = async (session: TestSession) => {
    if (selectedSession?.id === session.id) return;

    try {
      setSelectedSession(session);
      setIsLoadingResults(true);
      const results = await getBatteryResultsBySession(session.id);
      setBatteryResults(results);
    } catch (error) {
      console.error('加载电池结果失败:', error);
      setBatteryResults([]);
    } finally {
      setIsLoadingResults(false);
    }
  };

  // 查看详情
  const handleViewDetail = (result: BatteryResult) => {
    console.log('查看详情:', result);
    // TODO: 实现详情查看功能
  };

  return (
    <div className="h-screen flex flex-col overflow-hidden">
      {/* 页面标题 */}
      <div className="flex items-center justify-between p-6 flex-shrink-0 border-b">
        <div>
          <h1 className="text-3xl font-bold flex items-center gap-2">
            <Target className="h-8 w-8" />
            电池分选
          </h1>
          <p className="text-muted-foreground mt-2">电池测试数据的智能分选和质量分级</p>
        </div>

        <div className="flex items-center gap-2">
          {currentRule && (
            <>
              <Badge variant="outline">
                当前规则: {currentRule.name}
              </Badge>
              <Button
                onClick={() => {/* handleRunSorting */}}
                disabled={isLoading}
                className="ml-2"
              >
                <Play className="h-4 w-4 mr-2" />
                执行分选
              </Button>
            </>
          )}
        </div>
      </div>

      {/* 主要功能标签页 */}
      <Tabs defaultValue="results" className="flex-1 flex flex-col overflow-hidden">
        <div className="px-6 pt-4 flex-shrink-0">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="results" className="flex items-center gap-2">
              <Target className="h-4 w-4" />
              分选结果查看
            </TabsTrigger>
            <TabsTrigger value="settings" className="flex items-center gap-2">
              <Settings className="h-4 w-4" />
              分选条件设置
            </TabsTrigger>
          </TabsList>
        </div>

        <TabsContent value="results" className="flex-1 flex flex-col overflow-hidden p-6">
          {/* 分选结果查看界面 */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 h-full overflow-hidden">
            {/* 左侧：测试会话列表 */}
            <div className="lg:col-span-1 h-full overflow-hidden">
              <TestSessionList
                sessions={testSessions}
                selectedSessionId={selectedSession?.id}
                onSessionSelect={handleSessionSelect}
                isLoading={isLoadingSessions}
                className="h-full"
              />
            </div>

            {/* 右侧：电池结果表格 */}
            <div className="lg:col-span-2 h-full overflow-hidden">
              <BatteryResultsTable
                results={batteryResults}
                sessionName={selectedSession?.processName}
                isLoading={isLoadingResults}
                onViewDetail={handleViewDetail}
                className="h-full"
              />
            </div>
          </div>
        </TabsContent>

        <TabsContent value="settings" className="flex-1 flex flex-col overflow-hidden p-6">
          {/* 分选条件设置界面 */}
          <div className="h-full overflow-auto">
            <SortingRuleEditor
              rule={currentRule || undefined}
              onSave={(rule) => {
                setCurrentRule(rule);
                console.log('保存分选规则:', rule);
              }}
              onTest={(rule) => {
                console.log('测试分选规则:', rule);
              }}
            />
          </div>
        </TabsContent>
      </Tabs>

    </div>
  );
}

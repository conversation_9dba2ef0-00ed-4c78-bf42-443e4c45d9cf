'use client';

import React, { useMemo } from 'react';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  <PERSON>lt<PERSON>,
  Legend,
  ResponsiveContainer,
  ReferenceLine
} from 'recharts';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { BatteryDataRecord, StepType } from '@/lib/types';
import { useTranslations } from 'next-intl';

interface VoltageTimeChartProps {
  data: BatteryDataRecord[];
  selectedCycles?: number[];
  showStepTypes?: StepType[];
  height?: number;
}

interface ChartDataPoint {
  time: number;
  voltage: number;
  stepType: StepType;
  cycleNumber: number;
  current: number;
  temperature: number;
  absoluteTime: string;
}

export function VoltageTimeChart({
  data,
  selectedCycles,
  showStepTypes = [StepType.CC_CHARGE, StepType.CC_DISCHARGE, StepType.REST],
  height = 400
}: VoltageTimeChartProps) {
  const t = useTranslations('batteryAnalysis.charts');

  // 处理和过滤数据
  const chartData = useMemo(() => {
    let filteredData = data;

    // 按循环号过滤
    if (selectedCycles && selectedCycles.length > 0) {
      filteredData = filteredData.filter(record => 
        selectedCycles.includes(record.cycleNumber)
      );
    }

    // 按工步类型过滤
    filteredData = filteredData.filter(record => 
      showStepTypes.includes(record.stepType)
    );

    // 转换为图表数据格式
    return filteredData.map((record, index) => ({
      time: index, // 使用索引作为时间轴
      voltage: record.voltage,
      stepType: record.stepType,
      cycleNumber: record.cycleNumber,
      current: record.current,
      temperature: record.temperature,
      absoluteTime: record.absoluteTime.toLocaleString()
    }));
  }, [data, selectedCycles, showStepTypes]);

  // 获取工步类型颜色
  const getStepTypeColor = (stepType: StepType): string => {
    const colorMap: Record<StepType, string> = {
      [StepType.CC_CHARGE]: '#22c55e',      // 绿色 - 充电
      [StepType.CC_DISCHARGE]: '#ef4444',   // 红色 - 放电
      [StepType.CV_CHARGE]: '#3b82f6',      // 蓝色 - 恒压充电
      [StepType.CCCV_CHARGE]: '#8b5cf6',    // 紫色 - 恒流恒压充电
      [StepType.REST]: '#6b7280'            // 灰色 - 搁置
    };
    return colorMap[stepType] || '#6b7280';
  };

  // 自定义工具提示
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload as ChartDataPoint;
      return (
        <div className="bg-background border rounded-lg p-3 shadow-lg">
          <p className="font-medium mb-2">{t('voltageTime.tooltip.title')}</p>
          <div className="space-y-1 text-sm">
            <div className="flex justify-between gap-4">
              <span className="text-muted-foreground">{t('voltageTime.voltage')}:</span>
              <span className="font-medium">{data.voltage.toFixed(4)} V</span>
            </div>
            <div className="flex justify-between gap-4">
              <span className="text-muted-foreground">{t('voltageTime.current')}:</span>
              <span className="font-medium">{data.current.toFixed(4)} A</span>
            </div>
            <div className="flex justify-between gap-4">
              <span className="text-muted-foreground">{t('voltageTime.stepType')}:</span>
              <Badge 
                variant="secondary" 
                style={{ backgroundColor: getStepTypeColor(data.stepType) + '20' }}
              >
                {data.stepType}
              </Badge>
            </div>
            <div className="flex justify-between gap-4">
              <span className="text-muted-foreground">{t('voltageTime.cycle')}:</span>
              <span className="font-medium">{data.cycleNumber}</span>
            </div>
            <div className="flex justify-between gap-4">
              <span className="text-muted-foreground">{t('voltageTime.temperature')}:</span>
              <span className="font-medium">{data.temperature.toFixed(1)} °C</span>
            </div>
            <div className="flex justify-between gap-4">
              <span className="text-muted-foreground">{t('voltageTime.time')}:</span>
              <span className="font-medium text-xs">{data.absoluteTime}</span>
            </div>
          </div>
        </div>
      );
    }
    return null;
  };

  // 计算统计信息
  const stats = useMemo(() => {
    if (chartData.length === 0) return null;

    const voltages = chartData.map(d => d.voltage);
    const maxVoltage = Math.max(...voltages);
    const minVoltage = Math.min(...voltages);
    const avgVoltage = voltages.reduce((sum, v) => sum + v, 0) / voltages.length;

    return {
      maxVoltage,
      minVoltage,
      avgVoltage,
      dataPoints: chartData.length,
      cycles: [...new Set(chartData.map(d => d.cycleNumber))].length
    };
  }, [chartData]);

  if (chartData.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>{t('voltageTime.title')}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-64 text-muted-foreground">
            {t('voltageTime.noData')}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle>{t('voltageTime.title')}</CardTitle>
          {stats && (
            <div className="flex gap-2">
              <Badge variant="outline">
                {t('voltageTime.dataPoints')}: {stats.dataPoints.toLocaleString()}
              </Badge>
              <Badge variant="outline">
                {t('voltageTime.cycles')}: {stats.cycles}
              </Badge>
            </div>
          )}
        </div>
        {stats && (
          <div className="flex gap-4 text-sm text-muted-foreground">
            <span>{t('voltageTime.maxVoltage')}: {stats.maxVoltage.toFixed(4)}V</span>
            <span>{t('voltageTime.minVoltage')}: {stats.minVoltage.toFixed(4)}V</span>
            <span>{t('voltageTime.avgVoltage')}: {stats.avgVoltage.toFixed(4)}V</span>
          </div>
        )}
      </CardHeader>
      <CardContent>
        <ResponsiveContainer width="100%" height={height}>
          <LineChart data={chartData} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
            <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
            <XAxis 
              dataKey="time"
              type="number"
              scale="linear"
              domain={['dataMin', 'dataMax']}
              tickFormatter={(value) => `${Math.floor(value / 100)}`}
              label={{ value: t('voltageTime.timeAxis'), position: 'insideBottom', offset: -5 }}
            />
            <YAxis 
              domain={['dataMin - 0.1', 'dataMax + 0.1']}
              label={{ value: t('voltageTime.voltageAxis'), angle: -90, position: 'insideLeft' }}
            />
            <Tooltip content={<CustomTooltip />} />
            <Legend />
            
            {/* 根据工步类型分组绘制不同颜色的线段 */}
            {showStepTypes.map(stepType => {
              const stepData = chartData.filter(d => d.stepType === stepType);
              if (stepData.length === 0) return null;
              
              return (
                <Line
                  key={stepType}
                  dataKey="voltage"
                  data={stepData}
                  stroke={getStepTypeColor(stepType)}
                  strokeWidth={1.5}
                  dot={false}
                  name={stepType}
                  connectNulls={false}
                />
              );
            })}
            
            {/* 添加参考线 */}
            {stats && (
              <>
                <ReferenceLine 
                  y={stats.avgVoltage} 
                  stroke="#6b7280" 
                  strokeDasharray="5 5" 
                  label={`${t('voltageTime.avgLine')}: ${stats.avgVoltage.toFixed(3)}V`}
                />
              </>
            )}
          </LineChart>
        </ResponsiveContainer>
      </CardContent>
    </Card>
  );
}

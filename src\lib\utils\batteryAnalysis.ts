import { BatteryDataRecord, BatteryPerformanceStats, StepType } from '@/lib/types';

/**
 * 计算电池性能统计分析
 */
export function calculateBatteryPerformanceStats(data: BatteryDataRecord[]): BatteryPerformanceStats {
  if (data.length === 0) {
    return createEmptyStats();
  }

  // 按循环号分组数据
  const cycleGroups = groupDataByCycle(data);
  const cycleNumbers = Object.keys(cycleGroups).map(Number).sort((a, b) => a - b);

  // 计算容量统计
  const capacityStats = calculateCapacityStats(cycleGroups, cycleNumbers);
  
  // 计算能量统计
  const energyStats = calculateEnergyStats(cycleGroups, cycleNumbers);
  
  // 计算循环统计
  const cycleStats = calculateCycleStats(cycleGroups, cycleNumbers);
  
  // 计算温度统计
  const temperatureStats = calculateTemperatureStats(data);
  
  // 计算电压统计
  const voltageStats = calculateVoltageStats(data);

  return {
    capacityStats,
    energyStats,
    cycleStats,
    temperatureStats,
    voltageStats
  };
}

/**
 * 按循环号分组数据
 */
function groupDataByCycle(data: BatteryDataRecord[]): Record<number, BatteryDataRecord[]> {
  return data.reduce((groups, record) => {
    const cycle = record.cycleNumber;
    if (!groups[cycle]) {
      groups[cycle] = [];
    }
    groups[cycle].push(record);
    return groups;
  }, {} as Record<number, BatteryDataRecord[]>);
}

/**
 * 计算容量统计
 */
function calculateCapacityStats(
  cycleGroups: Record<number, BatteryDataRecord[]>,
  cycleNumbers: number[]
) {
  const cycleCapacities: Array<{
    cycle: number;
    chargeCapacity: number;
    dischargeCapacity: number;
  }> = [];

  // 计算每个循环的最大容量
  cycleNumbers.forEach(cycle => {
    const records = cycleGroups[cycle];
    const chargeRecords = records.filter(r => r.stepType === StepType.CC_CHARGE);
    const dischargeRecords = records.filter(r => r.stepType === StepType.CC_DISCHARGE);
    
    const maxChargeCapacity = Math.max(...chargeRecords.map(r => r.chargeCapacity), 0);
    const maxDischargeCapacity = Math.max(...dischargeRecords.map(r => r.dischargeCapacity), 0);
    
    cycleCapacities.push({
      cycle,
      chargeCapacity: maxChargeCapacity,
      dischargeCapacity: maxDischargeCapacity
    });
  });

  if (cycleCapacities.length === 0) {
    return {
      initial: 0,
      current: 0,
      retention: 0,
      degradationRate: 0,
      maxCapacity: 0,
      minCapacity: 0,
      avgCapacity: 0
    };
  }

  const dischargeCapacities = cycleCapacities.map(c => c.dischargeCapacity);
  const initial = dischargeCapacities[0] || 0;
  const current = dischargeCapacities[dischargeCapacities.length - 1] || 0;
  const retention = initial > 0 ? (current / initial) * 100 : 0;
  
  // 计算容量衰减率 (每循环百分比)
  const degradationRate = cycleCapacities.length > 1 && initial > 0
    ? ((initial - current) / initial / cycleCapacities.length) * 100
    : 0;

  return {
    initial,
    current,
    retention,
    degradationRate,
    maxCapacity: Math.max(...dischargeCapacities),
    minCapacity: Math.min(...dischargeCapacities),
    avgCapacity: dischargeCapacities.reduce((sum, cap) => sum + cap, 0) / dischargeCapacities.length
  };
}

/**
 * 计算能量统计
 */
function calculateEnergyStats(
  cycleGroups: Record<number, BatteryDataRecord[]>,
  cycleNumbers: number[]
) {
  const efficiencies: number[] = [];
  let totalChargeEnergy = 0;
  let totalDischargeEnergy = 0;

  cycleNumbers.forEach(cycle => {
    const records = cycleGroups[cycle];
    const chargeRecords = records.filter(r => r.stepType === StepType.CC_CHARGE);
    const dischargeRecords = records.filter(r => r.stepType === StepType.CC_DISCHARGE);
    
    const maxChargeEnergy = Math.max(...chargeRecords.map(r => r.chargeEnergy), 0);
    const maxDischargeEnergy = Math.max(...dischargeRecords.map(r => r.dischargeEnergy), 0);
    
    totalChargeEnergy += maxChargeEnergy;
    totalDischargeEnergy += maxDischargeEnergy;
    
    if (maxChargeEnergy > 0) {
      efficiencies.push((maxDischargeEnergy / maxChargeEnergy) * 100);
    }
  });

  const avgEfficiency = efficiencies.length > 0
    ? efficiencies.reduce((sum, eff) => sum + eff, 0) / efficiencies.length
    : 0;

  const roundTripEfficiency = totalChargeEnergy > 0
    ? (totalDischargeEnergy / totalChargeEnergy) * 100
    : 0;

  return {
    chargeEfficiency: avgEfficiency, // 简化处理，实际应该分别计算
    dischargeEfficiency: avgEfficiency,
    roundTripEfficiency,
    energyDensity: 0, // 需要电池重量信息
    powerDensity: 0   // 需要电池重量信息
  };
}

/**
 * 计算循环统计
 */
function calculateCycleStats(
  cycleGroups: Record<number, BatteryDataRecord[]>,
  cycleNumbers: number[]
) {
  const cycleTimes: number[] = [];

  cycleNumbers.forEach(cycle => {
    const records = cycleGroups[cycle];
    if (records.length > 0) {
      const startTime = records[0].absoluteTime.getTime();
      const endTime = records[records.length - 1].absoluteTime.getTime();
      const cycleTimeHours = (endTime - startTime) / (1000 * 60 * 60);
      cycleTimes.push(cycleTimeHours);
    }
  });

  const avgCycleTime = cycleTimes.length > 0
    ? cycleTimes.reduce((sum, time) => sum + time, 0) / cycleTimes.length
    : 0;

  return {
    totalCycles: cycleNumbers.length,
    completedCycles: cycleNumbers.length, // 简化处理
    avgCycleTime,
    maxCycleTime: cycleTimes.length > 0 ? Math.max(...cycleTimes) : 0,
    minCycleTime: cycleTimes.length > 0 ? Math.min(...cycleTimes) : 0
  };
}

/**
 * 计算温度统计
 */
function calculateTemperatureStats(data: BatteryDataRecord[]) {
  const temperatures = data.map(record => record.temperature);
  
  if (temperatures.length === 0) {
    return {
      avgTemperature: 0,
      maxTemperature: 0,
      minTemperature: 0,
      tempVariation: 0
    };
  }

  const maxTemperature = Math.max(...temperatures);
  const minTemperature = Math.min(...temperatures);
  const avgTemperature = temperatures.reduce((sum, temp) => sum + temp, 0) / temperatures.length;

  return {
    avgTemperature,
    maxTemperature,
    minTemperature,
    tempVariation: maxTemperature - minTemperature
  };
}

/**
 * 计算电压统计
 */
function calculateVoltageStats(data: BatteryDataRecord[]) {
  const voltages = data.map(record => record.voltage);
  
  if (voltages.length === 0) {
    return {
      avgVoltage: 0,
      maxVoltage: 0,
      minVoltage: 0,
      voltageRange: 0
    };
  }

  const maxVoltage = Math.max(...voltages);
  const minVoltage = Math.min(...voltages);
  const avgVoltage = voltages.reduce((sum, voltage) => sum + voltage, 0) / voltages.length;

  return {
    avgVoltage,
    maxVoltage,
    minVoltage,
    voltageRange: maxVoltage - minVoltage
  };
}

/**
 * 创建空的统计数据
 */
function createEmptyStats(): BatteryPerformanceStats {
  return {
    capacityStats: {
      initial: 0,
      current: 0,
      retention: 0,
      degradationRate: 0,
      maxCapacity: 0,
      minCapacity: 0,
      avgCapacity: 0
    },
    energyStats: {
      chargeEfficiency: 0,
      dischargeEfficiency: 0,
      roundTripEfficiency: 0,
      energyDensity: 0,
      powerDensity: 0
    },
    cycleStats: {
      totalCycles: 0,
      completedCycles: 0,
      avgCycleTime: 0,
      maxCycleTime: 0,
      minCycleTime: 0
    },
    temperatureStats: {
      avgTemperature: 0,
      maxTemperature: 0,
      minTemperature: 0,
      tempVariation: 0
    },
    voltageStats: {
      avgVoltage: 0,
      maxVoltage: 0,
      minVoltage: 0,
      voltageRange: 0
    }
  };
}

/**
 * 计算容量衰减趋势
 */
export function calculateCapacityFadeTrend(data: BatteryDataRecord[]): Array<{
  cycle: number;
  capacity: number;
  retention: number;
  fadeRate: number;
}> {
  const cycleGroups = groupDataByCycle(data);
  const cycleNumbers = Object.keys(cycleGroups).map(Number).sort((a, b) => a - b);
  
  const trendData: Array<{
    cycle: number;
    capacity: number;
    retention: number;
    fadeRate: number;
  }> = [];

  let initialCapacity = 0;

  cycleNumbers.forEach((cycle, index) => {
    const records = cycleGroups[cycle];
    const dischargeRecords = records.filter(r => r.stepType === StepType.CC_DISCHARGE);
    const maxDischargeCapacity = Math.max(...dischargeRecords.map(r => r.dischargeCapacity), 0);

    if (index === 0) {
      initialCapacity = maxDischargeCapacity;
    }

    const retention = initialCapacity > 0 ? (maxDischargeCapacity / initialCapacity) * 100 : 100;
    const fadeRate = index > 0 && initialCapacity > 0
      ? ((initialCapacity - maxDischargeCapacity) / initialCapacity / (index + 1)) * 100
      : 0;

    trendData.push({
      cycle,
      capacity: maxDischargeCapacity,
      retention,
      fadeRate
    });
  });

  return trendData;
}

/**
 * 预测电池寿命 (基于线性衰减模型)
 */
export function predictBatteryLifetime(
  data: BatteryDataRecord[],
  eolThreshold: number = 80 // 寿命终止阈值 (%)
): {
  predictedCycles: number;
  currentRetention: number;
  fadeRatePerCycle: number;
  confidence: 'high' | 'medium' | 'low';
} {
  const trendData = calculateCapacityFadeTrend(data);
  
  if (trendData.length < 3) {
    return {
      predictedCycles: 0,
      currentRetention: 0,
      fadeRatePerCycle: 0,
      confidence: 'low'
    };
  }

  const currentRetention = trendData[trendData.length - 1].retention;
  const fadeRatePerCycle = trendData[trendData.length - 1].fadeRate;

  // 简单线性预测
  const remainingRetention = currentRetention - eolThreshold;
  const predictedCycles = fadeRatePerCycle > 0
    ? Math.ceil(remainingRetention / fadeRatePerCycle)
    : 0;

  // 评估预测置信度
  let confidence: 'high' | 'medium' | 'low' = 'low';
  if (trendData.length >= 10) {
    confidence = 'high';
  } else if (trendData.length >= 5) {
    confidence = 'medium';
  }

  return {
    predictedCycles: Math.max(0, predictedCycles),
    currentRetention,
    fadeRatePerCycle,
    confidence
  };
}

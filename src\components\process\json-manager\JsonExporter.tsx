'use client';

import React, { useState } from 'react';
import { useTranslations } from 'next-intl';
import { Download, FileText, Settings, CheckCircle, AlertCircle } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';

import { Step } from '@/lib/process/types/step';
import { exportStepsToJson, downloadJsonFile } from '@/lib/process/services/jsonService';

interface JsonExporterProps {
  steps: Step[];
  selectedStepIds?: string[];
  disabled?: boolean;
}

export function JsonExporter({ steps, selectedStepIds = [], disabled = false }: JsonExporterProps) {
  const t = useTranslations('process.jsonExporter');
  
  const [isOpen, setIsOpen] = useState(false);
  const [exportOptions, setExportOptions] = useState({
    filename: `steps_export_${new Date().toISOString().slice(0, 10)}.json`,
    description: '',
    exportedBy: 'PDCS用户',
    exportAll: true,
    includeAuxiliaryParams: true,
    includeMetadata: true
  });
  const [isExporting, setIsExporting] = useState(false);
  const [exportResult, setExportResult] = useState<{ success: boolean; message: string } | null>(null);

  // 获取要导出的工步
  const getStepsToExport = (): Step[] => {
    if (exportOptions.exportAll || selectedStepIds.length === 0) {
      return steps;
    }
    return steps.filter(step => selectedStepIds.includes(step.id));
  };

  const handleExport = async () => {
    setIsExporting(true);
    setExportResult(null);

    try {
      const stepsToExport = getStepsToExport();
      
      if (stepsToExport.length === 0) {
        setExportResult({
          success: false,
          message: '没有可导出的工步数据'
        });
        return;
      }

      // 处理辅助参数
      const processedSteps = exportOptions.includeAuxiliaryParams 
        ? stepsToExport 
        : stepsToExport.map(step => ({
            ...step,
            auxiliaryParams: {
              voltage: { enabled: false, checkTime: 0, upperLimit: 0, lowerLimit: 0, upperRange: 0, lowerRange: 0, deviation: 0, absoluteValue: 0, checkInterval: 0, fluctuationValue: 0, continuousCount: 0, totalCount: 0, rateValue: 0, comparator: '>' as const, timeInterval: 0 },
              current: { enabled: false, startTime: 0, upperCurrent: 0, lowerCurrent: 0, currentValue: 0, checkInterval: 0, dropValue: 0 },
              capacity: { enabled: false, ccCapacityLimit: { value: 0, enabled: false }, cvCapacityLimit: { value: 0, enabled: false } },
              samplingInterval: { enabled: false, voltageDiff: 0, currentDiff: 0, temperatureDiff: 0, capacityDiff: 0, powerDiff: 0 },
              stepProtection: { enabled: false, impedanceUpperLimit: 0, impedanceLowerLimit: 0 }
            }
          }));

      const result = exportStepsToJson(processedSteps, {
        filename: exportOptions.filename,
        description: exportOptions.description || undefined,
        exportedBy: exportOptions.exportedBy
      });

      if (result.success && result.data && result.filename) {
        downloadJsonFile(result.data, result.filename);
        setExportResult({
          success: true,
          message: `成功导出 ${processedSteps.length} 个工步到文件 ${result.filename}`
        });
        
        // 延迟关闭对话框
        setTimeout(() => {
          setIsOpen(false);
        }, 2000);
      } else {
        setExportResult({
          success: false,
          message: result.error || '导出失败'
        });
      }
    } catch (error) {
      setExportResult({
        success: false,
        message: error instanceof Error ? error.message : '导出过程中发生错误'
      });
    } finally {
      setIsExporting(false);
    }
  };

  const handleClose = () => {
    setIsOpen(false);
    setExportResult(null);
  };

  const stepsToExport = getStepsToExport();
  const hasSelectedSteps = selectedStepIds.length > 0;

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" disabled={disabled || steps.length === 0}>
          <Download className="h-4 w-4 mr-2" />
          导出JSON
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-4xl max-h-[90vh] flex flex-col">
        <DialogHeader className="flex-shrink-0">
          <DialogTitle>导出工步流程</DialogTitle>
          <DialogDescription>
            将工步流程配置导出为JSON格式文件
          </DialogDescription>
        </DialogHeader>

        <div className="flex-1 overflow-y-auto space-y-6 pr-2" style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}>
          <style jsx>{`
            div::-webkit-scrollbar {
              display: none;
            }
          `}</style>
          {/* 导出范围选择 */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">导出范围</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="exportAll"
                  checked={exportOptions.exportAll}
                  onCheckedChange={(checked) => 
                    setExportOptions(prev => ({ ...prev, exportAll: checked as boolean }))
                  }
                />
                <Label htmlFor="exportAll">导出所有工步</Label>
                <Badge variant="outline">{steps.length} 个工步</Badge>
              </div>

              {hasSelectedSteps && (
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="exportSelected"
                    checked={!exportOptions.exportAll}
                    onCheckedChange={(checked) => 
                      setExportOptions(prev => ({ ...prev, exportAll: !checked }))
                    }
                  />
                  <Label htmlFor="exportSelected">仅导出选中的工步</Label>
                  <Badge variant="outline">{selectedStepIds.length} 个工步</Badge>
                </div>
              )}

              <div className="p-3 bg-muted rounded-md text-sm">
                <div className="font-medium mb-1">将导出工步数量: {stepsToExport.length}</div>
                {stepsToExport.length > 0 && (
                  <div className="text-muted-foreground">
                    包含: {stepsToExport.map(s => s.name).slice(0, 3).join(', ')}
                    {stepsToExport.length > 3 && ` 等 ${stepsToExport.length} 个工步`}
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* 导出选项 */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">导出选项</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="includeAuxiliaryParams"
                  checked={exportOptions.includeAuxiliaryParams}
                  onCheckedChange={(checked) => 
                    setExportOptions(prev => ({ ...prev, includeAuxiliaryParams: checked as boolean }))
                  }
                />
                <Label htmlFor="includeAuxiliaryParams">包含辅助参数组</Label>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="includeMetadata"
                  checked={exportOptions.includeMetadata}
                  onCheckedChange={(checked) => 
                    setExportOptions(prev => ({ ...prev, includeMetadata: checked as boolean }))
                  }
                />
                <Label htmlFor="includeMetadata">包含元数据信息</Label>
              </div>
            </CardContent>
          </Card>

          {/* 文件信息 */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">文件信息</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="filename">文件名</Label>
                <Input
                  id="filename"
                  value={exportOptions.filename}
                  onChange={(e) => 
                    setExportOptions(prev => ({ ...prev, filename: e.target.value }))
                  }
                  placeholder="steps_export.json"
                />
                <p className="text-xs text-muted-foreground">
                  文件将自动下载到浏览器默认下载目录
                </p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="exportedBy">导出者</Label>
                <Input
                  id="exportedBy"
                  value={exportOptions.exportedBy}
                  onChange={(e) => 
                    setExportOptions(prev => ({ ...prev, exportedBy: e.target.value }))
                  }
                  placeholder="请输入导出者名称"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">描述信息</Label>
                <Textarea
                  id="description"
                  value={exportOptions.description}
                  onChange={(e) => 
                    setExportOptions(prev => ({ ...prev, description: e.target.value }))
                  }
                  placeholder="请输入导出文件的描述信息（可选）"
                  className="min-h-[80px]"
                />
                <p className="text-xs text-muted-foreground">
                  描述信息将保存在导出文件的元数据中
                </p>
              </div>
            </CardContent>
          </Card>

          {/* 导出结果 */}
          {exportResult && (
            <Alert variant={exportResult.success ? "default" : "destructive"}>
              {exportResult.success ? (
                <CheckCircle className="h-4 w-4" />
              ) : (
                <AlertCircle className="h-4 w-4" />
              )}
              <AlertDescription>{exportResult.message}</AlertDescription>
            </Alert>
          )}

          {/* 预览信息 */}
          <Card className="border-muted">
            <CardHeader>
              <CardTitle className="text-lg flex items-center space-x-2">
                <FileText className="h-5 w-5" />
                <span>导出预览</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-muted-foreground">工步数量：</span>
                  <span className="font-medium ml-2">{stepsToExport.length}</span>
                </div>
                <div>
                  <span className="text-muted-foreground">文件格式：</span>
                  <span className="font-medium ml-2">JSON</span>
                </div>
                <div>
                  <span className="text-muted-foreground">包含辅助参数：</span>
                  <span className="font-medium ml-2">
                    {exportOptions.includeAuxiliaryParams ? '是' : '否'}
                  </span>
                </div>
                <div>
                  <span className="text-muted-foreground">包含元数据：</span>
                  <span className="font-medium ml-2">
                    {exportOptions.includeMetadata ? '是' : '否'}
                  </span>
                </div>
              </div>

              <Separator />

              <div className="text-xs text-muted-foreground">
                <p>导出的JSON文件可以在其他PDCS系统中导入，或用于备份和版本控制。</p>
                <p>文件将包含完整的工步配置信息，确保数据的完整性和可移植性。</p>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* 操作按钮 - 固定在底部 */}
        <div className="flex-shrink-0 flex items-center justify-between pt-4 border-t">
          <Button variant="outline" onClick={handleClose}>
            取消
          </Button>

          <div className="flex items-center space-x-2">
            <Button
              onClick={handleExport}
              disabled={isExporting || stepsToExport.length === 0 || !exportOptions.filename.trim()}
            >
              {isExporting ? (
                <>
                  <Settings className="h-4 w-4 mr-2 animate-spin" />
                  导出中...
                </>
              ) : (
                <>
                  <Download className="h-4 w-4 mr-2" />
                  开始导出
                </>
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}

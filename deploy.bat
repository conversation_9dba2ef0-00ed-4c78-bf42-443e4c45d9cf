@echo off
echo 🚀 开始部署朗天PDCS系统...

REM 检查Node.js是否安装
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js未安装，请先安装Node.js 18+
    pause
    exit /b 1
)

REM 检查npm是否可用
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ npm不可用
    pause
    exit /b 1
)

echo ✅ Node.js环境检查通过

REM 安装依赖
echo 📦 安装依赖...
npm install
if %errorlevel% neq 0 (
    echo ❌ 依赖安装失败
    pause
    exit /b 1
)

REM 构建项目
echo 🔨 构建生产版本...
npm run build
if %errorlevel% neq 0 (
    echo ❌ 构建失败
    pause
    exit /b 1
)

echo ✅ 构建完成！

REM 询问是否立即启动
set /p start_now="是否立即启动服务？(y/n): "
if /i "%start_now%"=="y" (
    echo 🚀 启动服务...
    echo 访问地址: http://localhost:3000
    echo 按 Ctrl+C 停止服务
    npm start
) else (
    echo 💡 手动启动命令: npm start
    echo 💡 访问地址: http://localhost:3000
)

pause

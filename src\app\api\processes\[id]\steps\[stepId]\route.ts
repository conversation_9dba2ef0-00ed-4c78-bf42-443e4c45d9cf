import { NextRequest, NextResponse } from 'next/server';
import { ProcessService } from '@/lib/process/services/processService';
import { Step } from '@/lib/process/types/step';

// PUT /api/processes/[id]/steps/[stepId] - 更新工艺流程中的工步
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string; stepId: string }> }
) {
  const resolvedParams = await params;
  try {
    const body = await request.json();
    const stepData: Step = body;

    const processService = new ProcessService();
    const success = await processService.updateStepInProcess(resolvedParams.id, resolvedParams.stepId, stepData);

    return NextResponse.json({
      success: true,
      data: success
    });
  } catch (error) {
    console.error('更新工艺流程中的工步失败:', error);
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : '更新工艺流程中的工步失败'
      },
      { status: 500 }
    );
  }
}

// DELETE /api/processes/[id]/steps/[stepId] - 从工艺流程中删除工步
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string; stepId: string }> }
) {
  const resolvedParams = await params;
  try {
    const processService = new ProcessService();
    const success = await processService.removeStepFromProcess(resolvedParams.id, resolvedParams.stepId);

    return NextResponse.json({
      success: true,
      data: success
    });
  } catch (error) {
    console.error('从工艺流程中删除工步失败:', error);
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : '从工艺流程中删除工步失败'
      },
      { status: 500 }
    );
  }
}

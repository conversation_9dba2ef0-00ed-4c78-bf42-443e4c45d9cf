'use client';

import React from 'react';
import { useTranslations } from 'next-intl';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Info } from 'lucide-react';

import { StepFormData } from '@/lib/process/types/step';
import { COMPARATOR_OPTIONS } from '@/lib/process/constants/stepConstants';

interface AuxiliaryParametersStepProps {
  data: StepFormData;
  onChange: (data: Partial<StepFormData>) => void;
  errors: string[];
}

export function AuxiliaryParametersStep({ data, onChange, errors }: AuxiliaryParametersStepProps) {
  const t = useTranslations('process.stepWizard.auxiliaryParameters');

  const handleAuxiliaryParamChange = (category: string, field: string, value: any) => {
    onChange({
      auxiliaryParams: {
        ...data.auxiliaryParams,
        [category]: {
          ...data.auxiliaryParams[category as keyof typeof data.auxiliaryParams],
          [field]: value
        }
      }
    });
  };

  const handleNestedParamChange = (category: string, parentField: string, field: string, value: any) => {
    const currentParent = data.auxiliaryParams[category as keyof typeof data.auxiliaryParams] as any;
    onChange({
      auxiliaryParams: {
        ...data.auxiliaryParams,
        [category]: {
          ...currentParent,
          [parentField]: {
            ...currentParent[parentField],
            [field]: value
          }
        }
      }
    });
  };

  return (
    <div className="space-y-6">
      <Alert>
        <Info className="h-4 w-4" />
        <AlertDescription>
          辅助参数组为可选配置，用于高级保护和监控功能。如果不需要特殊保护，可以跳过此步骤。
        </AlertDescription>
      </Alert>

      <Tabs defaultValue="voltage" className="w-full">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="voltage">电压保护</TabsTrigger>
          <TabsTrigger value="current">电流保护</TabsTrigger>
          <TabsTrigger value="capacity">容量限制</TabsTrigger>
          <TabsTrigger value="sampling">采样间隔</TabsTrigger>
          <TabsTrigger value="protection">工步保护</TabsTrigger>
        </TabsList>

        {/* 电压参数组 */}
        <TabsContent value="voltage" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="text-lg">电压保护参数</CardTitle>
                  <CardDescription>配置电压相关的保护和监控参数</CardDescription>
                </div>
                <div className="flex items-center space-x-2">
                  <Label htmlFor="voltage-enabled">启用</Label>
                  <Switch
                    id="voltage-enabled"
                    checked={data.auxiliaryParams.voltage.enabled}
                    onCheckedChange={(checked) => handleAuxiliaryParamChange('voltage', 'enabled', checked)}
                  />
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              {data.auxiliaryParams.voltage.enabled ? (
                <>
                  {/* 定时检查 */}
                  <div className="space-y-3">
                    <h4 className="font-medium">定时检查</h4>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="voltage-checkTime">检查间隔时间 (s)</Label>
                        <Input
                          id="voltage-checkTime"
                          type="number"
                          min="0"
                          max="3600"
                          value={data.auxiliaryParams.voltage.checkTime}
                          onChange={(e) => handleAuxiliaryParamChange('voltage', 'checkTime', parseInt(e.target.value) || 0)}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="voltage-upperLimit">电压上限阈值 (mV)</Label>
                        <Input
                          id="voltage-upperLimit"
                          type="number"
                          min="0"
                          max="5000"
                          value={data.auxiliaryParams.voltage.upperLimit}
                          onChange={(e) => handleAuxiliaryParamChange('voltage', 'upperLimit', parseFloat(e.target.value) || 0)}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="voltage-lowerLimit">电压下限阈值 (mV)</Label>
                        <Input
                          id="voltage-lowerLimit"
                          type="number"
                          min="0"
                          max="5000"
                          value={data.auxiliaryParams.voltage.lowerLimit}
                          onChange={(e) => handleAuxiliaryParamChange('voltage', 'lowerLimit', parseFloat(e.target.value) || 0)}
                        />
                      </div>
                    </div>
                  </div>

                  <Separator />

                  {/* 电压范围检查 */}
                  <div className="space-y-3">
                    <h4 className="font-medium">电压范围检查</h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="voltage-upperRange">范围上限 (mV)</Label>
                        <Input
                          id="voltage-upperRange"
                          type="number"
                          min="0"
                          max="5000"
                          value={data.auxiliaryParams.voltage.upperRange}
                          onChange={(e) => handleAuxiliaryParamChange('voltage', 'upperRange', parseFloat(e.target.value) || 0)}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="voltage-lowerRange">范围下限 (mV)</Label>
                        <Input
                          id="voltage-lowerRange"
                          type="number"
                          min="0"
                          max="5000"
                          value={data.auxiliaryParams.voltage.lowerRange}
                          onChange={(e) => handleAuxiliaryParamChange('voltage', 'lowerRange', parseFloat(e.target.value) || 0)}
                        />
                      </div>
                    </div>
                  </div>

                  <Separator />

                  {/* 电压波动保护 */}
                  <div className="space-y-3">
                    <h4 className="font-medium">电压波动保护</h4>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="voltage-fluctuationValue">波动阈值 (mV)</Label>
                        <Input
                          id="voltage-fluctuationValue"
                          type="number"
                          min="0"
                          max="1000"
                          value={data.auxiliaryParams.voltage.fluctuationValue}
                          onChange={(e) => handleAuxiliaryParamChange('voltage', 'fluctuationValue', parseFloat(e.target.value) || 0)}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="voltage-continuousCount">连续波动次数</Label>
                        <Input
                          id="voltage-continuousCount"
                          type="number"
                          min="0"
                          max="100"
                          value={data.auxiliaryParams.voltage.continuousCount}
                          onChange={(e) => handleAuxiliaryParamChange('voltage', 'continuousCount', parseInt(e.target.value) || 0)}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="voltage-totalCount">累计波动次数</Label>
                        <Input
                          id="voltage-totalCount"
                          type="number"
                          min="0"
                          max="1000"
                          value={data.auxiliaryParams.voltage.totalCount}
                          onChange={(e) => handleAuxiliaryParamChange('voltage', 'totalCount', parseInt(e.target.value) || 0)}
                        />
                      </div>
                    </div>
                  </div>

                  <Separator />

                  {/* 电压速率保护 */}
                  <div className="space-y-3">
                    <h4 className="font-medium">电压速率保护</h4>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="voltage-rateValue">速率阈值 (mV)</Label>
                        <Input
                          id="voltage-rateValue"
                          type="number"
                          min="0"
                          max="1000"
                          value={data.auxiliaryParams.voltage.rateValue}
                          onChange={(e) => handleAuxiliaryParamChange('voltage', 'rateValue', parseFloat(e.target.value) || 0)}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="voltage-comparator">比较运算符</Label>
                        <Select 
                          value={data.auxiliaryParams.voltage.comparator} 
                          onValueChange={(value) => handleAuxiliaryParamChange('voltage', 'comparator', value)}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            {COMPARATOR_OPTIONS.map((option) => (
                              <SelectItem key={option.value} value={option.value}>
                                {option.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="voltage-timeInterval">时间间隔 (s)</Label>
                        <Input
                          id="voltage-timeInterval"
                          type="number"
                          min="0"
                          max="60"
                          value={data.auxiliaryParams.voltage.timeInterval}
                          onChange={(e) => handleAuxiliaryParamChange('voltage', 'timeInterval', parseInt(e.target.value) || 0)}
                        />
                      </div>
                    </div>
                  </div>
                </>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  <p>电压保护参数已禁用</p>
                  <p className="text-sm">启用开关以配置电压保护参数</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* 电流参数组 */}
        <TabsContent value="current" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="text-lg">电流保护参数</CardTitle>
                  <CardDescription>配置电流相关的保护和监控参数</CardDescription>
                </div>
                <div className="flex items-center space-x-2">
                  <Label htmlFor="current-enabled">启用</Label>
                  <Switch
                    id="current-enabled"
                    checked={data.auxiliaryParams.current.enabled}
                    onCheckedChange={(checked) => handleAuxiliaryParamChange('current', 'enabled', checked)}
                  />
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              {data.auxiliaryParams.current.enabled ? (
                <>
                  {/* 定时检查 */}
                  <div className="space-y-3">
                    <h4 className="font-medium">定时检查</h4>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="current-startTime">检查起始时间 (s)</Label>
                        <Input
                          id="current-startTime"
                          type="number"
                          min="0"
                          max="3600"
                          value={data.auxiliaryParams.current.startTime}
                          onChange={(e) => handleAuxiliaryParamChange('current', 'startTime', parseInt(e.target.value) || 0)}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="current-upperCurrent">电流上限 (mA)</Label>
                        <Input
                          id="current-upperCurrent"
                          type="number"
                          min="0"
                          max="10000"
                          value={data.auxiliaryParams.current.upperCurrent}
                          onChange={(e) => handleAuxiliaryParamChange('current', 'upperCurrent', parseFloat(e.target.value) || 0)}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="current-lowerCurrent">电流下限 (mA)</Label>
                        <Input
                          id="current-lowerCurrent"
                          type="number"
                          min="0"
                          max="10000"
                          value={data.auxiliaryParams.current.lowerCurrent}
                          onChange={(e) => handleAuxiliaryParamChange('current', 'lowerCurrent', parseFloat(e.target.value) || 0)}
                        />
                      </div>
                    </div>
                  </div>

                  <Separator />

                  {/* 电流超差保护 */}
                  <div className="space-y-3">
                    <h4 className="font-medium">电流超差保护</h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="current-currentValue">超差阈值 (mA)</Label>
                        <Input
                          id="current-currentValue"
                          type="number"
                          min="0"
                          max="10000"
                          value={data.auxiliaryParams.current.currentValue}
                          onChange={(e) => handleAuxiliaryParamChange('current', 'currentValue', parseFloat(e.target.value) || 0)}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="current-checkInterval">检查间隔 (s)</Label>
                        <Input
                          id="current-checkInterval"
                          type="number"
                          min="0"
                          max="60"
                          value={data.auxiliaryParams.current.checkInterval}
                          onChange={(e) => handleAuxiliaryParamChange('current', 'checkInterval', parseInt(e.target.value) || 0)}
                        />
                      </div>
                    </div>
                  </div>

                  <Separator />

                  {/* 电流突降保护 */}
                  <div className="space-y-3">
                    <h4 className="font-medium">电流突降保护</h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="current-dropValue">突降阈值 (mA)</Label>
                        <Input
                          id="current-dropValue"
                          type="number"
                          min="0"
                          max="10000"
                          value={data.auxiliaryParams.current.dropValue}
                          onChange={(e) => handleAuxiliaryParamChange('current', 'dropValue', parseFloat(e.target.value) || 0)}
                        />
                      </div>
                    </div>
                  </div>
                </>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  <p>电流保护参数已禁用</p>
                  <p className="text-sm">启用开关以配置电流保护参数</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* 容量参数组 */}
        <TabsContent value="capacity" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="text-lg">容量限制参数</CardTitle>
                  <CardDescription>配置容量限制相关参数</CardDescription>
                </div>
                <div className="flex items-center space-x-2">
                  <Label htmlFor="capacity-enabled">启用</Label>
                  <Switch
                    id="capacity-enabled"
                    checked={data.auxiliaryParams.capacity.enabled}
                    onCheckedChange={(checked) => handleAuxiliaryParamChange('capacity', 'enabled', checked)}
                  />
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              {data.auxiliaryParams.capacity.enabled ? (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <h4 className="font-medium">恒流容量上限</h4>
                      <Switch
                        checked={data.auxiliaryParams.capacity.ccCapacityLimit.enabled}
                        onCheckedChange={(checked) => handleNestedParamChange('capacity', 'ccCapacityLimit', 'enabled', checked)}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="capacity-ccLimit">容量限制 (mAh)</Label>
                      <Input
                        id="capacity-ccLimit"
                        type="number"
                        min="0"
                        max="50000"
                        value={data.auxiliaryParams.capacity.ccCapacityLimit.value}
                        onChange={(e) => handleNestedParamChange('capacity', 'ccCapacityLimit', 'value', parseFloat(e.target.value) || 0)}
                        disabled={!data.auxiliaryParams.capacity.ccCapacityLimit.enabled}
                      />
                    </div>
                    <Badge variant={data.auxiliaryParams.capacity.ccCapacityLimit.enabled ? "default" : "secondary"}>
                      {data.auxiliaryParams.capacity.ccCapacityLimit.enabled ? "已启用" : "已禁用"}
                    </Badge>
                  </div>

                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <h4 className="font-medium">恒压容量上限</h4>
                      <Switch
                        checked={data.auxiliaryParams.capacity.cvCapacityLimit.enabled}
                        onCheckedChange={(checked) => handleNestedParamChange('capacity', 'cvCapacityLimit', 'enabled', checked)}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="capacity-cvLimit">容量限制 (mAh)</Label>
                      <Input
                        id="capacity-cvLimit"
                        type="number"
                        min="0"
                        max="50000"
                        value={data.auxiliaryParams.capacity.cvCapacityLimit.value}
                        onChange={(e) => handleNestedParamChange('capacity', 'cvCapacityLimit', 'value', parseFloat(e.target.value) || 0)}
                        disabled={!data.auxiliaryParams.capacity.cvCapacityLimit.enabled}
                      />
                    </div>
                    <Badge variant={data.auxiliaryParams.capacity.cvCapacityLimit.enabled ? "default" : "secondary"}>
                      {data.auxiliaryParams.capacity.cvCapacityLimit.enabled ? "已启用" : "已禁用"}
                    </Badge>
                  </div>
                </div>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  <p>容量限制参数已禁用</p>
                  <p className="text-sm">启用开关以配置容量限制参数</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* 采样间隔参数组 */}
        <TabsContent value="sampling" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="text-lg">采样间隔参数</CardTitle>
                  <CardDescription>配置数据采集间隔参数</CardDescription>
                </div>
                <div className="flex items-center space-x-2">
                  <Label htmlFor="sampling-enabled">启用</Label>
                  <Switch
                    id="sampling-enabled"
                    checked={data.auxiliaryParams.samplingInterval.enabled}
                    onCheckedChange={(checked) => handleAuxiliaryParamChange('samplingInterval', 'enabled', checked)}
                  />
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              {data.auxiliaryParams.samplingInterval.enabled ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="sampling-voltageDiff">电压差阈值 (mV)</Label>
                    <Input
                      id="sampling-voltageDiff"
                      type="number"
                      min="0"
                      max="10000"
                      value={data.auxiliaryParams.samplingInterval.voltageDiff}
                      onChange={(e) => handleAuxiliaryParamChange('samplingInterval', 'voltageDiff', parseFloat(e.target.value) || 0)}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="sampling-currentDiff">电流差阈值 (mA)</Label>
                    <Input
                      id="sampling-currentDiff"
                      type="number"
                      min="0"
                      max="100000"
                      value={data.auxiliaryParams.samplingInterval.currentDiff}
                      onChange={(e) => handleAuxiliaryParamChange('samplingInterval', 'currentDiff', parseFloat(e.target.value) || 0)}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="sampling-temperatureDiff">温度差阈值 (℃)</Label>
                    <Input
                      id="sampling-temperatureDiff"
                      type="number"
                      min="0"
                      max="100"
                      value={data.auxiliaryParams.samplingInterval.temperatureDiff}
                      onChange={(e) => handleAuxiliaryParamChange('samplingInterval', 'temperatureDiff', parseFloat(e.target.value) || 0)}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="sampling-capacityDiff">容量差阈值 (mAh)</Label>
                    <Input
                      id="sampling-capacityDiff"
                      type="number"
                      min="0"
                      max="100000"
                      value={data.auxiliaryParams.samplingInterval.capacityDiff}
                      onChange={(e) => handleAuxiliaryParamChange('samplingInterval', 'capacityDiff', parseFloat(e.target.value) || 0)}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="sampling-powerDiff">功率差阈值 (mW)</Label>
                    <Input
                      id="sampling-powerDiff"
                      type="number"
                      min="0"
                      max="100000"
                      value={data.auxiliaryParams.samplingInterval.powerDiff}
                      onChange={(e) => handleAuxiliaryParamChange('samplingInterval', 'powerDiff', parseFloat(e.target.value) || 0)}
                    />
                  </div>
                </div>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  <p>采样间隔参数已禁用</p>
                  <p className="text-sm">启用开关以配置采样间隔参数</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* 工步保护参数组 */}
        <TabsContent value="protection" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="text-lg">工步保护参数</CardTitle>
                  <CardDescription>配置工步级保护参数</CardDescription>
                </div>
                <div className="flex items-center space-x-2">
                  <Label htmlFor="protection-enabled">启用</Label>
                  <Switch
                    id="protection-enabled"
                    checked={data.auxiliaryParams.stepProtection.enabled}
                    onCheckedChange={(checked) => handleAuxiliaryParamChange('stepProtection', 'enabled', checked)}
                  />
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              {data.auxiliaryParams.stepProtection.enabled ? (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="protection-impedanceUpper">阻抗上限 (mΩ)</Label>
                    <Input
                      id="protection-impedanceUpper"
                      type="number"
                      min="0"
                      max="10000"
                      value={data.auxiliaryParams.stepProtection.impedanceUpperLimit}
                      onChange={(e) => handleAuxiliaryParamChange('stepProtection', 'impedanceUpperLimit', parseFloat(e.target.value) || 0)}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="protection-impedanceLower">阻抗下限 (mΩ)</Label>
                    <Input
                      id="protection-impedanceLower"
                      type="number"
                      min="0"
                      max="10000"
                      value={data.auxiliaryParams.stepProtection.impedanceLowerLimit}
                      onChange={(e) => handleAuxiliaryParamChange('stepProtection', 'impedanceLowerLimit', parseFloat(e.target.value) || 0)}
                    />
                  </div>
                </div>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  <p>工步保护参数已禁用</p>
                  <p className="text-sm">启用开关以配置工步保护参数</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}

'use client';

import React, { useState, useMemo } from 'react';
import { Search, Filter, Grid, List, SortAsc, SortDesc } from 'lucide-react';

import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

import { ChannelCard } from './ChannelCard';
import { Channel, ChannelStatus, ChannelType, ChannelSelectorConfig } from '@/lib/process/types/channel';

interface ChannelGridProps {
  channels: Channel[];
  selectedChannelIds: string[];
  onChannelSelect: (channel: Channel) => void;
  onChannelToggle: (channel: Channel) => void;
  config: ChannelSelectorConfig;
  className?: string;
}

type SortField = 'channelNumber' | 'name' | 'status' | 'deviceId';
type SortOrder = 'asc' | 'desc';
type ViewMode = 'grid' | 'list';

export function ChannelGrid({
  channels,
  selectedChannelIds,
  onChannelSelect,
  onChannelToggle,
  config,
  className
}: ChannelGridProps) {
  
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string[]>([]);
  const [typeFilter, setTypeFilter] = useState<string[]>([]);
  const [deviceFilter, setDeviceFilter] = useState<string[]>([]);
  const [selectedDevice, setSelectedDevice] = useState<string>('all'); // 当前选择的设备
  const [sortField, setSortField] = useState<SortField>('channelNumber');
  const [sortOrder, setSortOrder] = useState<SortOrder>('asc');
  const [viewMode, setViewMode] = useState<ViewMode>('grid');

  // 获取所有设备ID
  const deviceIds = useMemo(() => {
    const ids = Array.from(new Set(channels.map(c => c.deviceId)));
    return ids.sort();
  }, [channels]);

  // 筛选和排序后的通道
  const filteredAndSortedChannels = useMemo(() => {
    let filtered = channels;

    // 搜索筛选
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(channel => 
        channel.name.toLowerCase().includes(term) ||
        channel.channelNumber.toString().includes(term) ||
        channel.deviceId.toLowerCase().includes(term) ||
        channel.batteryInfo?.name.toLowerCase().includes(term)
      );
    }

    // 状态筛选
    if (statusFilter.length > 0) {
      filtered = filtered.filter(channel => statusFilter.includes(channel.status));
    }

    // 类型筛选
    if (typeFilter.length > 0) {
      filtered = filtered.filter(channel => typeFilter.includes(channel.type));
    }

    // 设备筛选
    if (deviceFilter.length > 0) {
      filtered = filtered.filter(channel => deviceFilter.includes(channel.deviceId));
    }

    // 优先显示选择的设备
    if (selectedDevice && selectedDevice !== 'all') {
      filtered = filtered.filter(channel => channel.deviceId === selectedDevice);
    }

    // 配置筛选
    if (config.filterByStatus.length > 0) {
      filtered = filtered.filter(channel => config.filterByStatus.includes(channel.status));
    }

    if (config.filterByType.length > 0) {
      filtered = filtered.filter(channel => config.filterByType.includes(channel.type));
    }

    // 排序
    filtered.sort((a, b) => {
      let aValue: any, bValue: any;

      switch (sortField) {
        case 'channelNumber':
          aValue = a.channelNumber;
          bValue = b.channelNumber;
          break;
        case 'name':
          aValue = a.name.toLowerCase();
          bValue = b.name.toLowerCase();
          break;
        case 'status':
          aValue = a.status;
          bValue = b.status;
          break;
        case 'deviceId':
          aValue = a.deviceId;
          bValue = b.deviceId;
          break;
        default:
          aValue = a.channelNumber;
          bValue = b.channelNumber;
      }

      if (aValue < bValue) return sortOrder === 'asc' ? -1 : 1;
      if (aValue > bValue) return sortOrder === 'asc' ? 1 : -1;
      return 0;
    });

    return filtered;
  }, [channels, searchTerm, statusFilter, typeFilter, deviceFilter, sortField, sortOrder, config]);

  // 按设备分组
  const groupedChannels = useMemo(() => {
    if (!config.groupByDevice) {
      return { '': filteredAndSortedChannels };
    }

    return filteredAndSortedChannels.reduce((groups, channel) => {
      const deviceId = channel.deviceId;
      if (!groups[deviceId]) {
        groups[deviceId] = [];
      }
      groups[deviceId].push(channel);
      return groups;
    }, {} as Record<string, Channel[]>);
  }, [filteredAndSortedChannels, config.groupByDevice]);

  const handleStatusFilterChange = (status: any, checked: boolean) => {
    if (checked) {
      setStatusFilter(prev => [...prev, status]);
    } else {
      setStatusFilter(prev => prev.filter(s => s !== status));
    }
  };

  const handleTypeFilterChange = (type: any, checked: boolean) => {
    if (checked) {
      setTypeFilter(prev => [...prev, type]);
    } else {
      setTypeFilter(prev => prev.filter(t => t !== type));
    }
  };

  const handleDeviceFilterChange = (deviceId: string, checked: boolean) => {
    if (checked) {
      setDeviceFilter(prev => [...prev, deviceId]);
    } else {
      setDeviceFilter(prev => prev.filter(d => d !== deviceId));
    }
  };

  const clearFilters = () => {
    setSearchTerm('');
    setStatusFilter([]);
    setTypeFilter([]);
    setDeviceFilter([]);
  };

  const hasActiveFilters = searchTerm || statusFilter.length > 0 || typeFilter.length > 0 || deviceFilter.length > 0;
  const activeFilterCount = (searchTerm ? 1 : 0) + (statusFilter.length > 0 ? 1 : 0) + (typeFilter.length > 0 ? 1 : 0) + (deviceFilter.length > 0 ? 1 : 0);

  const getGridCols = () => {
    if (config.compactMode) {
      return 'grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 xl:grid-cols-8 auto-rows-fr';
    }
    return 'grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 auto-rows-fr';
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {/* 工具栏 */}
      <div className="flex items-center justify-between space-x-4">
        {/* 设备选择和搜索 */}
        <div className="flex items-center space-x-2 flex-1 max-w-md">
          {/* 设备选择器 */}
          <div className="flex items-center space-x-2">
            <Label className="text-sm font-medium whitespace-nowrap">设备:</Label>
            <Select value={selectedDevice} onValueChange={setSelectedDevice}>
              <SelectTrigger className="w-32">
                <SelectValue placeholder="全部" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">全部设备</SelectItem>
                {deviceIds.map((deviceId) => (
                  <SelectItem key={deviceId} value={deviceId}>
                    {deviceId}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* 搜索框 */}
          <div className="relative flex-1">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="搜索通道..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-8"
            />
          </div>
        </div>

        {/* 工具按钮 */}
        <div className="flex items-center space-x-2">
          {/* 活跃筛选器指示 */}
          {hasActiveFilters && (
            <Badge variant="secondary" className="text-xs">
              {activeFilterCount} 个筛选器
            </Badge>
          )}

          {/* 筛选器 */}
          <Popover>
            <PopoverTrigger asChild>
              <Button variant="outline" size="sm">
                <Filter className="h-4 w-4 mr-1" />
                筛选
                {hasActiveFilters && (
                  <Badge variant="destructive" className="ml-2 h-4 w-4 rounded-full p-0 text-xs">
                    {activeFilterCount}
                  </Badge>
                )}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-80" align="end">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h4 className="font-medium">筛选条件</h4>
                  {hasActiveFilters && (
                    <Button variant="ghost" size="sm" onClick={clearFilters}>
                      清除
                    </Button>
                  )}
                </div>

                <Separator />

                {/* 状态筛选 */}
                <div className="space-y-2">
                  <Label className="text-sm font-medium">通道状态</Label>
                  <div className="space-y-1">
                    {['idle', 'running', 'paused', 'error', 'offline', 'maintenance'].map((status) => (
                      <div key={status} className="flex items-center space-x-2">
                        <Checkbox
                          id={`status-${status}`}
                          checked={statusFilter.includes(status)}
                          onCheckedChange={(checked) =>
                            handleStatusFilterChange(status as any, checked as boolean)
                          }
                        />
                        <Label htmlFor={`status-${status}`} className="text-sm">
                          {status}
                        </Label>
                      </div>
                    ))}
                  </div>
                </div>

                <Separator />

                {/* 类型筛选 */}
                <div className="space-y-2">
                  <Label className="text-sm font-medium">通道类型</Label>
                  <div className="space-y-1">
                    {['charge', 'discharge', 'hybrid'].map((type) => (
                      <div key={type} className="flex items-center space-x-2">
                        <Checkbox
                          id={`type-${type}`}
                          checked={typeFilter.includes(type)}
                          onCheckedChange={(checked) =>
                            handleTypeFilterChange(type as any, checked as boolean)
                          }
                        />
                        <Label htmlFor={`type-${type}`} className="text-sm">
                          {type}
                        </Label>
                      </div>
                    ))}
                  </div>
                </div>

                {/* 设备筛选 */}
                {deviceIds.length > 1 && (
                  <>
                    <Separator />
                    <div className="space-y-2">
                      <Label className="text-sm font-medium">设备</Label>
                      <div className="space-y-1 max-h-32 overflow-y-auto">
                        {deviceIds.map((deviceId) => (
                          <div key={deviceId} className="flex items-center space-x-2">
                            <Checkbox
                              id={`device-${deviceId}`}
                              checked={deviceFilter.includes(deviceId)}
                              onCheckedChange={(checked) => 
                                handleDeviceFilterChange(deviceId, checked as boolean)
                              }
                            />
                            <Label htmlFor={`device-${deviceId}`} className="text-sm">
                              {deviceId}
                            </Label>
                          </div>
                        ))}
                      </div>
                    </div>
                  </>
                )}
              </div>
            </PopoverContent>
          </Popover>

          {/* 排序 */}
          <Select value={`${sortField}-${sortOrder}`} onValueChange={(value) => {
            const [field, order] = value.split('-') as [SortField, SortOrder];
            setSortField(field);
            setSortOrder(order);
          }}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="channelNumber-asc">通道号 ↑</SelectItem>
              <SelectItem value="channelNumber-desc">通道号 ↓</SelectItem>
              <SelectItem value="name-asc">名称 ↑</SelectItem>
              <SelectItem value="name-desc">名称 ↓</SelectItem>
              <SelectItem value="status-asc">状态 ↑</SelectItem>
              <SelectItem value="status-desc">状态 ↓</SelectItem>
              <SelectItem value="deviceId-asc">设备 ↑</SelectItem>
              <SelectItem value="deviceId-desc">设备 ↓</SelectItem>
            </SelectContent>
          </Select>

          {/* 视图模式 */}
          <div className="flex items-center border rounded-md">
            <Button
              variant={viewMode === 'grid' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('grid')}
              className="rounded-r-none"
            >
              <Grid className="h-4 w-4" />
            </Button>
            <Button
              variant={viewMode === 'list' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('list')}
              className="rounded-l-none"
            >
              <List className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* 结果统计 */}
      <div className="flex items-center justify-between text-sm text-muted-foreground">
        <div>
          显示 {filteredAndSortedChannels.length} / {channels.length} 个通道
          {selectedChannelIds.length > 0 && (
            <span className="ml-2">
              (已选择 {selectedChannelIds.length} 个)
            </span>
          )}
        </div>
        {config.maxSelection && (
          <div>
            最多可选择 {config.maxSelection} 个通道
          </div>
        )}
      </div>

      {/* 通道网格/列表 */}
      <div className="space-y-6">
        {Object.entries(groupedChannels).map(([deviceId, deviceChannels]) => (
          <div key={deviceId || 'default'}>
            {/* 设备分组标题 */}
            {config.groupByDevice && deviceId && (
              <Card className="mb-6">
                <CardHeader className="pb-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <CardTitle className="text-lg">{deviceId}</CardTitle>
                      <CardDescription className="mt-1">
                        {deviceChannels.length} 个通道
                      </CardDescription>
                    </div>
                    <Badge variant="secondary" className="text-sm">
                      {deviceChannels.filter(ch => ch.status !== 'offline').length} 在线
                    </Badge>
                  </div>
                </CardHeader>
              </Card>
            )}

            {/* 通道网格 */}
            <div className={
              viewMode === 'grid'
                ? `grid gap-6 ${getGridCols()}`
                : 'space-y-3'
            }>
              {deviceChannels.map((channel) => (
                <ChannelCard
                  key={channel.id}
                  channel={channel}
                  isSelected={selectedChannelIds.includes(channel.id)}
                  onSelect={onChannelSelect}
                  onToggleSelect={onChannelToggle}
                  showBatteryInfo={config.showBatteryInfo}
                  showRealTimeData={config.showRealTimeData}
                  showStatistics={config.showStatistics}
                  compactMode={config.compactMode}
                  multiSelect={config.multiSelect}
                />
              ))}
            </div>
          </div>
        ))}

        {/* 空状态 */}
        {filteredAndSortedChannels.length === 0 && (
          <Card>
            <CardContent className="flex items-center justify-center py-12">
              <div className="text-center">
                <div className="text-muted-foreground mb-2">
                  {hasActiveFilters ? '没有符合条件的通道' : '暂无通道数据'}
                </div>
                {hasActiveFilters && (
                  <Button variant="outline" size="sm" onClick={clearFilters}>
                    清除筛选条件
                  </Button>
                )}
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}

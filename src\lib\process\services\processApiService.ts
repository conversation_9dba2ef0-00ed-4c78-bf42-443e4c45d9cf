/**
 * 工艺流程API服务 - 客户端使用
 */

import {
  Process,
  ProcessInfo,
  CreateProcessData,
  UpdateProcessData,
  ProcessQueryParams,
  ProcessStep,
} from '../types/process';
import { Step } from '../types/step';

interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export class ProcessApiService {
  private baseUrl = '/api/processes';

  /**
   * 获取工艺流程列表
   */
  async getProcesses(params?: ProcessQueryParams): Promise<ProcessInfo[]> {
    // 临时返回空数组，避免数据库错误
    console.warn('ProcessApiService: 数据库功能暂时禁用，返回空数据');
    return [];

    /* 原始实现 - 暂时注释
    const searchParams = new URLSearchParams();

    if (params) {
      if (params.status) searchParams.set('status', params.status);
      if (params.creatorId) searchParams.set('creatorId', params.creatorId);
      if (params.sortBy) searchParams.set('sortBy', params.sortBy);
      if (params.sortOrder) searchParams.set('sortOrder', params.sortOrder);
      if (params.limit) searchParams.set('limit', params.limit.toString());
      if (params.offset) searchParams.set('offset', params.offset.toString());
    }

    const url = `${this.baseUrl}?${searchParams.toString()}`;
    const response = await fetch(url);

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result: ApiResponse<ProcessInfo[]> = await response.json();

    if (!result.success) {
      throw new Error(result.error || '获取工艺流程列表失败');
    }

    return result.data || [];
    */
  }

  /**
   * 获取单个工艺流程
   */
  async getProcess(id: string): Promise<Process | null> {
    // 临时返回null，避免数据库错误
    console.warn('ProcessApiService: 数据库功能暂时禁用，返回null');
    return null;

    /* 原始实现 - 暂时注释
    const response = await fetch(`${this.baseUrl}/${id}`);

    if (response.status === 404) {
      return null;
    }

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result: ApiResponse<Process> = await response.json();

    if (!result.success) {
      throw new Error(result.error || '获取工艺流程失败');
    }

    return result.data || null;
    */
  }

  /**
   * 创建工艺流程
   */
  async createProcess(data: CreateProcessData): Promise<Process> {
    const response = await fetch(this.baseUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result: ApiResponse<Process> = await response.json();
    
    if (!result.success) {
      throw new Error(result.error || '创建工艺流程失败');
    }

    return result.data!;
  }

  /**
   * 更新工艺流程
   */
  async updateProcess(id: string, data: UpdateProcessData): Promise<Process> {
    const response = await fetch(`${this.baseUrl}/${id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result: ApiResponse<Process> = await response.json();
    
    if (!result.success) {
      throw new Error(result.error || '更新工艺流程失败');
    }

    return result.data!;
  }

  /**
   * 删除工艺流程
   */
  async deleteProcess(id: string): Promise<void> {
    const response = await fetch(`${this.baseUrl}/${id}`, {
      method: 'DELETE',
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result: ApiResponse<void> = await response.json();

    if (!result.success) {
      throw new Error(result.error || '删除工艺流程失败');
    }
  }

  /**
   * 向工艺流程添加工步
   */
  async addStepToProcess(processId: string, step: Step): Promise<ProcessStep> {
    const response = await fetch(`${this.baseUrl}/${processId}/steps`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(step),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result: ApiResponse<ProcessStep> = await response.json();

    if (!result.success) {
      throw new Error(result.error || '添加工步到工艺流程失败');
    }

    return result.data!;
  }

  /**
   * 更新工艺流程中的工步
   */
  async updateStepInProcess(processId: string, stepId: string, step: Step): Promise<boolean> {
    const response = await fetch(`${this.baseUrl}/${processId}/steps/${stepId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(step),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result: ApiResponse<boolean> = await response.json();

    if (!result.success) {
      throw new Error(result.error || '更新工艺流程中的工步失败');
    }

    return result.data || false;
  }

  /**
   * 从工艺流程中删除工步
   */
  async removeStepFromProcess(processId: string, stepId: string): Promise<boolean> {
    const response = await fetch(`${this.baseUrl}/${processId}/steps/${stepId}`, {
      method: 'DELETE',
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result: ApiResponse<boolean> = await response.json();

    if (!result.success) {
      throw new Error(result.error || '从工艺流程中删除工步失败');
    }

    return result.data || false;
  }
}

# 🚀 朗天PDCS快速部署指南

## 📦 生产环境打包完成

您的朗天PDCS系统已经配置好生产环境部署文件，包含以下文件：

### 📁 部署文件清单
- `Dockerfile` - Docker容器配置
- `docker-compose.yml` - Docker编排配置
- `nginx.conf` - Nginx反向代理配置
- `deploy.sh` - Linux/macOS部署脚本
- `deploy.bat` - Windows部署脚本
- `ecosystem.config.js` - PM2进程管理配置
- `.env.production` - 生产环境变量
- `healthcheck.js` - 健康检查脚本
- `DEPLOYMENT.md` - 详细部署文档

## ⚡ 快速部署步骤

### 方法一：Docker部署（推荐）

```bash
# 1. 确保安装了Docker和Docker Compose
docker --version
docker-compose --version

# 2. 克隆项目到服务器
git clone <your-repo> tianlang-pdcs
cd tianlang-pdcs

# 3. 配置环境变量
cp .env.production .env.local
# 编辑 .env.local 修改相关配置

# 4. 一键部署
chmod +x deploy.sh
./deploy.sh

# 5. 访问应用
# http://your-server-ip
```

### 方法二：传统部署

```bash
# 1. 安装Node.js 18+
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# 2. 安装PM2
npm install -g pm2

# 3. 构建和启动
npm install
npm run build:production
pm2 start ecosystem.config.js --env production

# 4. 设置开机自启
pm2 startup
pm2 save
```

### 方法三：Windows部署

```cmd
# 1. 确保安装了Node.js 18+
node --version

# 2. 运行部署脚本
deploy.bat

# 3. 访问应用
# http://localhost:3000
```

## 🔧 配置要点

### 1. 环境变量配置
编辑 `.env.local` 文件：
```env
NEXT_PUBLIC_API_URL=http://your-server-ip:3001/api
NEXTAUTH_URL=http://your-domain.com
NEXTAUTH_SECRET=your-secure-secret-key
```

### 2. 端口配置
- 默认端口：3000
- Nginx代理：80/443
- 可在 `docker-compose.yml` 中修改

### 3. 域名配置
- 修改 `nginx.conf` 中的 `server_name`
- 配置DNS解析
- 申请SSL证书（可选）

## 📊 部署后检查

### 1. 服务状态检查
```bash
# Docker方式
docker-compose ps
docker-compose logs -f

# PM2方式
pm2 status
pm2 logs tianlang-pdcs

# 健康检查
node healthcheck.js
```

### 2. 功能测试
- [ ] 页面正常加载
- [ ] 设备监控功能
- [ ] 工艺流程管理
- [ ] 数据分析图表
- [ ] 电池分选功能
- [ ] 多语言切换

### 3. 性能检查
```bash
# 内存使用
free -h

# CPU使用
top

# 磁盘空间
df -h

# 网络连接
netstat -tulpn | grep :3000
```

## 🛡️ 安全建议

1. **防火墙配置**
```bash
sudo ufw allow 22    # SSH
sudo ufw allow 80    # HTTP
sudo ufw allow 443   # HTTPS
sudo ufw enable
```

2. **SSL证书**
```bash
# 使用Let's Encrypt
sudo certbot --nginx -d your-domain.com
```

3. **定期备份**
```bash
# 备份数据
docker-compose exec app tar -czf backup.tar.gz /app/data
```

## 🔄 更新部署

```bash
# 拉取最新代码
git pull

# Docker方式更新
docker-compose down
docker-compose build --no-cache
docker-compose up -d

# PM2方式更新
npm install
npm run build:production
pm2 restart tianlang-pdcs
```

## 📞 技术支持

如遇问题，请检查：
1. 日志文件：`docker-compose logs` 或 `pm2 logs`
2. 端口占用：`netstat -tulpn | grep :3000`
3. 系统资源：`htop` 或 `top`
4. 网络连接：`curl http://localhost:3000`

---

## 🎉 部署完成！

访问 **http://your-server-ip** 开始使用朗天PDCS系统！

**默认功能模块：**
- 🖥️ 设备监控
- ⚙️ 工艺流程管理  
- 📊 数据分析
- 🔋 电池分选
- 📱 多语言支持

**系统特点：**
- 响应式设计，支持各种设备
- 实时数据监控
- 工业级稳定性
- 现代化用户界面

import { Device, Channel, Alarm, ApiResponse, PaginatedResponse } from '@/lib/types';

// API 基础配置
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8080/api';

// 通用请求函数
async function apiRequest<T>(
  endpoint: string,
  options: RequestInit = {}
): Promise<ApiResponse<T>> {
  try {
    const response = await fetch(`${API_BASE_URL}${endpoint}`, {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return {
      success: true,
      data,
    };
  } catch (error) {
    console.error('API request failed:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

// 设备相关API
export const deviceApi = {
  // 获取所有设备
  async getDevices(): Promise<ApiResponse<Device[]>> {
    return apiRequest<Device[]>('/devices');
  },

  // 获取单个设备
  async getDevice(deviceName: string): Promise<ApiResponse<Device>> {
    return apiRequest<Device>(`/devices/${encodeURIComponent(deviceName)}`);
  },

  // 更新设备状态
  async updateDeviceStatus(deviceName: string, status: string): Promise<ApiResponse<Device>> {
    return apiRequest<Device>(`/devices/${encodeURIComponent(deviceName)}/status`, {
      method: 'PUT',
      body: JSON.stringify({ status }),
    });
  },

  // 获取设备的实时数据
  async getDeviceRealTimeData(deviceName: string): Promise<ApiResponse<Device>> {
    return apiRequest<Device>(`/devices/${encodeURIComponent(deviceName)}/realtime`);
  },
};

// 通道相关API
export const channelApi = {
  // 获取所有通道
  async getChannels(): Promise<ApiResponse<Channel[]>> {
    return apiRequest<Channel[]>('/channels');
  },

  // 获取设备的通道
  async getChannelsByDevice(deviceName: string): Promise<ApiResponse<Channel[]>> {
    return apiRequest<Channel[]>(`/devices/${encodeURIComponent(deviceName)}/channels`);
  },

  // 获取单个通道
  async getChannel(deviceName: string, channelNumber: number): Promise<ApiResponse<Channel>> {
    return apiRequest<Channel>(`/devices/${encodeURIComponent(deviceName)}/channels/${channelNumber}`);
  },

  // 更新通道配置
  async updateChannel(deviceName: string, channelNumber: number, data: Partial<Channel>): Promise<ApiResponse<Channel>> {
    return apiRequest<Channel>(`/devices/${encodeURIComponent(deviceName)}/channels/${channelNumber}`, {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  },

  // 启用/禁用通道
  async toggleChannel(deviceName: string, channelNumber: number, enabled: boolean): Promise<ApiResponse<Channel>> {
    return apiRequest<Channel>(`/devices/${encodeURIComponent(deviceName)}/channels/${channelNumber}/toggle`, {
      method: 'POST',
      body: JSON.stringify({ enabled }),
    });
  },
};

// 报警相关API
export const alarmApi = {
  // 获取所有报警
  async getAlarms(page = 1, pageSize = 50): Promise<ApiResponse<PaginatedResponse<Alarm>>> {
    return apiRequest<PaginatedResponse<Alarm>>(`/alarms?page=${page}&pageSize=${pageSize}`);
  },

  // 获取活跃报警
  async getActiveAlarms(): Promise<ApiResponse<Alarm[]>> {
    return apiRequest<Alarm[]>('/alarms/active');
  },

  // 获取设备的报警
  async getAlarmsByDevice(deviceName: string): Promise<ApiResponse<Alarm[]>> {
    return apiRequest<Alarm[]>(`/devices/${encodeURIComponent(deviceName)}/alarms`);
  },

  // 确认报警
  async acknowledgeAlarm(alarmId: string, acknowledgedBy: string): Promise<ApiResponse<Alarm>> {
    return apiRequest<Alarm>(`/alarms/${alarmId}/acknowledge`, {
      method: 'POST',
      body: JSON.stringify({ acknowledgedBy }),
    });
  },

  // 清除报警
  async clearAlarm(alarmId: string): Promise<ApiResponse<void>> {
    return apiRequest<void>(`/alarms/${alarmId}`, {
      method: 'DELETE',
    });
  },
};

// 系统相关API
export const systemApi = {
  // 获取系统状态
  async getSystemStatus(): Promise<ApiResponse<any>> {
    return apiRequest<any>('/system/status');
  },

  // 获取系统统计
  async getSystemStatistics(): Promise<ApiResponse<any>> {
    return apiRequest<any>('/system/statistics');
  },

  // 系统健康检查
  async healthCheck(): Promise<ApiResponse<{ status: string; timestamp: string }>> {
    return apiRequest<{ status: string; timestamp: string }>('/system/health');
  },
};

// WebSocket 连接管理
export class WebSocketManager {
  private ws: WebSocket | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectInterval = 5000;
  private listeners: Map<string, Set<(data: any) => void>> = new Map();

  constructor(private url: string = 'ws://localhost:8080/ws') {}

  connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        this.ws = new WebSocket(this.url);

        this.ws.onopen = () => {
          console.log('WebSocket connected');
          this.reconnectAttempts = 0;
          resolve();
        };

        this.ws.onmessage = (event) => {
          try {
            const data = JSON.parse(event.data);
            this.handleMessage(data);
          } catch (error) {
            console.error('Failed to parse WebSocket message:', error);
          }
        };

        this.ws.onclose = () => {
          console.log('WebSocket disconnected');
          this.attemptReconnect();
        };

        this.ws.onerror = (error) => {
          console.error('WebSocket error:', error);
          reject(error);
        };
      } catch (error) {
        reject(error);
      }
    });
  }

  private handleMessage(data: any) {
    const { type, payload } = data;
    const listeners = this.listeners.get(type);
    if (listeners) {
      listeners.forEach(listener => listener(payload));
    }
  }

  private attemptReconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      console.log(`Attempting to reconnect... (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
      
      setTimeout(() => {
        this.connect().catch(console.error);
      }, this.reconnectInterval);
    }
  }

  subscribe(type: string, listener: (data: any) => void) {
    if (!this.listeners.has(type)) {
      this.listeners.set(type, new Set());
    }
    this.listeners.get(type)!.add(listener);
  }

  unsubscribe(type: string, listener: (data: any) => void) {
    const listeners = this.listeners.get(type);
    if (listeners) {
      listeners.delete(listener);
    }
  }

  send(type: string, payload: any) {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify({ type, payload }));
    }
  }

  disconnect() {
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
  }
}

// 导出WebSocket实例
export const wsManager = new WebSocketManager();

'use client';

import React from 'react';
import { cn } from '@/lib/utils';

interface BatteryIconProps {
  level: number; // 电量百分比 0-100
  charging?: boolean; // 是否正在充电
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  showPercentage?: boolean;
  variant?: 'default' | 'outline' | 'minimal';
}

export function BatteryIcon({ 
  level, 
  charging = false, 
  size = 'md', 
  className,
  showPercentage = false,
  variant = 'default'
}: BatteryIconProps) {
  
  // 确保电量在0-100范围内
  const normalizedLevel = Math.max(0, Math.min(100, level));
  
  // 根据电量确定颜色
  const getBatteryColor = () => {
    if (charging) return 'text-blue-500';
    if (normalizedLevel > 60) return 'text-green-500';
    if (normalizedLevel > 30) return 'text-yellow-500';
    if (normalizedLevel > 15) return 'text-orange-500';
    return 'text-red-500';
  };

  // 根据电量确定填充颜色
  const getFillColor = () => {
    if (charging) return '#3b82f6'; // blue-500
    if (normalizedLevel > 60) return '#22c55e'; // green-500
    if (normalizedLevel > 30) return '#eab308'; // yellow-500
    if (normalizedLevel > 15) return '#f97316'; // orange-500
    return '#ef4444'; // red-500
  };

  // 尺寸配置
  const sizeConfig = {
    sm: { width: 20, height: 12, strokeWidth: 1.5 },
    md: { width: 24, height: 14, strokeWidth: 2 },
    lg: { width: 32, height: 18, strokeWidth: 2.5 }
  };

  const { width, height, strokeWidth } = sizeConfig[size];
  
  // 电池主体宽度和高度
  const bodyWidth = width * 0.8;
  const bodyHeight = height * 0.7;
  const bodyX = (width - bodyWidth) / 2;
  const bodyY = (height - bodyHeight) / 2;
  
  // 电池正极宽度和高度
  const terminalWidth = width * 0.1;
  const terminalHeight = height * 0.3;
  const terminalX = bodyX + bodyWidth;
  const terminalY = (height - terminalHeight) / 2;
  
  // 电量填充宽度
  const fillWidth = (bodyWidth - strokeWidth * 2) * (normalizedLevel / 100);
  const fillX = bodyX + strokeWidth;
  const fillY = bodyY + strokeWidth;
  const fillHeight = bodyHeight - strokeWidth * 2;

  return (
    <div className={cn('inline-flex items-center space-x-1', className)}>
      <div className={cn('relative', getBatteryColor())}>
        <svg
          width={width}
          height={height}
          viewBox={`0 0 ${width} ${height}`}
          className="drop-shadow-sm"
        >
          {/* 电池主体外框 */}
          <rect
            x={bodyX}
            y={bodyY}
            width={bodyWidth}
            height={bodyHeight}
            rx={2}
            ry={2}
            fill="none"
            stroke="currentColor"
            strokeWidth={strokeWidth}
            className={variant === 'outline' ? 'opacity-60' : ''}
          />
          
          {/* 电池正极 */}
          <rect
            x={terminalX}
            y={terminalY}
            width={terminalWidth}
            height={terminalHeight}
            rx={1}
            ry={1}
            fill="currentColor"
            className={variant === 'outline' ? 'opacity-60' : ''}
          />
          
          {/* 电量填充 */}
          {normalizedLevel > 0 && variant !== 'outline' && (
            <rect
              x={fillX}
              y={fillY}
              width={fillWidth}
              height={fillHeight}
              rx={1}
              ry={1}
              fill={getFillColor()}
              className="transition-all duration-300"
            />
          )}
          
          {/* 充电动画效果 */}
          {charging && (
            <g className="animate-pulse">
              {/* 闪电图标 */}
              <path
                d={`M${bodyX + bodyWidth * 0.3} ${bodyY + bodyHeight * 0.2} 
                   L${bodyX + bodyWidth * 0.6} ${bodyY + bodyHeight * 0.2}
                   L${bodyX + bodyWidth * 0.4} ${bodyY + bodyHeight * 0.5}
                   L${bodyX + bodyWidth * 0.7} ${bodyY + bodyHeight * 0.5}
                   L${bodyX + bodyWidth * 0.4} ${bodyY + bodyHeight * 0.8}
                   L${bodyX + bodyWidth * 0.6} ${bodyY + bodyHeight * 0.8}
                   L${bodyX + bodyWidth * 0.3} ${bodyY + bodyHeight * 0.5}
                   L${bodyX + bodyWidth * 0.5} ${bodyY + bodyHeight * 0.5}
                   Z`}
                fill="white"
                className="drop-shadow-sm"
              />
            </g>
          )}
          
          {/* 低电量警告 */}
          {normalizedLevel <= 15 && !charging && (
            <circle
              cx={bodyX + bodyWidth * 0.5}
              cy={bodyY + bodyHeight * 0.5}
              r={1}
              fill="white"
              className="animate-pulse"
            />
          )}
        </svg>
        
        {/* 状态指示器 */}
        {variant === 'minimal' && (
          <div 
            className={cn(
              'absolute -top-1 -right-1 w-2 h-2 rounded-full',
              charging ? 'bg-blue-500 animate-pulse' : 
              normalizedLevel > 30 ? 'bg-green-500' : 'bg-red-500'
            )}
          />
        )}
      </div>
      
      {/* 电量百分比显示 */}
      {showPercentage && (
        <span className={cn(
          'text-xs font-medium tabular-nums',
          size === 'sm' ? 'text-xs' : size === 'lg' ? 'text-sm' : 'text-xs',
          getBatteryColor()
        )}>
          {normalizedLevel}%
        </span>
      )}
      
      {/* 充电状态文字 */}
      {charging && showPercentage && (
        <span className="text-xs text-blue-500 font-medium">
          充电中
        </span>
      )}
    </div>
  );
}

// 预设的电池状态组件
export function BatteryStatusIndicator({ 
  level, 
  charging = false, 
  size = 'sm',
  className 
}: Omit<BatteryIconProps, 'showPercentage' | 'variant'>) {
  return (
    <BatteryIcon
      level={level}
      charging={charging}
      size={size}
      showPercentage={true}
      variant="default"
      className={className}
    />
  );
}

// 简化的电池图标（仅图标，无百分比）
export function SimpleBatteryIcon({ 
  level, 
  charging = false, 
  size = 'md',
  className 
}: Omit<BatteryIconProps, 'showPercentage' | 'variant'>) {
  return (
    <BatteryIcon
      level={level}
      charging={charging}
      size={size}
      showPercentage={false}
      variant="default"
      className={className}
    />
  );
}

// 轮廓电池图标（仅轮廓）
export function OutlineBatteryIcon({ 
  level, 
  charging = false, 
  size = 'md',
  className 
}: Omit<BatteryIconProps, 'showPercentage' | 'variant'>) {
  return (
    <BatteryIcon
      level={level}
      charging={charging}
      size={size}
      showPercentage={false}
      variant="outline"
      className={className}
    />
  );
}

// 最小化电池图标（带状态点）
export function MinimalBatteryIcon({ 
  level, 
  charging = false, 
  size = 'sm',
  className 
}: Omit<BatteryIconProps, 'showPercentage' | 'variant'>) {
  return (
    <BatteryIcon
      level={level}
      charging={charging}
      size={size}
      showPercentage={false}
      variant="minimal"
      className={className}
    />
  );
}

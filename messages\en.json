{"common": {"welcome": "Welcome to Langtian PDCS", "dashboard": "Dashboard", "devices": "Devices", "alarms": "Alarms", "reports": "Reports", "settings": "Settings", "logout": "Logout", "login": "<PERSON><PERSON>", "loading": "Loading...", "save": "Save", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "add": "Add", "search": "Search", "filter": "Filter", "export": "Export", "import": "Import"}, "navigation": {"home": "Home", "overview": "Overview", "monitoring": "Monitoring", "control": "Control", "maintenance": "Maintenance", "analytics": "Analytics"}, "dashboard": {"title": "System Dashboard", "systemStatus": "System Status", "activeDevices": "Active Devices", "totalAlarms": "Total Alarms", "energyConsumption": "Energy Consumption", "productionRate": "Production Rate"}, "devices": {"title": "Device Management", "deviceList": "Device List", "deviceStatus": "Device Status", "online": "Online", "offline": "Offline", "maintenance": "Maintenance", "error": "Error"}, "alarms": {"title": "Alarm Management", "activeAlarms": "Active Alarms", "alarmHistory": "Alarm History", "critical": "Critical", "warning": "Warning", "info": "Info", "acknowledged": "Acknowledged"}, "events": {"title": "Event Viewer", "description": "View and manage system events including device status, channel operations, user actions, etc.", "totalEvents": "Total Events", "criticalEvents": "Critical Events", "deviceEvents": "Device Events", "channelEvents": "Channel Events", "userEvents": "User Events", "systemEvents": "System Events", "recentEvents": "Last 24 Hours", "needAttention": "Events Need Attention", "deviceStatusChange": "Device Status Changes", "channelOperations": "Channel Operation Records", "realTimeUpdate": "Real-time Update", "export": "Export", "settings": "Settings", "filter": "Filter", "eventList": "Event List", "eventListDescription": "System events displayed in reverse chronological order", "loading": "Loading...", "noEvents": "No event data available", "showingEvents": "Showing {filtered} / {total} events", "previousPage": "Previous", "nextPage": "Next", "pageInfo": "Page {current} of {total}", "quickTime": {"lastHour": "Last Hour", "today": "Today", "yesterday": "Yesterday", "last7Days": "Last 7 Days", "last30Days": "Last 30 Days", "thisMonth": "This Month", "lastMonth": "Last Month"}, "levels": {"info": "Info", "warning": "Warning", "error": "Error", "critical": "Critical"}, "categories": {"device": "<PERSON><PERSON>", "channel": "Channel", "user": "User", "system": "System"}, "types": {"device_online": "Device Online", "device_offline": "Device Offline", "device_maintenance": "Device Maintenance", "device_error": "<PERSON><PERSON>", "device_recovered": "<PERSON><PERSON> Recovered", "channel_enabled": "Channel Enabled", "channel_disabled": "Channel Disabled", "channel_step_changed": "Step Changed", "channel_parameter_exceeded": "Parameter Exceeded", "channel_process_started": "Process Started", "channel_process_completed": "Process Completed", "channel_process_stopped": "Process Stopped", "user_login": "User Login", "user_logout": "User <PERSON>", "user_device_control": "Device Control", "user_channel_operation": "Channel Operation", "user_config_changed": "Config Changed", "user_settings_modified": "Settings Modified", "system_startup": "System Startup", "system_shutdown": "System Shutdown", "system_error": "System Error", "system_warning": "System Warning", "system_backup_created": "Backup Created", "system_update_installed": "Update Installed"}}, "batteryAnalysis": {"title": "Battery Data Analysis", "description": "Visualization analysis and statistical reports of battery test data", "dataPoints": "Data Points", "exportCSV": "Export CSV", "exportJSON": "Export JSON", "loadNewFile": "Load New File", "recordCount": "Record Count", "cycleRange": "Cycle Range", "fileSize": "File Size", "uploadTime": "Upload Time", "charts": {"voltage": "Voltage Analysis", "capacity": {"title": "Capacity Analysis", "noData": "No data available", "totalCycles": "Total Cycles", "currentRetention": "Current Retention", "initialCapacity": "Initial Capacity", "currentCapacity": "Current Capacity", "fadeRate": "Fade Rate", "avgEfficiency": "Avg Efficiency", "chargeCapacity": "Charge Capacity", "dischargeCapacity": "Discharge Capacity", "efficiency": "Coulombic Efficiency", "retention": "Capacity Retention", "energyEfficiency": "Energy Efficiency", "cycleAxis": "Cycle Number", "capacityAxis": "Capacity (Ah)", "efficiencyAxis": "Efficiency (%)", "retentionAxis": "Retention (%)", "coulombicEfficiency": "Coulombic Efficiency", "capacityRetention": "Capacity Retention", "tabs": {"capacity": "Capacity Comparison", "efficiency": "Efficiency Analysis", "retention": "Retention"}, "tooltip": {"title": "Cycle"}}, "temperature": {"title": "Temperature Monitoring", "noData": "No data available", "dataPoints": "Data Points", "criticalAlerts": "Critical Alerts", "warnings": "Temperature Warnings", "maxTemp": "Max Temperature", "minTemp": "Min Temperature", "avgTemp": "Avg Temperature", "tempRange": "Temperature Range", "timeAxis": "Time", "temperatureAxis": "Temperature (°C)", "warningThreshold": "Warning Threshold", "criticalThreshold": "Critical Threshold", "avgLine": "Average", "temperature": "Temperature", "voltage": "Voltage", "current": "Current", "stepType": "Step Type", "cycle": "Cycle", "time": "Time", "criticalWarning": "Detected {count} critical temperature warning points (>{threshold}°C)", "temperatureWarning": "Detected {count} temperature warning points (>{threshold}°C)", "tooltip": {"title": "Temperature Data"}}, "differential": {"title": "Differential Analysis (dQ/dV)", "noData": "No data available", "dataPoints": "Data Points", "cycles": "Cycles", "maxDQDV": "Max dQ/dV", "avgDQDV": "Avg dQ/dV", "voltageRange": "Voltage Range", "totalPoints": "Total Points", "voltageAxis": "Voltage (V)", "voltage": "Voltage", "cycle": "Cycle", "stepType": "Step Type", "current": "Current", "charge": "Charge", "discharge": "Discharge", "avgLine": "Average", "tabs": {"byCycle": "By Cycle", "byStepType": "By Step Type", "combined": "Combined View"}, "tooltip": {"title": "Differential Data"}}, "voltageTime": {"title": "Voltage-Time Curve", "noData": "No data available", "dataPoints": "Data Points", "cycles": "Cycles", "maxVoltage": "Max Voltage", "minVoltage": "Min Voltage", "avgVoltage": "Avg Voltage", "timeAxis": "Time", "voltageAxis": "Voltage (V)", "avgLine": "Average", "voltage": "Voltage", "current": "Current", "stepType": "Step Type", "cycle": "Cycle", "temperature": "Temperature", "time": "Time", "tooltip": {"title": "Voltage Data"}}}, "fileUpload": {"title": "File Upload", "dragDrop": "Drag and drop files here or click to select files", "dropHere": "Drop files here", "supportedFormats": "Supported Formats", "maxSize": "<PERSON> Si<PERSON>", "selectFile": "Select File", "uploading": "Uploading", "success": "File uploaded successfully", "recordCount": "Record Count", "fileSize": "File Size", "cycleRange": "Cycle Range", "uploadTime": "Upload Time"}, "stats": {"title": "Statistical Analysis", "noData": "No data available", "batteryHealth": {"title": "Battery Health Status", "capacityRetention": "Capacity Retention", "initialCapacity": "Initial Capacity", "currentCapacity": "Current Capacity", "status": {"excellent": "Excellent", "good": "Good", "fair": "Fair", "poor": "Poor"}}, "capacity": {"title": "Capacity Statistics", "maxCapacity": "Max Capacity", "avgCapacity": "Avg Capacity", "minCapacity": "Min Capacity", "fadeRate": "Fade Rate"}, "energy": {"title": "Energy Efficiency", "chargeEfficiency": "Charge Efficiency", "dischargeEfficiency": "Discharge Efficiency", "roundTripEfficiency": "Round Trip Efficiency"}, "cycle": {"title": "Cycle Statistics", "totalCycles": "Total Cycles", "completedCycles": "Completed Cycles", "avgCycleTime": "Avg Cycle Time", "maxCycleTime": "Max Cycle Time"}, "temperature": {"title": "Temperature Statistics", "maxTemp": "Max Temperature", "avgTemp": "Avg Temperature", "minTemp": "Min Temperature", "variation": "Temperature Variation"}, "lifetime": {"title": "Lifetime Prediction", "predictedCycles": "Predicted Cycles", "currentRetention": "Current Retention", "fadeRate": "Fade Rate", "confidence": {"high": "High Confidence", "medium": "Medium Confidence", "low": "Low Confidence"}, "disclaimer": "* Prediction results are based on linear degradation model, for reference only"}}}, "batterySorting": {"title": "Battery Sorting", "description": "Intelligent sorting and quality grading of battery test data", "currentRule": "Current Rule", "runSorting": "Run Sorting", "variables": {"title": "Variable Definition", "addVariable": "Add Variable", "variableName": "Variable Name", "dataType": "Data Type", "stepNumber": "Step Number", "cycleNumber": "Cycle Number", "actions": "Actions", "noVariables": "No variables defined", "addNewVariable": "Add New Variable", "editVariable": "Edit Variable", "variableNamePlaceholder": "Enter variable name", "stepNumberPlaceholder": "Optional, specify step number", "cycleNumberPlaceholder": "Optional, specify cycle number", "description": "Description", "descriptionPlaceholder": "Optional, variable description", "cancel": "Cancel", "add": "Add", "save": "Save"}, "grades": {"title": "Grade Definition", "addGrade": "Add Grade", "priority": "Priority", "gradeName": "Grade Name", "color": "Color", "conditions": "Condition Expression", "actions": "Actions", "noGrades": "No grades defined", "noConditions": "No conditions set", "addNewGrade": "Add New Grade", "editGrade": "Edit Grade", "gradeNamePlaceholder": "Enter grade name", "conditionsPlaceholder": "Enter condition expression, e.g.: End Capacity > 3000", "conditionsHelp": "Use variable names, comparison operators (>, <, >=, <=, ==, !=) and logical operators (AND, OR, NOT) to combine conditions", "descriptionPlaceholder": "Optional, grade description", "cancel": "Cancel", "add": "Add", "save": "Save"}, "deviceList": {"title": "Device List", "searchPlaceholder": "Search devices or locations", "noSearchResults": "No matching devices found", "noDevices": "No devices available", "channels": "Channels", "active": "Active", "error": "Error", "lastUpdate": "Last Update", "channel": "Channel", "currentStep": "Current Step", "currentCycle": "Current Cycle", "channelStatus": {"idle": "Idle", "testing": "Testing", "completed": "Completed", "error": "Error", "unknown": "Unknown"}}, "filter": {"title": "Filter", "active": "Active", "reset": "Reset", "apply": "Apply", "dateRange": "Date Range", "selectDateRange": "Select Date Range", "startDate": "Start Date", "endDate": "End Date", "clear": "Clear", "confirm": "Confirm", "devices": "Devices", "stepNumbers": "Step Numbers", "step": "Step", "cycleNumbers": "Cycle Numbers", "cycle": "Cycle", "more": "More", "grades": "Grades", "channelStatus": "Channel Status", "status": {"idle": "Idle", "testing": "Testing", "completed": "Completed", "error": "Error"}}, "results": {"title": "Sorting Results", "refresh": "Refresh", "export": "Export", "loading": "Loading", "noResults": "No sorting results", "noResultsHint": "Please set sorting rules and run sorting first", "device": "<PERSON><PERSON>", "channel": "Channel", "channelNumber": "Channel", "grade": "Grade", "errorMessage": "Error Message", "sortingTime": "Sorting Time", "error": "Error", "pending": "Pending", "stats": {"total": "Total", "graded": "Graded", "ungraded": "Ungraded", "errors": "Errors"}, "gradeDistribution": "Grade Distribution"}, "conditionEditor": {"title": "Condition Editor", "selectVariable": "Select Variable", "selectVariablePlaceholder": "Please select variable", "selectOperator": "Select Operator", "comparisonValue": "Comparison Value", "comparisonValuePlaceholder": "Enter numeric value", "actions": "Actions", "addCondition": "Add Condition", "logicalOperators": "Logical Operators", "addParentheses": "Add Parentheses", "conditionExpression": "Condition Expression", "invalid": "Invalid", "valid": "<PERSON><PERSON>", "conditionPlaceholder": "Enter condition expression or use tools above to build", "usageInstructions": "Usage Instructions", "instruction1": "Use tools above to quickly build conditions", "instruction2": "Supports comparison operators: >, <, >=, <=, ==, !=", "instruction3": "Supports logical operators: AND, OR, NOT", "instruction4": "Use parentheses to control operation precedence", "exampleConditions": "Example Conditions", "clearCondition": "Clear Condition"}, "ruleEditor": {"basicInfo": {"title": "Rule Basic Information", "ruleName": "Rule Name", "ruleNamePlaceholder": "Enter rule name", "isActive": "Enable Rule", "description": "Rule Description", "descriptionPlaceholder": "Enter rule description", "status": "Status", "valid": "<PERSON><PERSON>", "invalid": "Invalid", "variables": "Variables", "grades": "Grades"}, "validation": {"errors": "Validation Errors", "nameRequired": "Rule name is required", "variablesRequired": "At least one variable must be defined", "gradesRequired": "At least one grade must be defined", "gradeConditionsRequired": "All grades must have conditions set", "undefinedVariable": "Grade {grade} uses undefined variable: {variable}"}, "actions": {"test": "Test Rule", "save": "Save Rule"}}, "dataTypes": {"voltage": "Voltage", "current": "Current", "power": "Power", "contactResistance": "Contact Resistance", "capacity": "Capacity", "specificCapacity": "Specific Capacity", "chargeCapacity": "Charge Capacity", "chargeSpecificCapacity": "Charge Specific Capacity", "dischargeCapacity": "Discharge Capacity", "dischargeSpecificCapacity": "Discharge Specific Capacity", "energy": "Energy", "specificEnergy": "Specific Energy", "chargeEnergy": "Charge Energy", "chargeSpecificEnergy": "Charge Specific Energy", "dischargeEnergy": "Discharge Energy", "dischargeSpecificEnergy": "Discharge Specific Energy", "dQdV": "dQ/dV", "dQmdV": "dQm/dV", "v1": "V1 Voltage", "temperature": "Temperature", "auxVoltageDiff": "Aux Voltage Diff", "auxTempDiff": "<PERSON><PERSON>"}}, "settings": {"title": "System Settings", "description": "Configure system display, units, notifications and monitoring settings", "tabs": {"display": "Display", "units": "Units", "notifications": "Notifications", "monitoring": "Monitoring"}, "actions": {"save": "Save Settings", "reset": "Reset", "restoreDefaults": "<PERSON><PERSON>"}, "display": {"theme": {"title": "Theme Settings", "description": "Choose the appearance theme for the application", "label": "Theme", "light": "Light Mode", "dark": "Dark Mode", "system": "Follow System"}, "language": {"title": "Language Settings", "description": "Select the interface display language", "label": "Language", "zh": "中文", "en": "English"}, "interface": {"title": "Interface Settings", "description": "Customize interface display options"}, "fontSize": {"label": "Font Size", "small": "Small", "medium": "Medium", "large": "Large"}, "compactMode": {"label": "Compact Mode", "description": "Reduce spacing between interface elements to show more content"}, "animations": {"label": "Animations", "description": "Enable interface transition animations and visual effects"}}, "units": {"precision": {"title": "Numerical Precision", "description": "Set the number of decimal places for numerical display", "decimalPlaces": "Decimal Places", "example": "Example"}, "electrical": {"title": "Electrical Parameters", "description": "Set display units for voltage, current, power and other parameters", "voltage": "Voltage", "current": "Current", "power": "Power", "resistance": "Resistance"}, "energy": {"title": "Energy Parameters", "description": "Set display units for capacity, energy and other parameters", "capacity": "Capacity", "energy": "Energy"}, "environmental": {"title": "Environmental Parameters", "description": "Set display units for temperature, time and other parameters", "temperature": "Temperature", "time": "Time"}, "preview": {"title": "Unit Preview", "description": "View the display effect of current unit settings"}}, "notifications": {"title": "Notification Settings", "description": "Configure how to receive system notifications", "enabled": {"title": "Enable Notifications", "description": "Receive system event and alert notifications"}, "sound": {"title": "Sound Alerts", "description": "Play notification sounds"}, "desktop": {"title": "Desktop Notifications", "description": "Show notification popups on desktop"}, "email": {"title": "Email Notifications", "description": "Send important notifications to email"}, "types": {"title": "Notification Types", "description": "The system will send notifications in the following situations", "system": {"title": "System Events", "startup": "System startup", "shutdown": "System shutdown", "error": "System error"}, "device": {"title": "Device Events", "online": "Device online", "offline": "Device offline", "alarm": "Device alarm"}, "test": {"title": "Test Events", "completed": "Test completed", "failed": "Test failed", "threshold": "Threshold exceeded"}, "sorting": {"title": "Sorting Events", "completed": "Sorting completed", "error": "Sorting error", "grade": "Grade changed"}}}, "monitoring": {"refresh": {"title": "Refresh Settings", "description": "Configure data refresh frequency", "interval": "Refresh Interval", "options": {"1s": "1 second", "2s": "2 seconds", "5s": "5 seconds", "10s": "10 seconds", "30s": "30 seconds", "1m": "1 minute"}}, "retention": {"title": "Data Retention", "description": "Set historical data retention time", "period": "Retention Period", "options": {"7d": "7 days", "14d": "14 days", "30d": "30 days", "60d": "60 days", "90d": "90 days", "1y": "1 year"}}, "thresholds": {"title": "<PERSON><PERSON>", "description": "Set threshold ranges for parameter limit alarms", "voltage": "Voltage Threshold (V)", "current": "Current Threshold (A)", "temperature": "Temperature Threshold (°C)", "min": "Minimum", "max": "Maximum"}}}, "process": {"title": "Step Process Management", "description": "Configure and manage battery test step processes", "tabs": {"editor": "Step Editor", "table": "Step View", "send": "Step Sender", "overview": "Process Overview"}, "stepEditor": {"title": "Step Editor", "description": "Create and edit battery test step processes", "createStep": "Create Step", "editStep": "Edit Step", "noSteps": "No Steps", "noStepsDescription": "Start creating your first step process", "createFirstStep": "Create Step"}, "stepForm": {"basicInfo": "Basic Information", "basicInfoDescription": "Set basic properties of the step", "electricalParams": "Electrical Parameters", "electricalParamsDescription": "Set current and voltage related parameters", "timeAndTermination": "Time and Termination Conditions", "timeAndTerminationDescription": "Set time and termination conditions for the step", "notes": "Notes", "advancedSettings": "Advanced Settings", "fields": {"name": "Step Name", "status": "Step Status", "mode": "Step Mode", "current": "Current (mA)", "upperVoltage": "Upper Voltage (mV)", "lowerVoltage": "Lower Voltage (mV)", "duration": "Duration", "capacity": "Capacity (mAh)", "power": "Power (mW)", "upperTemperature": "Upper Temperature (℃)", "lowerTemperature": "Lower Temperature (℃)", "samplingInterval": "Sampling Interval (s)", "notes": "Notes"}, "placeholders": {"name": "Enter step name", "status": "Select step status", "mode": "Select step mode", "duration": "00:01:00.000", "notes": "Enter notes..."}, "ranges": {"current": "Range: 0.1 - 10000 mA", "voltage": "Range: 0 - 5000 mV", "capacity": "Range: 0 - 50000 mAh", "power": "Range: 0 - 10000 mW", "temperature": "Range: -40 - 100 ℃", "samplingInterval": "Range: 1 - 3600 s"}, "timeFormat": "Format: hh:mm:ss.ms", "maxCharacters": "Maximum 500 characters", "required": "Required"}, "stepStatus": {"discharge": "Discharge", "charge": "Charge", "rest": "Rest", "suspend": "Suspend", "cycle": "Cycle"}, "stepMode": {"cc": "Constant Current", "cccv": "CC-CV", "cp": "Constant Power", "cr": "Constant Resistance", "dcir": "DC Internal Resistance", "pulse": "Pulse", "cv": "Constant Voltage", "rate_cc": "Rate CC", "rate_cv": "Rate CV"}, "statistics": {"totalSteps": "Total Steps", "chargeSteps": "Charge Steps", "dischargeSteps": "Discharge Steps", "restSteps": "Rest Steps"}}}
'use client';

import { useState } from 'react';
import { useDeviceStore } from '@/stores/deviceStore';
import {
  Dialog,
  DialogContent,
} from '@/components/ui/dialog';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import {
  Settings,
  Loader2,
  Network,
  RefreshCw,
  AlertCircle,
  CheckCircle,
  Clock,
  Plus,
  Trash2
} from 'lucide-react';
import { Device } from '@/lib/types';
import { DeviceSettingsDialog } from './DeviceSettingsDialog';
import { useEventStore } from '@/stores/eventStore';
import { EventType, EventLevel, EventCategory } from '@/types/events';

interface DeviceManagementDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function DeviceManagementDialog({ open, onOpenChange }: DeviceManagementDialogProps) {
  const {
    devices,
    localIpAddress,
    deviceConnectivityStatus,
    setLocalIpAddress,
    testDeviceConnectivity,
    removeDevice
  } = useDeviceStore();

  const { addEvent } = useEventStore();
  
  // 本地状态
  const [localIp, setLocalIp] = useState(localIpAddress);
  const [isTestingAll, setIsTestingAll] = useState(false);
  const [deviceSettingsOpen, setDeviceSettingsOpen] = useState(false);
  const [selectedDevice, setSelectedDevice] = useState<Device | null>(null);
  const [deviceSettingsMode, setDeviceSettingsMode] = useState<'add' | 'edit'>('edit');
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [deviceToDelete, setDeviceToDelete] = useState<string | null>(null);

  // 处理本机IP保存
  const handleSaveLocalIp = () => {
    setLocalIpAddress(localIp);
  };

  // 处理连通性测试
  const handleTestConnectivity = async () => {
    setIsTestingAll(true);
    try {
      await testDeviceConnectivity();
    } finally {
      setIsTestingAll(false);
    }
  };

  // 设备设置对话框关闭后刷新
  const handleDeviceSettingsClose = (open: boolean) => {
    setDeviceSettingsOpen(open);
    if (!open) {
      setSelectedDevice(null);
    }
  };

  // 打开添加设备对话框
  const handleAddDevice = () => {
    setSelectedDevice(null);
    setDeviceSettingsMode('add');
    setDeviceSettingsOpen(true);

    // 记录事件
    addEvent({
      type: EventType.USER_DEVICE_CONTROL,
      category: EventCategory.USER,
      level: EventLevel.INFO,
      source: {
        type: 'user',
        userId: 'admin',
        userName: '管理员'
      },
      title: '打开添加设备对话框',
      description: '用户打开了添加设备对话框',
      data: {
        operation: 'open_add_device_dialog',
        target: 'device_management_dialog'
      }
    });
  };

  // 打开编辑设备对话框
  const handleEditDevice = (device: Device) => {
    setSelectedDevice(device);
    setDeviceSettingsMode('edit');
    setDeviceSettingsOpen(true);

    // 记录事件
    addEvent({
      type: EventType.USER_DEVICE_CONTROL,
      category: EventCategory.USER,
      level: EventLevel.INFO,
      source: {
        type: 'user',
        userId: 'admin',
        userName: '管理员'
      },
      title: '打开设备设置对话框',
      description: `用户打开了设备 ${device.deviceName} 的设置对话框`,
      data: {
        operation: 'open_edit_device_dialog',
        target: device.deviceName,
        parameters: {
          deviceName: device.deviceName,
          ipAddress: device.ipAddress.join('.'),
          status: device.status
        }
      }
    });
  };

  // 打开删除确认对话框
  const handleDeleteDevice = (deviceName: string) => {
    setDeviceToDelete(deviceName);
    setDeleteDialogOpen(true);

    // 记录事件
    addEvent({
      type: EventType.USER_DEVICE_CONTROL,
      category: EventCategory.USER,
      level: EventLevel.WARNING,
      source: {
        type: 'user',
        userId: 'admin',
        userName: '管理员'
      },
      title: '请求删除设备',
      description: `用户请求删除设备 ${deviceName}`,
      data: {
        operation: 'request_delete_device',
        target: deviceName
      }
    });
  };

  // 确认删除设备
  const handleConfirmDelete = () => {
    if (deviceToDelete) {
      // 记录删除事件
      addEvent({
        type: EventType.USER_DEVICE_CONTROL,
        category: EventCategory.USER,
        level: EventLevel.ERROR,
        source: {
          type: 'user',
          userId: 'admin',
          userName: '管理员'
        },
        title: '设备已删除',
        description: `用户删除了设备 ${deviceToDelete}`,
        data: {
          operation: 'delete_device',
          target: deviceToDelete,
          metadata: {
            deletedAt: new Date().toISOString(),
            operator: 'admin'
          }
        }
      });

      removeDevice(deviceToDelete);
      setDeviceToDelete(null);
    }
    setDeleteDialogOpen(false);
  };

  // 取消删除
  const handleCancelDelete = () => {
    setDeviceToDelete(null);
    setDeleteDialogOpen(false);
  };

  // 获取连通状态图标和颜色
  const getConnectivityStatus = (deviceName: string) => {
    const status = deviceConnectivityStatus[deviceName];
    
    switch (status) {
      case 'online':
        return {
          icon: <CheckCircle className="h-4 w-4" />,
          badge: <Badge variant="default" className="bg-green-500">在线</Badge>,
          color: 'text-green-500'
        };
      case 'offline':
        return {
          icon: <AlertCircle className="h-4 w-4" />,
          badge: <Badge variant="destructive">离线</Badge>,
          color: 'text-red-500'
        };
      case 'testing':
        return {
          icon: <Loader2 className="h-4 w-4 animate-spin" />,
          badge: <Badge variant="secondary">测试中</Badge>,
          color: 'text-yellow-500'
        };
      default:
        return {
          icon: <Clock className="h-4 w-4" />,
          badge: <Badge variant="outline">未知</Badge>,
          color: 'text-gray-500'
        };
    }
  };

  // 获取设备类型显示名称
  const getDeviceTypeLabel = (deviceType: string) => {
    const typeMap: Record<string, string> = {
      'battery_tester': '电池测试仪',
      'charge_discharge': '充放电设备',
      'sorting_machine': '分选设备',
      'data_collector': '数据采集器',
      'controller': '控制器',
      'sensor': '传感器',
    };
    return typeMap[deviceType] || deviceType;
  };

  return (
    <>
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="max-w-[95vw] w-auto min-w-[1000px] h-[80vh] max-h-[800px] overflow-hidden">
          <div className="flex flex-col h-full space-y-4 py-4">
            {/* 本机IP设置和连通性测试区域 */}
            <div className="flex-shrink-0">
              <h3 className="text-sm font-medium text-muted-foreground flex items-center gap-2 mb-3">
                <Network className="h-4 w-4" />
                本机网络设置与连通性测试
              </h3>

              <div className="flex items-end gap-3">
                <div className="flex-1">
                  <Label htmlFor="localIp" className="text-sm">本机IP地址</Label>
                  <Input
                    id="localIp"
                    value={localIp}
                    onChange={(e) => setLocalIp(e.target.value)}
                    placeholder="*************"
                    className="mt-1"
                  />
                </div>
                <Button
                  onClick={handleSaveLocalIp}
                  variant="outline"
                  size="sm"
                >
                  保存IP
                </Button>
                <Button
                  onClick={handleTestConnectivity}
                  disabled={isTestingAll}
                  variant="outline"
                  size="sm"
                  className="flex items-center gap-2"
                >
                  {isTestingAll ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    <RefreshCw className="h-4 w-4" />
                  )}
                  {isTestingAll ? '测试中...' : '测试所有设备'}
                </Button>
              </div>
            </div>

            <Separator className="flex-shrink-0" />

            {/* 设备列表表格 */}
            <div className="flex flex-col">
              <div className="flex items-center justify-between mb-4 flex-shrink-0">
                <h3 className="text-sm font-medium text-muted-foreground">设备列表</h3>
                <Button
                  onClick={handleAddDevice}
                  size="sm"
                  className="flex items-center gap-2"
                >
                  <Plus className="h-4 w-4" />
                  添加设备
                </Button>
              </div>

              <div className="border rounded-lg overflow-hidden">
                <div className="h-full overflow-auto max-h-[550px] scrollbar-hide">
                  <Table className="min-w-[900px]">
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-24 text-center">连通状态</TableHead>
                      <TableHead className="w-32">设备名称</TableHead>
                      <TableHead className="w-36">IP地址</TableHead>
                      <TableHead className="w-20 text-center">端口</TableHead>
                      <TableHead className="w-28">设备类型</TableHead>
                      <TableHead className="w-24 text-center">通道数量</TableHead>
                      <TableHead className="w-24 text-center">状态</TableHead>
                      <TableHead className="w-20 text-center">操作</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {devices.map((device) => {
                      const connectivity = getConnectivityStatus(device.deviceName);
                      
                      return (
                        <TableRow key={device.deviceName}>
                          <TableCell className="text-center">
                            <div className="flex items-center justify-center">
                              <div className={connectivity.color}>
                                {connectivity.icon}
                              </div>
                            </div>
                          </TableCell>
                          <TableCell className="font-medium">
                            {device.deviceName}
                          </TableCell>
                          <TableCell className="font-mono">
                            {device.ipAddress.join('.')}
                          </TableCell>
                          <TableCell className="text-center font-mono">502</TableCell>
                          <TableCell>
                            {getDeviceTypeLabel('battery_tester')}
                          </TableCell>
                          <TableCell className="text-center">64</TableCell>
                          <TableCell className="text-center">
                            {connectivity.badge}
                          </TableCell>
                          <TableCell className="text-center">
                            <div className="flex items-center justify-center gap-1">
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleEditDevice(device)}
                                className="h-8 w-8 p-0"
                                title="编辑设备"
                              >
                                <Settings className="h-4 w-4" />
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleDeleteDevice(device.deviceName)}
                                className="h-8 w-8 p-0 text-red-500 hover:text-red-700 hover:bg-red-50"
                                title="删除设备"
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      );
                    })}
                  </TableBody>
                  </Table>
                </div>
              </div>

              {devices.length === 0 && (
                <div className="text-center py-8 text-muted-foreground">
                  暂无设备数据
                </div>
              )}
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* 设备设置对话框 */}
      <DeviceSettingsDialog
        device={selectedDevice}
        open={deviceSettingsOpen}
        onOpenChange={handleDeviceSettingsClose}
        mode={deviceSettingsMode}
      />

      {/* 删除确认对话框 */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>确认删除设备</AlertDialogTitle>
            <AlertDialogDescription>
              您确定要删除设备 "{deviceToDelete}" 吗？此操作无法撤销，将同时删除该设备的所有通道数据和报警记录。
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={handleCancelDelete}>取消</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleConfirmDelete}
              className="bg-red-600 hover:bg-red-700"
            >
              删除
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}

'use client';

import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  TrendingUp, 
  TrendingDown, 
  Minus, 
  Monitor, 
  Activity, 
  CheckCircle, 
  Heart,
  AlertTriangle
} from 'lucide-react';
import { KPIMetric } from '@/lib/types/dashboard';
import { cn } from '@/lib/utils';

interface KPICardProps {
  metric: KPIMetric;
  className?: string;
}

const ICON_MAP = {
  monitor: Monitor,
  activity: Activity,
  'check-circle': CheckCircle,
  heart: Heart
};

export function KPICard({ metric, className }: KPICardProps) {
  const IconComponent = metric.icon ? ICON_MAP[metric.icon as keyof typeof ICON_MAP] : Activity;
  
  const getTrendIcon = () => {
    switch (metric.changeType) {
      case 'increase':
        return <TrendingUp className="h-3 w-3" />;
      case 'decrease':
        return <TrendingDown className="h-3 w-3" />;
      default:
        return <Minus className="h-3 w-3" />;
    }
  };

  const getTrendColor = () => {
    switch (metric.changeType) {
      case 'increase':
        return 'text-green-600';
      case 'decrease':
        return 'text-red-600';
      default:
        return 'text-gray-600';
    }
  };

  const getStatusColor = () => {
    switch (metric.status) {
      case 'good':
        return 'text-green-600';
      case 'warning':
        return 'text-orange-600';
      case 'error':
        return 'text-red-600';
      default:
        return 'text-gray-600';
    }
  };

  const getStatusIcon = () => {
    switch (metric.status) {
      case 'warning':
        return <AlertTriangle className="h-4 w-4 text-orange-500" />;
      case 'error':
        return <AlertTriangle className="h-4 w-4 text-red-500" />;
      default:
        return null;
    }
  };

  return (
    <Card className={cn('relative overflow-hidden', className)}>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium text-muted-foreground">
          {metric.title}
        </CardTitle>
        <div className="flex items-center space-x-1">
          {getStatusIcon()}
          <IconComponent className={cn('h-4 w-4', getStatusColor())} />
        </div>
      </CardHeader>
      <CardContent>
        <div className="flex items-baseline justify-between">
          <div className="flex items-baseline space-x-1">
            <div className="text-2xl font-bold">
              {metric.value.toFixed(1)}
            </div>
            {metric.unit && (
              <span className="text-sm text-muted-foreground">
                {metric.unit}
              </span>
            )}
          </div>
          
          {metric.change !== undefined && (
            <Badge 
              variant="outline" 
              className={cn(
                'flex items-center space-x-1 text-xs',
                getTrendColor()
              )}
            >
              {getTrendIcon()}
              <span>{Math.abs(metric.change).toFixed(1)}%</span>
            </Badge>
          )}
        </div>
        
        <p className="text-xs text-muted-foreground mt-1">
          {metric.changeType === 'increase' && '较上小时增长'}
          {metric.changeType === 'decrease' && '较上小时下降'}
          {metric.changeType === 'stable' && '保持稳定'}
        </p>
      </CardContent>
      
      {/* 状态指示条 */}
      <div 
        className={cn(
          'absolute bottom-0 left-0 right-0 h-1',
          metric.status === 'good' && 'bg-green-500',
          metric.status === 'warning' && 'bg-orange-500',
          metric.status === 'error' && 'bg-red-500'
        )}
      />
    </Card>
  );
}

interface KPIGridProps {
  metrics: KPIMetric[];
  className?: string;
}

export function KPIGrid({ metrics, className }: KPIGridProps) {
  return (
    <div className={cn('grid gap-4 md:grid-cols-2 lg:grid-cols-4', className)}>
      {metrics.map((metric) => (
        <KPICard key={metric.id} metric={metric} />
      ))}
    </div>
  );
}

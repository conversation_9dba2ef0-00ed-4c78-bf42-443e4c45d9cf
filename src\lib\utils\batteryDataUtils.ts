import { BatteryDataRecord, StepType, ModuleSwitch, BatteryDataSet, BatteryDataMetadata, CSVImportConfig } from '@/lib/types';

/**
 * 解析CSV文件内容为电池数据记录数组
 */
export function parseCSVToBatteryData(
  csvContent: string,
  config: CSVImportConfig = {
    delimiter: ',',
    encoding: 'utf-8',
    hasHeader: true,
    skipRows: 0,
    fieldMapping: {}
  }
): BatteryDataRecord[] {
  const lines = csvContent.split('\n').filter(line => line.trim());
  
  // 跳过指定行数
  const dataLines = lines.slice(config.skipRows);
  
  // 如果有表头，跳过第一行
  const recordLines = config.hasHeader ? dataLines.slice(1) : dataLines;
  
  return recordLines.map((line, index) => {
    const fields = parseCSVLine(line, config.delimiter);
    return parseRecordFields(fields, index);
  }).filter(record => record !== null) as BatteryDataRecord[];
}

/**
 * 解析CSV行，处理引号和转义字符
 */
function parseCSVLine(line: string, delimiter: string): string[] {
  const result: string[] = [];
  let current = '';
  let inQuotes = false;
  
  for (let i = 0; i < line.length; i++) {
    const char = line[i];
    
    if (char === '"') {
      inQuotes = !inQuotes;
    } else if (char === delimiter && !inQuotes) {
      result.push(current.trim());
      current = '';
    } else {
      current += char;
    }
  }
  
  result.push(current.trim());
  return result;
}

/**
 * 解析单条记录的字段
 */
function parseRecordFields(fields: string[], index: number): BatteryDataRecord | null {
  try {
    // 根据CSV文件的列顺序解析字段
    const [
      dataSequence, cycleNumber, stepNumber, stepType, time, totalTime,
      current, voltage, capacity, specificCapacity, chargeCapacity, chargeSpecificCapacity,
      dischargeCapacity, dischargeSpecificCapacity, energy, specificEnergy,
      chargeEnergy, chargeSpecificEnergy, dischargeEnergy, dischargeSpecificEnergy,
      absoluteTime, power, dQdV, dQmdV, contactResistance, moduleSwitch,
      v1, temperature, auxVoltageDiff, auxTempDiff
    ] = fields;

    return {
      dataSequence: parseInt(dataSequence) || 0,
      cycleNumber: parseInt(cycleNumber) || 0,
      stepNumber: parseInt(stepNumber) || 0,
      stepType: mapStepType(stepType),
      time: time || '00:00:00',
      totalTime: totalTime || '00:00:00',
      absoluteTime: parseAbsoluteTime(absoluteTime),
      current: parseFloat(current) || 0,
      voltage: parseFloat(voltage) || 0,
      power: parseFloat(power) || 0,
      contactResistance: parseFloat(contactResistance) || 0,
      capacity: parseFloat(capacity) || 0,
      specificCapacity: parseFloat(specificCapacity) || 0,
      chargeCapacity: parseFloat(chargeCapacity) || 0,
      chargeSpecificCapacity: parseFloat(chargeSpecificCapacity) || 0,
      dischargeCapacity: parseFloat(dischargeCapacity) || 0,
      dischargeSpecificCapacity: parseFloat(dischargeSpecificCapacity) || 0,
      energy: parseFloat(energy) || 0,
      specificEnergy: parseFloat(specificEnergy) || 0,
      chargeEnergy: parseFloat(chargeEnergy) || 0,
      chargeSpecificEnergy: parseFloat(chargeSpecificEnergy) || 0,
      dischargeEnergy: parseFloat(dischargeEnergy) || 0,
      dischargeSpecificEnergy: parseFloat(dischargeSpecificEnergy) || 0,
      dQdV: parseFloat(dQdV) || 0,
      dQmdV: parseFloat(dQmdV) || 0,
      moduleSwitch: mapModuleSwitch(moduleSwitch),
      v1: parseFloat(v1) || 0,
      temperature: parseFloat(temperature) || 0,
      auxVoltageDiff: parseFloat(auxVoltageDiff) || 0,
      auxTempDiff: parseFloat(auxTempDiff) || 0,
    };
  } catch (error) {
    console.warn(`解析第 ${index + 1} 行数据时出错:`, error);
    return null;
  }
}

/**
 * 映射工步类型
 */
function mapStepType(stepType: string): StepType {
  const typeMap: Record<string, StepType> = {
    '搁置': StepType.REST,
    '恒流充电': StepType.CC_CHARGE,
    '恒流放电': StepType.CC_DISCHARGE,
    '恒压充电': StepType.CV_CHARGE,
    '恒流恒压充电': StepType.CCCV_CHARGE,
  };
  
  return typeMap[stepType] || StepType.REST;
}

/**
 * 映射模块开关状态
 */
function mapModuleSwitch(switchState: string): ModuleSwitch {
  return switchState === '开启' ? ModuleSwitch.ON : ModuleSwitch.OFF;
}

/**
 * 解析绝对时间
 */
function parseAbsoluteTime(timeStr: string): Date {
  try {
    return new Date(timeStr);
  } catch {
    return new Date();
  }
}

/**
 * 将电池数据记录数组转换为CSV格式
 */
export function batteryDataToCSV(data: BatteryDataRecord[]): string {
  const headers = [
    '数据序号', '循环号', '工步号', '工步类型', '时间', '总时间',
    '电流(A)', '电压(V)', '容量(Ah)', '比容量(mAh/g)', '充电容量(Ah)', '充电比容量(mAh/g)',
    '放电容量(Ah)', '放电比容量(mAh/g)', '能量(Wh)', '比能量(mWh/g)',
    '充电能量(Wh)', '充电比能量(mWh/g)', '放电能量(Wh)', '放电比能量(mWh/g)',
    '绝对时间', '功率(W)', 'dQ/dV(mAh/V)', 'dQm/dV(mAh/V.g)', '接触电阻(mΩ)', '模块启停开关',
    'V1(V)', 'T1(℃)', '辅助通道电压差(V)', '辅助通道温差(℃)'
  ];

  const csvLines = [headers.join(',')];
  
  data.forEach(record => {
    const row = [
      record.dataSequence,
      record.cycleNumber,
      record.stepNumber,
      record.stepType,
      record.time,
      record.totalTime,
      record.current,
      record.voltage,
      record.capacity,
      record.specificCapacity,
      record.chargeCapacity,
      record.chargeSpecificCapacity,
      record.dischargeCapacity,
      record.dischargeSpecificCapacity,
      record.energy,
      record.specificEnergy,
      record.chargeEnergy,
      record.chargeSpecificEnergy,
      record.dischargeEnergy,
      record.dischargeSpecificEnergy,
      record.absoluteTime.toISOString().replace('T', ' ').substring(0, 19),
      record.power,
      record.dQdV,
      record.dQmdV,
      record.contactResistance,
      record.moduleSwitch,
      record.v1,
      record.temperature,
      record.auxVoltageDiff,
      record.auxTempDiff
    ];
    
    csvLines.push(row.join(','));
  });

  return csvLines.join('\n');
}

/**
 * 生成电池数据集的元信息
 */
export function generateBatteryDataMetadata(data: BatteryDataRecord[]): BatteryDataMetadata {
  if (data.length === 0) {
    return {
      cycleRange: { start: 0, end: 0 },
      dateRange: { start: new Date(), end: new Date() }
    };
  }

  const cycles = data.map(d => d.cycleNumber);
  const dates = data.map(d => d.absoluteTime);
  const voltages = data.map(d => d.voltage);
  const temperatures = data.map(d => d.temperature);

  return {
    cycleRange: {
      start: Math.min(...cycles),
      end: Math.max(...cycles)
    },
    dateRange: {
      start: new Date(Math.min(...dates.map(d => d.getTime()))),
      end: new Date(Math.max(...dates.map(d => d.getTime())))
    },
    testConditions: {
      temperature: temperatures.reduce((sum, t) => sum + t, 0) / temperatures.length
    }
  };
}

/**
 * 创建电池数据集
 */
export function createBatteryDataSet(
  name: string,
  data: BatteryDataRecord[],
  description?: string
): BatteryDataSet {
  return {
    id: generateId(),
    name,
    description,
    uploadTime: new Date(),
    fileSize: JSON.stringify(data).length,
    recordCount: data.length,
    data,
    metadata: generateBatteryDataMetadata(data)
  };
}

/**
 * 生成唯一ID
 */
function generateId(): string {
  return Date.now().toString(36) + Math.random().toString(36).substr(2);
}

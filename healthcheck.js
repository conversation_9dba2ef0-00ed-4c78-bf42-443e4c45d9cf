#!/usr/bin/env node

/**
 * 朗天PDCS系统健康检查脚本
 */

const http = require('http');
const https = require('https');

const config = {
  host: process.env.HEALTH_CHECK_HOST || 'localhost',
  port: process.env.HEALTH_CHECK_PORT || 3000,
  path: process.env.HEALTH_CHECK_PATH || '/',
  timeout: process.env.HEALTH_CHECK_TIMEOUT || 5000,
  useHttps: process.env.HEALTH_CHECK_HTTPS === 'true'
};

function healthCheck() {
  return new Promise((resolve, reject) => {
    const client = config.useHttps ? https : http;
    
    const options = {
      hostname: config.host,
      port: config.port,
      path: config.path,
      method: 'GET',
      timeout: config.timeout
    };

    const req = client.request(options, (res) => {
      if (res.statusCode >= 200 && res.statusCode < 400) {
        resolve({
          status: 'healthy',
          statusCode: res.statusCode,
          timestamp: new Date().toISOString()
        });
      } else {
        reject({
          status: 'unhealthy',
          statusCode: res.statusCode,
          timestamp: new Date().toISOString()
        });
      }
    });

    req.on('error', (error) => {
      reject({
        status: 'error',
        error: error.message,
        timestamp: new Date().toISOString()
      });
    });

    req.on('timeout', () => {
      req.destroy();
      reject({
        status: 'timeout',
        timeout: config.timeout,
        timestamp: new Date().toISOString()
      });
    });

    req.end();
  });
}

async function main() {
  try {
    const result = await healthCheck();
    console.log('✅ 健康检查通过:', JSON.stringify(result, null, 2));
    process.exit(0);
  } catch (error) {
    console.error('❌ 健康检查失败:', JSON.stringify(error, null, 2));
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = { healthCheck };

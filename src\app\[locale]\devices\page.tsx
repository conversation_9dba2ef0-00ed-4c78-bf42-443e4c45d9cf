import { useTranslations } from 'next-intl';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Search, Plus, Filter, MoreHorizontal } from 'lucide-react';

// 模拟设备数据
const mockDevices = [
  {
    id: 'DEV001',
    name: '主控制器 A1',
    type: 'PLC',
    status: 'online',
    location: '车间A-1区',
    lastUpdate: '2025-07-21 11:25:30',
    temperature: 45.2,
    pressure: 2.3,
  },
  {
    id: 'DEV002',
    name: '传感器 B2',
    type: 'Sensor',
    status: 'online',
    location: '车间B-2区',
    lastUpdate: '2025-07-21 11:25:28',
    temperature: 38.7,
    pressure: 1.8,
  },
  {
    id: 'DEV003',
    name: '执行器 C3',
    type: 'Actuator',
    status: 'maintenance',
    location: '车间C-3区',
    lastUpdate: '2025-07-21 10:45:15',
    temperature: 52.1,
    pressure: 3.1,
  },
  {
    id: 'DEV004',
    name: '监控摄像头 D4',
    type: 'Camera',
    status: 'offline',
    location: '车间D-4区',
    lastUpdate: '2025-07-21 09:30:22',
    temperature: null,
    pressure: null,
  },
];

const getStatusBadge = (status: string) => {
  switch (status) {
    case 'online':
      return <Badge className="bg-green-500">在线</Badge>;
    case 'offline':
      return <Badge variant="destructive">离线</Badge>;
    case 'maintenance':
      return <Badge className="bg-yellow-500">维护中</Badge>;
    case 'error':
      return <Badge variant="destructive">故障</Badge>;
    default:
      return <Badge variant="secondary">未知</Badge>;
  }
};

export default function DevicesPage() {
  const t = useTranslations('devices');
  const tCommon = useTranslations('common');

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">{t('title')}</h1>
          <p className="text-muted-foreground">
            管理和监控所有工业设备的状态和参数
          </p>
        </div>
        <Button>
          <Plus className="mr-2 h-4 w-4" />
          添加设备
        </Button>
      </div>

      {/* 统计卡片 */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">总设备数</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">24</div>
            <p className="text-xs text-muted-foreground">
              +2 较上月
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('online')}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">18</div>
            <p className="text-xs text-muted-foreground">
              75% 在线率
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('maintenance')}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">4</div>
            <p className="text-xs text-muted-foreground">
              计划维护中
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('offline')}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">2</div>
            <p className="text-xs text-muted-foreground">
              需要检查
            </p>
          </CardContent>
        </Card>
      </div>

      {/* 设备列表 */}
      <Card>
        <CardHeader>
          <CardTitle>{t('deviceList')}</CardTitle>
          <CardDescription>
            查看和管理所有设备的详细信息
          </CardDescription>
        </CardHeader>
        <CardContent>
          {/* 搜索和筛选 */}
          <div className="flex items-center space-x-2 mb-4">
            <div className="relative flex-1">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input placeholder="搜索设备..." className="pl-8" />
            </div>
            <Button variant="outline">
              <Filter className="mr-2 h-4 w-4" />
              筛选
            </Button>
          </div>

          {/* 设备表格 */}
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>设备ID</TableHead>
                <TableHead>设备名称</TableHead>
                <TableHead>类型</TableHead>
                <TableHead>状态</TableHead>
                <TableHead>位置</TableHead>
                <TableHead>温度(°C)</TableHead>
                <TableHead>压力(MPa)</TableHead>
                <TableHead>最后更新</TableHead>
                <TableHead>操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {mockDevices.map((device) => (
                <TableRow key={device.id}>
                  <TableCell className="font-medium">{device.id}</TableCell>
                  <TableCell>{device.name}</TableCell>
                  <TableCell>{device.type}</TableCell>
                  <TableCell>{getStatusBadge(device.status)}</TableCell>
                  <TableCell>{device.location}</TableCell>
                  <TableCell>
                    {device.temperature ? device.temperature.toFixed(1) : '-'}
                  </TableCell>
                  <TableCell>
                    {device.pressure ? device.pressure.toFixed(1) : '-'}
                  </TableCell>
                  <TableCell className="text-sm text-muted-foreground">
                    {device.lastUpdate}
                  </TableCell>
                  <TableCell>
                    <Button variant="ghost" size="icon">
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
}

# 电池数据分析模块

## 项目概述

本项目为朗天PDCS（工业过程数据采集与监视控制系统）新增了完整的电池数据分析功能模块，专门用于电池行业的测试数据可视化分析和统计报告。

## 功能特性

### 🔋 核心功能
- **CSV文件导入导出** - 支持电池测试数据的批量导入和导出
- **实时数据可视化** - 多种专业图表展示电池性能数据
- **统计分析** - 全面的电池性能指标统计和健康状态评估
- **寿命预测** - 基于历史数据的电池寿命预测算法
- **国际化支持** - 完整的中英文双语界面

### 📊 数据可视化图表
1. **电压-时间曲线图** - 展示电池充放电过程中的电压变化
2. **容量分析图** - 分析电池容量衰减趋势和效率变化
3. **温度监控图** - 监控电池测试过程中的温度变化
4. **微分分析图 (dQ/dV)** - 电池电化学特性的微分分析

### 📈 统计分析功能
- **电池健康状态评估** - 基于容量保持率的健康等级评定
- **容量统计** - 初始容量、当前容量、衰减率等关键指标
- **能量效率分析** - 充电效率、放电效率、往返效率统计
- **循环寿命分析** - 循环次数、平均循环时间等统计
- **温度影响分析** - 温度对电池性能的影响评估

## 技术架构

### 前端技术栈
- **框架**: Next.js 15 (App Router) + TypeScript
- **UI组件**: Shadcn UI + Tailwind CSS v4
- **图表库**: Recharts
- **状态管理**: Zustand
- **国际化**: next-intl
- **文件处理**: react-dropzone

### 项目结构
```
src/
├── components/battery-analysis/          # 电池分析组件
│   ├── FileUpload.tsx                   # 文件上传组件
│   ├── BatteryStatsPanel.tsx           # 统计面板组件
│   └── charts/                         # 图表组件目录
│       ├── VoltageTimeChart.tsx         # 电压-时间图表
│       ├── CapacityAnalysisChart.tsx    # 容量分析图表
│       ├── TemperatureChart.tsx         # 温度监控图表
│       └── DifferentialAnalysisChart.tsx # 微分分析图表
├── lib/
│   ├── types/index.ts                   # 电池数据类型定义
│   ├── utils/
│   │   ├── batteryDataUtils.ts          # 数据处理工具
│   │   └── batteryAnalysis.ts           # 统计分析算法
│   └── hooks/
│       └── useBatteryDataImport.ts      # 数据导入Hook
└── app/[locale]/analytics/page.tsx      # 数据分析主页面
```

## 数据格式支持

### CSV文件格式
支持标准的电池测试数据CSV格式，包含以下字段：
- 基础信息：数据序号、循环号、工步号、工步类型、时间信息
- 电学参数：电流、电压、功率、接触电阻
- 容量参数：容量、比容量、充放电容量
- 能量参数：能量、比能量、充放电能量
- 环境参数：温度、辅助通道数据
- 微分参数：dQ/dV、dQm/dV

### 工步类型支持
- 搁置 (REST)
- 恒流充电 (CC_CHARGE)
- 恒流放电 (CC_DISCHARGE)
- 恒压充电 (CV_CHARGE)
- 恒流恒压充电 (CCCV_CHARGE)

## 使用指南

### 1. 数据导入
1. 访问 `/analytics` 页面
2. 点击或拖拽CSV文件到上传区域
3. 系统自动解析并验证数据格式
4. 上传成功后显示数据集基本信息

### 2. 数据分析
- **电压分析**: 查看电池充放电过程中的电压变化曲线
- **容量分析**: 分析电池容量衰减趋势和充放电效率
- **温度监控**: 监控测试过程中的温度变化和异常
- **微分分析**: 查看dQ/dV曲线，分析电池电化学特性

### 3. 统计报告
右侧统计面板提供：
- 电池健康状态评估
- 详细的性能统计数据
- 基于线性模型的寿命预测

### 4. 数据导出
- 支持CSV格式导出（保持原始数据结构）
- 支持JSON格式导出（包含元数据和统计信息）

## 测试数据

项目根目录提供了 `test-battery-data.csv` 测试文件，包含2个循环的示例数据，可用于功能测试。

## 开发说明

### 安装依赖
```bash
cd tianlang-pdcs
pnpm install
```

### 启动开发服务器
```bash
pnpm dev
```

### 添加新的UI组件
```bash
pnpm dlx shadcn@latest add [component-name]
```

## 国际化

项目支持中英文双语：
- 中文：`/zh/analytics`
- 英文：`/en/analytics`

翻译文件位于：
- `messages/zh.json` - 中文翻译
- `messages/en.json` - 英文翻译

## 性能优化

- 使用Next.js Server Components优化首屏加载
- 图表组件采用虚拟化技术处理大数据集
- 数据处理采用Web Workers避免UI阻塞
- 支持数据分页和过滤减少内存占用

## 扩展性

模块化设计支持：
- 新增图表类型
- 扩展数据格式支持
- 添加新的统计算法
- 集成实时数据源

## 注意事项

1. **文件大小限制**: 单个CSV文件最大支持50MB
2. **浏览器兼容性**: 建议使用Chrome、Firefox、Safari等现代浏览器
3. **数据精度**: 统计计算保留4位小数精度
4. **寿命预测**: 预测结果基于线性衰减模型，仅供参考

## 技术支持

如有问题或建议，请联系开发团队或提交Issue。

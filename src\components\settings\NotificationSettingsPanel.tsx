'use client';

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { 
  Bell, 
  Volume2, 
  Monitor, 
  Mail,
  AlertTriangle
} from 'lucide-react';
import { useTranslations } from 'next-intl';

interface NotificationSettings {
  enabled: boolean;
  sound: boolean;
  desktop: boolean;
  email: boolean;
}

interface NotificationSettingsPanelProps {
  settings: NotificationSettings;
  onSettingsChange: (settings: NotificationSettings) => void;
}

export function NotificationSettingsPanel({
  settings,
  onSettingsChange
}: NotificationSettingsPanelProps) {
  const t = useTranslations('settings.notifications');

  const updateSetting = <K extends keyof NotificationSettings>(
    key: K,
    value: NotificationSettings[K]
  ) => {
    onSettingsChange({
      ...settings,
      [key]: value
    });
  };

  const notificationOptions = [
    {
      key: 'enabled' as const,
      icon: Bell,
      title: t('enabled.title'),
      description: t('enabled.description'),
      value: settings.enabled
    },
    {
      key: 'sound' as const,
      icon: Volume2,
      title: t('sound.title'),
      description: t('sound.description'),
      value: settings.sound,
      disabled: !settings.enabled
    },
    {
      key: 'desktop' as const,
      icon: Monitor,
      title: t('desktop.title'),
      description: t('desktop.description'),
      value: settings.desktop,
      disabled: !settings.enabled
    },
    {
      key: 'email' as const,
      icon: Mail,
      title: t('email.title'),
      description: t('email.description'),
      value: settings.email,
      disabled: !settings.enabled
    }
  ];

  return (
    <div className="space-y-6">
      {/* 通知设置 */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-base">
            <Bell className="h-4 w-4" />
            {t('title')}
          </CardTitle>
          <CardDescription>
            {t('description')}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {notificationOptions.map((option) => {
            const Icon = option.icon;
            return (
              <div key={option.key} className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label className="flex items-center gap-2">
                    <Icon className="h-4 w-4" />
                    {option.title}
                  </Label>
                  <p className="text-sm text-muted-foreground">
                    {option.description}
                  </p>
                </div>
                <Switch
                  checked={option.value}
                  onCheckedChange={(checked) => updateSetting(option.key, checked)}
                  disabled={option.disabled}
                />
              </div>
            );
          })}
        </CardContent>
      </Card>

      {/* 通知类型说明 */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-base">
            <AlertTriangle className="h-4 w-4" />
            {t('types.title')}
          </CardTitle>
          <CardDescription>
            {t('types.description')}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <h4 className="font-medium text-sm">{t('types.system.title')}</h4>
                <ul className="text-sm text-muted-foreground space-y-1">
                  <li>• {t('types.system.startup')}</li>
                  <li>• {t('types.system.shutdown')}</li>
                  <li>• {t('types.system.error')}</li>
                </ul>
              </div>
              
              <div className="space-y-2">
                <h4 className="font-medium text-sm">{t('types.device.title')}</h4>
                <ul className="text-sm text-muted-foreground space-y-1">
                  <li>• {t('types.device.online')}</li>
                  <li>• {t('types.device.offline')}</li>
                  <li>• {t('types.device.alarm')}</li>
                </ul>
              </div>
              
              <div className="space-y-2">
                <h4 className="font-medium text-sm">{t('types.test.title')}</h4>
                <ul className="text-sm text-muted-foreground space-y-1">
                  <li>• {t('types.test.completed')}</li>
                  <li>• {t('types.test.failed')}</li>
                  <li>• {t('types.test.threshold')}</li>
                </ul>
              </div>
              
              <div className="space-y-2">
                <h4 className="font-medium text-sm">{t('types.sorting.title')}</h4>
                <ul className="text-sm text-muted-foreground space-y-1">
                  <li>• {t('types.sorting.completed')}</li>
                  <li>• {t('types.sorting.error')}</li>
                  <li>• {t('types.sorting.grade')}</li>
                </ul>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

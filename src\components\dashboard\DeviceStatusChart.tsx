'use client';

import React from 'react';
import { <PERSON><PERSON><PERSON>, Pie, Cell, ResponsiveC<PERSON>r, Legend, Tooltip } from 'recharts';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { DeviceStatusData } from '@/lib/types/dashboard';

interface DeviceStatusChartProps {
  data: DeviceStatusData[];
  className?: string;
}

const STATUS_LABELS = {
  online: '在线',
  offline: '离线',
  maintenance: '维护中',
  error: '故障'
};

const CustomTooltip = ({ active, payload }: any) => {
  if (active && payload && payload.length) {
    const data = payload[0].payload;
    return (
      <div className="bg-background border rounded-lg shadow-lg p-3">
        <p className="font-medium">{STATUS_LABELS[data.status as keyof typeof STATUS_LABELS]}</p>
        <p className="text-sm text-muted-foreground">
          设备数量: {data.count}
        </p>
        <p className="text-sm text-muted-foreground">
          占比: {data.percentage}%
        </p>
      </div>
    );
  }
  return null;
};

const CustomLegend = ({ payload }: any) => {
  return (
    <div className="flex flex-wrap justify-center gap-4 mt-4">
      {payload.map((entry: any, index: number) => (
        <div key={index} className="flex items-center space-x-2">
          <div 
            className="w-3 h-3 rounded-full" 
            style={{ backgroundColor: entry.color }}
          />
          <span className="text-sm text-muted-foreground">
            {STATUS_LABELS[entry.payload.status as keyof typeof STATUS_LABELS]}
          </span>
          <Badge variant="outline" className="text-xs">
            {entry.payload.count}
          </Badge>
        </div>
      ))}
    </div>
  );
};

export function DeviceStatusChart({ data, className }: DeviceStatusChartProps) {
  const totalDevices = data.reduce((sum, item) => sum + item.count, 0);
  
  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          设备状态分布
          <Badge variant="outline">{totalDevices} 台设备</Badge>
        </CardTitle>
        <CardDescription>
          当前系统中所有设备的状态分布情况
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="h-[300px]">
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie
                data={data}
                cx="50%"
                cy="50%"
                innerRadius={60}
                outerRadius={100}
                paddingAngle={2}
                dataKey="count"
              >
                {data.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip content={<CustomTooltip />} />
              <Legend content={<CustomLegend />} />
            </PieChart>
          </ResponsiveContainer>
        </div>
        
        {/* 状态统计 */}
        <div className="grid grid-cols-2 gap-4 mt-4">
          {data.map((item) => (
            <div key={item.status} className="text-center">
              <div className="text-lg font-semibold" style={{ color: item.color }}>
                {item.count}
              </div>
              <div className="text-xs text-muted-foreground">
                {STATUS_LABELS[item.status]} ({item.percentage}%)
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}

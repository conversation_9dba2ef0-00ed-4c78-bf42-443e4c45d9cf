'use client';

import React, { useState, useEffect, useMemo } from 'react';
import { useTranslations } from 'next-intl';
import { Check, X, RefreshCw, Zap, AlertCircle, ChevronDown, Monitor } from 'lucide-react';

import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';

import { useDeviceStore } from '@/stores/deviceStore';
import {
  ChannelSelectorConfig,
  ChannelSelectionResult,
  Channel as ProcessChannel
} from '@/lib/process/types/channel';
import { Channel as DeviceChannel } from '@/lib/types';

interface ChannelSelectorProps {
  selectedChannelIds?: string[];
  onSelectionChange: (result: ChannelSelectionResult) => void;
  config?: Partial<ChannelSelectorConfig>;
  trigger?: React.ReactNode;
  title?: string;
  description?: string;
  className?: string;
}

const DEFAULT_CONFIG: ChannelSelectorConfig = {
  multiSelect: true,
  showBatteryInfo: false,
  showRealTimeData: false,
  showStatistics: false,
  filterByStatus: [],
  filterByType: [],
  groupByDevice: true,
  compactMode: true
};

// 转换DeviceChannel到ProcessChannel
const convertToProcessChannel = (deviceChannel: DeviceChannel): ProcessChannel => {
  return {
    id: `${deviceChannel.deviceName}_${deviceChannel.channelNumber}`,
    deviceId: deviceChannel.deviceName,
    channelNumber: deviceChannel.channelNumber,
    name: `通道${deviceChannel.channelNumber}`,
    type: 'hybrid' as any,
    status: deviceChannel.isEnabled ? 'idle' : 'offline' as any,
    isSelected: false,
    capabilities: {
      maxCurrent: 5000,
      maxVoltage: 5000,
      maxPower: 25000,
      minVoltage: 0,
      temperatureRange: [-40, 85],
      supportedModes: ['cc', 'cv', 'cccv']
    },
    config: {
      name: `通道${deviceChannel.channelNumber}`,
      enabled: deviceChannel.isEnabled,
      autoStart: false,
      safetyLimits: {
        maxCurrent: 5000,
        maxVoltage: 5000,
        maxTemperature: 85,
        minVoltage: 0,
        minTemperature: -40
      }
    },
    statistics: {
      totalRunTime: 0,
      totalCycles: deviceChannel.cycleCount,
      totalCapacity: deviceChannel.capacity,
      lastMaintenanceDate: undefined,
      nextMaintenanceDate: undefined
    },
    createdAt: new Date(),
    updatedAt: new Date(),
    lastActiveAt: new Date()
  };
};

export function ChannelSelector({
  selectedChannelIds = [],
  onSelectionChange,
  config: userConfig = {},
  trigger,
  title = '选择通道',
  description = '选择要操作的设备通道',
  className
}: ChannelSelectorProps) {

  const t = useTranslations('process.channelSelector');

  // 使用全局设备和通道数据
  const { devices, channels } = useDeviceStore();

  const [isOpen, setIsOpen] = useState(false);
  const [internalSelectedIds, setInternalSelectedIds] = useState<string[]>(selectedChannelIds);
  const [showAdvancedOptions, setShowAdvancedOptions] = useState(false);

  // 合并配置
  const config = useMemo(() => ({
    ...DEFAULT_CONFIG,
    ...userConfig
  }), [userConfig]);

  // 同步外部选择状态
  useEffect(() => {
    setInternalSelectedIds(selectedChannelIds);
  }, [selectedChannelIds]);

  // 获取选中的通道
  const selectedChannels = useMemo(() => {
    return channels.filter(channel => {
      const channelId = `${channel.deviceName}_${channel.channelNumber}`;
      return internalSelectedIds.includes(channelId);
    }).map(convertToProcessChannel);
  }, [channels, internalSelectedIds]);

  // 可用通道（排除禁用的通道）
  const availableChannels = useMemo(() => {
    return channels.filter(channel =>
      channel.isEnabled &&
      channel.alarmStatus === 0 // 没有报警
    );
  }, [channels]);

  // 渲染紧凑的通道网格 - Badge风格
  const renderCompactChannelGrid = (deviceChannels: any[], deviceName: string) => {
    return (
      <div className="grid grid-cols-6 gap-2 p-3">
        {deviceChannels.map((channel) => {
          const channelId = `${deviceName}_${channel.channelNumber}`;
          const isSelected = internalSelectedIds.includes(channelId);
          const isOnline = channel.isEnabled && channel.stepStatus !== '离线';

          // 同步设备监控的状态逻辑
          const isRunning = ['充电', '放电', '运行中'].includes(channel.stepStatus);
          const isPaused = ['暂停', '搁置'].includes(channel.stepStatus);
          const isCompleted = ['完成'].includes(channel.stepStatus);
          const isError = ['故障'].includes(channel.stepStatus);

          // 状态颜色映射 - 同步设备监控的颜色
          const getStatusColor = () => {
            if (!isOnline) return 'bg-gray-200 text-gray-500 border-gray-300';
            if (isSelected) return 'bg-blue-500 text-white border-blue-600';
            if (isError) return 'bg-red-100 text-red-700 border-red-300';
            if (isRunning) return 'bg-green-100 text-green-700 border-green-300';
            if (isPaused) return 'bg-yellow-100 text-yellow-700 border-yellow-300';
            if (isCompleted) return 'bg-gray-100 text-gray-700 border-gray-300';
            return 'bg-blue-50 text-blue-600 border-blue-200 hover:bg-blue-100';
          };

          // 状态图标
          const getStatusIcon = () => {
            if (!isOnline) return '⚫';
            if (isError) return '❌';
            if (isRunning) return '🔋';
            if (isPaused) return '⏸️';
            if (isCompleted) return '✅';
            return '⚪';
          };

          // 获取设备编号（从设备名提取数字）
          const getDeviceNumber = (deviceName: string) => {
            const match = deviceName.match(/(\d+)/);
            return match ? match[1] : deviceName.slice(-1);
          };

          return (
            <div
              key={channelId}
              onClick={() => isOnline && handleChannelSelectById(channelId)}
              className={`
                relative w-16 h-12 rounded-full border-2 cursor-pointer transition-all duration-200 flex flex-col items-center justify-center text-xs font-medium shadow-sm
                ${getStatusColor()}
                ${isOnline ? 'hover:scale-105 hover:shadow-md' : 'cursor-not-allowed opacity-60'}
                ${isSelected ? 'ring-2 ring-blue-400 ring-offset-1' : ''}
              `}
              title={`设备${getDeviceNumber(deviceName)} 通道${channel.channelNumber} - ${channel.stepStatus} - ${channel.stepMode || 'N/A'}`}
            >
              {/* Badge风格布局 */}
              <div className="flex items-center space-x-1">
                {/* 状态图标 */}
                <span className="text-xs">{getStatusIcon()}</span>

                {/* 设备编号+通道编号 */}
                <span className="font-bold text-xs">
                  {getDeviceNumber(deviceName)}-{channel.channelNumber.toString().padStart(2, '0')}
                </span>
              </div>

              {/* 状态文本 */}
              <div className="text-[10px] leading-tight opacity-80 truncate max-w-full px-1">
                {channel.stepStatus}
              </div>

              {/* 选中标记 */}
              {isSelected && (
                <div className="absolute -top-1 -right-1 w-4 h-4 bg-green-500 rounded-full flex items-center justify-center shadow-sm">
                  <Check className="w-2.5 h-2.5 text-white" />
                </div>
              )}

              {/* 运行状态指示器 */}
              {isRunning && (
                <div className="absolute -bottom-0.5 -right-0.5 w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
              )}
            </div>
          );
        })}
      </div>
    );
  };

  // 处理通道选择（通过ID）
  const handleChannelSelectById = (channelId: string) => {
    if (!config.multiSelect) {
      setInternalSelectedIds([channelId]);
      return;
    }

    setInternalSelectedIds(prev => {
      const isSelected = prev.includes(channelId);

      if (isSelected) {
        return prev.filter(id => id !== channelId);
      } else {
        // 检查最大选择数量限制
        if (config.maxSelection && prev.length >= config.maxSelection) {
          return prev;
        }
        return [...prev, channelId];
      }
    });
  };

  // 处理单选
  const handleChannelSelect = (channel: any) => {
    handleChannelSelectById(channel.id || channel.channelNumber?.toString());
  };

  // 处理多选切换
  const handleChannelToggle = (channel: any) => {
    const channelId = `${channel.deviceName}_${channel.channelNumber}`;

    if (!config.multiSelect) {
      setInternalSelectedIds([channelId]);
      return;
    }

    setInternalSelectedIds(prev => {
      const isSelected = prev.includes(channelId);

      if (isSelected) {
        return prev.filter(id => id !== channelId);
      } else {
        // 检查最大选择数量限制
        if (config.maxSelection && prev.length >= config.maxSelection) {
          return prev;
        }
        return [...prev, channelId];
      }
    });
  };

  // 全选/取消全选
  const handleSelectAll = () => {
    const onlineChannels = channels.filter(ch => ch.isEnabled && ch.stepStatus !== '离线');
    const onlineChannelIds = onlineChannels.map(ch => `${ch.deviceName}_${ch.channelNumber}`);

    if (internalSelectedIds.length === onlineChannelIds.length) {
      setInternalSelectedIds([]);
    } else {
      const limitedIds = config.maxSelection
        ? onlineChannelIds.slice(0, config.maxSelection)
        : onlineChannelIds;
      setInternalSelectedIds(limitedIds);
    }
  };

  // 确认选择
  const handleConfirm = () => {
    const result: ChannelSelectionResult = {
      selectedChannels,
      selectedChannelIds: internalSelectedIds,
      selectionCount: internalSelectedIds.length
    };
    
    onSelectionChange(result);
    setIsOpen(false);
  };

  // 取消选择
  const handleCancel = () => {
    setInternalSelectedIds(selectedChannelIds);
    setIsOpen(false);
  };

  const isSelectionValid = internalSelectedIds.length > 0;
  const isMaxSelectionReached = config.maxSelection && internalSelectedIds.length >= config.maxSelection;

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        {trigger || (
          <Button variant="outline" className={className}>
            <Zap className="h-4 w-4 mr-2" />
            {title}
            {selectedChannelIds.length > 0 && (
              <Badge variant="secondary" className="ml-2">
                {selectedChannelIds.length}
              </Badge>
            )}
          </Button>
        )}
      </DialogTrigger>
      
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-hidden flex flex-col">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <Zap className="h-5 w-5" />
            <span>{title}</span>
          </DialogTitle>
          <DialogDescription>{description}</DialogDescription>
        </DialogHeader>

        {/* 统计信息 */}
        <Card>
          <CardContent className="py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <div className="text-sm">
                  <span className="text-muted-foreground">设备:</span>
                  <span className="ml-1 font-medium">{devices.length}</span>
                </div>
                <div className="text-sm">
                  <span className="text-muted-foreground">总通道:</span>
                  <span className="ml-1 font-medium">{channels.length}</span>
                </div>
                <div className="text-sm">
                  <span className="text-muted-foreground">在线:</span>
                  <span className="ml-1 font-medium text-green-600">
                    {channels.filter(ch => ch.isEnabled && ch.stepStatus !== '离线').length}
                  </span>
                </div>
                <div className="text-sm">
                  <span className="text-muted-foreground">已选:</span>
                  <span className="ml-1 font-medium text-blue-600">
                    {internalSelectedIds.length}
                  </span>
                </div>
                {config.maxSelection && (
                  <div className="text-sm">
                    <span className="text-muted-foreground">限制:</span>
                    <span className="ml-1 font-medium">
                      {config.maxSelection}
                    </span>
                  </div>
                )}
              </div>

              <div className="flex items-center space-x-2">
                {/* 全选按钮 */}
                {config.multiSelect && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleSelectAll}
                    disabled={channels.length === 0}
                  >
                    {internalSelectedIds.length === channels.filter(ch => ch.isEnabled && ch.stepStatus !== '离线').length ? '取消全选' : '全选'}
                  </Button>
                )}
              </div>
            </div>

            {/* 状态统计 */}
            <div className="flex items-center space-x-2 mt-3">
              <Badge variant="outline" className="text-xs">
                在线: {channels.filter(ch => ch.isEnabled && ch.stepStatus !== '离线').length}
              </Badge>
              <Badge variant="outline" className="text-xs">
                运行: {channels.filter(ch => ['充电', '放电', '运行中'].includes(ch.stepStatus)).length}
              </Badge>
              <Badge variant="outline" className="text-xs">
                离线: {channels.filter(ch => !ch.isEnabled || ch.stepStatus === '离线').length}
              </Badge>
            </div>
          </CardContent>
        </Card>

        {/* 选择限制警告 */}
        {isMaxSelectionReached && (
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              已达到最大选择数量限制 ({config.maxSelection} 个通道)
            </AlertDescription>
          </Alert>
        )}

        {/* 设备通道手风琴 */}
        <div className="flex-1 overflow-y-auto">
          {devices.length === 0 ? (
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                没有找到可用的设备。请检查设备连接状态。
              </AlertDescription>
            </Alert>
          ) : (
            <Accordion type="multiple" className="w-full">
              {devices.map((device) => {
                const deviceChannels = channels.filter(ch => ch.deviceName === device.deviceName);
                const selectedCount = deviceChannels.filter(ch => {
                  const channelId = `${device.deviceName}_${ch.channelNumber}`;
                  return internalSelectedIds.includes(channelId);
                }).length;
                const onlineCount = deviceChannels.filter(ch => ch.isEnabled && ch.stepStatus !== '离线').length;

                return (
                  <AccordionItem key={device.deviceName} value={device.deviceName}>
                    <AccordionTrigger className="hover:no-underline">
                      <div className="flex items-center justify-between w-full mr-4">
                        <div className="flex items-center space-x-3">
                          <Monitor className="h-4 w-4" />
                          <span className="font-medium">{device.deviceName}</span>
                          <Badge variant={device.status === '运行中' ? 'default' : 'secondary'}>
                            {device.status === '运行中' ? '在线' : '离线'}
                          </Badge>
                        </div>
                        <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                          <span>{onlineCount}/{deviceChannels.length} 在线</span>
                          {selectedCount > 0 && (
                            <Badge variant="secondary" className="text-xs">
                              已选 {selectedCount}
                            </Badge>
                          )}
                        </div>
                      </div>
                    </AccordionTrigger>
                    <AccordionContent>
                      <div className="pt-2">
                        {renderCompactChannelGrid(deviceChannels, device.deviceName)}
                      </div>
                    </AccordionContent>
                  </AccordionItem>
                );
              })}
            </Accordion>
          )}
        </div>

        {/* 选择预览 */}
        {internalSelectedIds.length > 0 && (
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-lg">选择预览</CardTitle>
              <CardDescription>
                已选择 {internalSelectedIds.length} 个通道
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex flex-wrap gap-2 max-h-20 overflow-y-auto">
                {selectedChannels.map((channel) => {
                  const channelId = `${channel.deviceId}_${channel.channelNumber}`;
                  const deviceNumber = channel.deviceId.match(/(\d+)/)?.[1] || channel.deviceId.slice(-1);

                  return (
                    <Badge
                      key={channelId}
                      variant="secondary"
                      className="flex items-center space-x-1"
                    >
                      <span>{deviceNumber}-{channel.channelNumber.toString().padStart(2, '0')}</span>
                      <button
                        onClick={() => handleChannelSelectById(channelId)}
                        className="ml-1 hover:bg-red-500 hover:text-white rounded-full p-0.5"
                      >
                        <X className="h-3 w-3" />
                      </button>
                    </Badge>
                  );
                })}
              </div>
            </CardContent>
          </Card>
        )}

        {/* 操作按钮 */}
        <div className="flex items-center justify-between pt-4 border-t">
          <div className="text-sm text-muted-foreground">
            {config.multiSelect ? (
              `已选择 ${internalSelectedIds.length} 个通道`
            ) : (
              internalSelectedIds.length > 0 ? '已选择 1 个通道' : '请选择一个通道'
            )}
          </div>

          <div className="flex items-center space-x-2">
            <Button variant="outline" onClick={handleCancel}>
              取消
            </Button>
            <Button 
              onClick={handleConfirm}
              disabled={!isSelectionValid}
            >
              <Check className="h-4 w-4 mr-2" />
              确认选择
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}

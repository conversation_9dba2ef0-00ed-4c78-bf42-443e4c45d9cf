'use client';

import React, { useState } from 'react';
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
} from '@/components/ui/sheet';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { 
  Monitor, 
  Gauge, 
  Bell, 
  Activity,
  Save,
  RotateCcw
} from 'lucide-react';
import { useTranslations } from 'next-intl';

import { DisplaySettingsPanel } from './DisplaySettingsPanel';
import { UnitSettingsPanel } from './UnitSettingsPanel';
import { NotificationSettingsPanel } from './NotificationSettingsPanel';
import { MonitoringSettingsPanel } from './MonitoringSettingsPanel';

import { SystemSettings, DisplaySettings, UnitSettings } from '@/lib/types';

interface SettingsSheetProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  settings: SystemSettings;
  onSettingsChange: (settings: SystemSettings) => void;
}

export function SettingsSheet({
  open,
  onOpenChange,
  settings,
  onSettingsChange
}: SettingsSheetProps) {
  const t = useTranslations('settings');
  const [localSettings, setLocalSettings] = useState<SystemSettings>(settings);
  const [hasChanges, setHasChanges] = useState(false);

  // 更新本地设置
  const updateLocalSettings = (newSettings: Partial<SystemSettings>) => {
    const updated = { ...localSettings, ...newSettings };
    setLocalSettings(updated);
    setHasChanges(true);
  };

  // 保存设置
  const handleSave = () => {
    onSettingsChange(localSettings);
    setHasChanges(false);
  };

  // 重置设置
  const handleReset = () => {
    setLocalSettings(settings);
    setHasChanges(false);
  };

  // 恢复默认设置
  const handleRestoreDefaults = () => {
    const defaultSettings: SystemSettings = {
      display: {
        theme: 'system',
        language: 'zh',
        compactMode: false,
        showAnimations: true,
        fontSize: 'medium'
      },
      units: {
        voltage: 'V',
        current: 'A',
        power: 'W',
        capacity: 'Ah',
        energy: 'Wh',
        temperature: '°C',
        resistance: 'Ω',
        time: 's',
        decimalPlaces: 2
      },
      notifications: {
        enabled: true,
        sound: true,
        desktop: true,
        email: false
      },
      monitoring: {
        refreshInterval: 5,
        dataRetention: 30,
        alertThresholds: {
          voltage: { min: 2.5, max: 4.5 },
          current: { min: -10, max: 10 },
          temperature: { min: -10, max: 60 }
        }
      }
    };
    
    setLocalSettings(defaultSettings);
    setHasChanges(true);
  };

  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      <SheetContent className="w-[600px] sm:w-[700px] flex flex-col max-h-screen">
        <SheetHeader className="pb-6 flex-shrink-0">
          <SheetTitle className="flex items-center gap-2">
            <Monitor className="h-5 w-5" />
            {t('title')}
          </SheetTitle>
          <SheetDescription>
            {t('description')}
          </SheetDescription>
        </SheetHeader>

        <div className="flex-1 overflow-y-auto space-y-6">
          <Tabs defaultValue="display" className="w-full">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="display" className="flex items-center gap-2">
                <Monitor className="h-4 w-4" />
                <span className="hidden sm:inline">{t('tabs.display')}</span>
              </TabsTrigger>
              <TabsTrigger value="units" className="flex items-center gap-2">
                <Gauge className="h-4 w-4" />
                <span className="hidden sm:inline">{t('tabs.units')}</span>
              </TabsTrigger>
              <TabsTrigger value="notifications" className="flex items-center gap-2">
                <Bell className="h-4 w-4" />
                <span className="hidden sm:inline">{t('tabs.notifications')}</span>
              </TabsTrigger>
              <TabsTrigger value="monitoring" className="flex items-center gap-2">
                <Activity className="h-4 w-4" />
                <span className="hidden sm:inline">{t('tabs.monitoring')}</span>
              </TabsTrigger>
            </TabsList>

            <div className="mt-6 px-1">
              <TabsContent value="display" className="space-y-4 mt-0">
                <DisplaySettingsPanel
                  settings={localSettings.display}
                  onSettingsChange={(display) => updateLocalSettings({ display })}
                />
              </TabsContent>

              <TabsContent value="units" className="space-y-4 mt-0">
                <UnitSettingsPanel
                  settings={localSettings.units}
                  onSettingsChange={(units) => updateLocalSettings({ units })}
                />
              </TabsContent>

              <TabsContent value="notifications" className="space-y-4 mt-0">
                <NotificationSettingsPanel
                  settings={localSettings.notifications}
                  onSettingsChange={(notifications) => updateLocalSettings({ notifications })}
                />
              </TabsContent>

              <TabsContent value="monitoring" className="space-y-4 mt-0">
                <MonitoringSettingsPanel
                  settings={localSettings.monitoring}
                  onSettingsChange={(monitoring) => updateLocalSettings({ monitoring })}
                />
              </TabsContent>
            </div>
          </Tabs>
        </div>

        {/* 底部操作按钮 */}
        <div className="flex items-center justify-between mt-8 pt-6 border-t flex-shrink-0">
          <Button
            variant="outline"
            onClick={handleRestoreDefaults}
            className="flex items-center gap-2"
          >
            <RotateCcw className="h-4 w-4" />
            {t('actions.restoreDefaults')}
          </Button>

          <div className="flex gap-2">
            <Button
              variant="outline"
              onClick={handleReset}
              disabled={!hasChanges}
            >
              {t('actions.reset')}
            </Button>
            <Button
              onClick={handleSave}
              disabled={!hasChanges}
              className="flex items-center gap-2"
            >
              <Save className="h-4 w-4" />
              {t('actions.save')}
            </Button>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}

'use client';

import { useTranslations } from 'next-intl';
import { useEventStore } from '@/stores/eventStore';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Separator } from '@/components/ui/separator';
import {
  Filter,
  X,
  RotateCcw,
} from 'lucide-react';
import { EventLevel, EventCategory } from '@/types/events';
import { cn } from '@/lib/utils';

interface EventFiltersProps {
  className?: string;
}

export function EventFilters({ className }: EventFiltersProps) {
  const t = useTranslations('events');

  const {
    filter,
    setFilter,
    clearFilter,
  } = useEventStore();

  // 事件级别选项
  const levelOptions = [
    { value: EventLevel.INFO, label: '信息', color: 'text-blue-600' },
    { value: EventLevel.WARNING, label: '警告', color: 'text-yellow-600' },
    { value: EventLevel.ERROR, label: '错误', color: 'text-red-600' },
    { value: EventLevel.CRITICAL, label: '严重', color: 'text-red-800' },
  ];

  // 事件类别选项
  const categoryOptions = [
    { value: EventCategory.DEVICE, label: '设备' },
    { value: EventCategory.CHANNEL, label: '通道' },
    { value: EventCategory.USER, label: '用户' },
    { value: EventCategory.SYSTEM, label: '系统' },
  ];

  // 处理级别筛选
  const handleLevelChange = (level: EventLevel, checked: boolean) => {
    const currentLevels = filter.levels || [];
    const newLevels = checked
      ? [...currentLevels, level]
      : currentLevels.filter(l => l !== level);

    setFilter({ levels: newLevels.length > 0 ? newLevels : undefined });
  };

  // 处理类别筛选
  const handleCategoryChange = (category: EventCategory, checked: boolean) => {
    const currentCategories = filter.categories || [];
    const newCategories = checked
      ? [...currentCategories, category]
      : currentCategories.filter(c => c !== category);

    setFilter({ categories: newCategories.length > 0 ? newCategories : undefined });
  };

  // 获取活跃的筛选条件
  const getActiveFilters = () => {
    const filters = [];

    // 级别筛选
    filter.levels?.forEach(level => {
      const levelOption = levelOptions.find(opt => opt.value === level);
      if (levelOption) {
        filters.push({
          type: 'level',
          label: levelOption.label,
          value: level,
        });
      }
    });

    // 类别筛选
    filter.categories?.forEach(category => {
      const categoryOption = categoryOptions.find(opt => opt.value === category);
      if (categoryOption) {
        filters.push({
          type: 'category',
          label: categoryOption.label,
          value: category,
        });
      }
    });

    // 确认状态筛选
    if (filter.acknowledgedOnly) {
      filters.push({
        type: 'acknowledged',
        label: '已确认',
        value: 'acknowledged',
      });
    }

    if (filter.unacknowledgedOnly) {
      filters.push({
        type: 'acknowledged',
        label: '未确认',
        value: 'unacknowledged',
      });
    }

    return filters;
  };

  const activeFilters = getActiveFilters();

  // 移除单个筛选条件
  const removeFilter = (filterToRemove: any) => {
    switch (filterToRemove.type) {
      case 'level':
        const newLevels = filter.levels?.filter(level => level !== filterToRemove.value);
        setFilter({ levels: newLevels?.length ? newLevels : undefined });
        break;
      case 'category':
        const newCategories = filter.categories?.filter(category => category !== filterToRemove.value);
        setFilter({ categories: newCategories?.length ? newCategories : undefined });
        break;
      case 'acknowledged':
        setFilter({
          acknowledgedOnly: filterToRemove.value === 'acknowledged' ? false : filter.acknowledgedOnly,
          unacknowledgedOnly: filterToRemove.value === 'unacknowledged' ? false : filter.unacknowledgedOnly,
        });
        break;
    }
  };

  return (
    <div className={cn("flex items-center gap-2 flex-wrap", className)}>
      {/* 筛选设置按钮 */}
      <Popover>
        <PopoverTrigger asChild>
          <Button variant="outline" size="sm">
            <Filter className="h-4 w-4 mr-2" />
            筛选
            {activeFilters.length > 0 && (
              <span className="ml-2 bg-primary text-primary-foreground rounded-full px-2 py-0.5 text-xs">
                {activeFilters.length}
              </span>
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-96" align="start">
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <h4 className="font-medium text-sm">筛选条件</h4>
              {activeFilters.length > 0 && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={clearFilter}
                >
                  <RotateCcw className="h-4 w-4 mr-1" />
                  清除
                </Button>
              )}
            </div>

            {/* 横向分区布局 */}
            <div className="flex gap-4">
              {/* 事件级别筛选 */}
              <div className="flex-1 space-y-2">
                <h5 className="text-xs font-medium text-muted-foreground">事件级别</h5>
                <div className="space-y-1">
                  {levelOptions.map((option) => (
                    <div key={option.value} className="flex items-center space-x-2">
                      <Checkbox
                        id={`level-${option.value}`}
                        checked={filter.levels?.includes(option.value) || false}
                        onCheckedChange={(checked) =>
                          handleLevelChange(option.value, checked as boolean)
                        }
                        className="h-3 w-3"
                      />
                      <label
                        htmlFor={`level-${option.value}`}
                        className={cn("text-xs cursor-pointer", option.color)}
                      >
                        {option.label}
                      </label>
                    </div>
                  ))}
                </div>
              </div>

              <Separator orientation="vertical" className="h-20" />

              {/* 事件类别筛选 */}
              <div className="flex-1 space-y-2">
                <h5 className="text-xs font-medium text-muted-foreground">事件类别</h5>
                <div className="space-y-1">
                  {categoryOptions.map((option) => (
                    <div key={option.value} className="flex items-center space-x-2">
                      <Checkbox
                        id={`category-${option.value}`}
                        checked={filter.categories?.includes(option.value) || false}
                        onCheckedChange={(checked) =>
                          handleCategoryChange(option.value, checked as boolean)
                        }
                        className="h-3 w-3"
                      />
                      <label htmlFor={`category-${option.value}`} className="text-xs cursor-pointer">
                        {option.label}
                      </label>
                    </div>
                  ))}
                </div>
              </div>

              <Separator orientation="vertical" className="h-20" />

              {/* 确认状态筛选 */}
              <div className="flex-1 space-y-2">
                <h5 className="text-xs font-medium text-muted-foreground">确认状态</h5>
                <div className="space-y-1">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="acknowledged-only"
                      checked={filter.acknowledgedOnly || false}
                      onCheckedChange={(checked) =>
                        setFilter({
                          acknowledgedOnly: checked as boolean,
                          unacknowledgedOnly: false,
                        })
                      }
                      className="h-3 w-3"
                    />
                    <label htmlFor="acknowledged-only" className="text-xs cursor-pointer">
                      已确认
                    </label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="unacknowledged-only"
                      checked={filter.unacknowledgedOnly || false}
                      onCheckedChange={(checked) =>
                        setFilter({
                          unacknowledgedOnly: checked as boolean,
                          acknowledgedOnly: false,
                        })
                      }
                      className="h-3 w-3"
                    />
                    <label htmlFor="unacknowledged-only" className="text-xs cursor-pointer">
                      未确认
                    </label>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </PopoverContent>
      </Popover>

      {/* 活跃筛选条件标签 */}
      {activeFilters.map((filter, index) => (
        <Badge
          key={`${filter.type}-${filter.value}-${index}`}
          variant="secondary"
          className="h-7 px-2 py-1 text-xs"
        >
          {filter.label}
          <button
            onClick={() => removeFilter(filter)}
            className="ml-1 hover:bg-muted-foreground/20 rounded-full p-0.5"
          >
            <X className="h-3 w-3" />
          </button>
        </Badge>
      ))}
    </div>
  );
}

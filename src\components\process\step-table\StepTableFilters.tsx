'use client';

import React from 'react';
import { Table } from '@tanstack/react-table';
import { Search, Filter, X, SlidersHorizontal } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Checkbox } from '@/components/ui/checkbox';

import { Step, StepStatus, StepMode } from '@/lib/process/types/step';
import { STEP_STATUS_OPTIONS, STEP_MODE_OPTIONS } from '@/lib/process/constants/stepConstants';

interface StepTableFiltersProps {
  table: Table<Step>;
  globalFilter: string;
  onGlobalFilterChange: (value: string) => void;
  statusFilter: StepStatus[];
  onStatusFilterChange: (statuses: StepStatus[]) => void;
  modeFilter: StepMode[];
  onModeFilterChange: (modes: StepMode[]) => void;
  onClearFilters: () => void;
}

export function StepTableFilters({
  table,
  globalFilter,
  onGlobalFilterChange,
  statusFilter,
  onStatusFilterChange,
  modeFilter,
  onModeFilterChange,
  onClearFilters
}: StepTableFiltersProps) {
  
  const hasActiveFilters = globalFilter || statusFilter.length > 0 || modeFilter.length > 0;
  const activeFilterCount = (globalFilter ? 1 : 0) + (statusFilter.length > 0 ? 1 : 0) + (modeFilter.length > 0 ? 1 : 0);

  const handleStatusChange = (status: StepStatus, checked: boolean) => {
    if (checked) {
      onStatusFilterChange([...statusFilter, status]);
    } else {
      onStatusFilterChange(statusFilter.filter(s => s !== status));
    }
  };

  const handleModeChange = (mode: StepMode, checked: boolean) => {
    if (checked) {
      onModeFilterChange([...modeFilter, mode]);
    } else {
      onModeFilterChange(modeFilter.filter(m => m !== mode));
    }
  };

  return (
    <div className="flex items-center justify-between space-x-4">
      {/* 搜索框 */}
      <div className="flex items-center space-x-2 flex-1 max-w-sm">
        <div className="relative">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="搜索工步名称..."
            value={globalFilter}
            onChange={(e) => onGlobalFilterChange(e.target.value)}
            className="pl-8"
          />
        </div>
      </div>

      {/* 筛选器 */}
      <div className="flex items-center space-x-2">
        {/* 活跃筛选器指示 */}
        {hasActiveFilters && (
          <Badge variant="secondary" className="text-xs">
            {activeFilterCount} 个筛选器
          </Badge>
        )}

        {/* 高级筛选器 */}
        <Popover>
          <PopoverTrigger asChild>
            <Button variant="outline" size="sm">
              <SlidersHorizontal className="h-4 w-4 mr-2" />
              筛选
              {hasActiveFilters && (
                <Badge variant="destructive" className="ml-2 h-5 w-5 rounded-full p-0 text-xs">
                  {activeFilterCount}
                </Badge>
              )}
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-80" align="end">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h4 className="font-medium">筛选条件</h4>
                {hasActiveFilters && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={onClearFilters}
                    className="h-8 px-2 text-xs"
                  >
                    <X className="h-3 w-3 mr-1" />
                    清除
                  </Button>
                )}
              </div>

              <Separator />

              {/* 工步状态筛选 */}
              <div className="space-y-3">
                <Label className="text-sm font-medium">工步状态</Label>
                <div className="space-y-2">
                  {STEP_STATUS_OPTIONS.map((option) => (
                    <div key={option.value} className="flex items-center space-x-2">
                      <Checkbox
                        id={`status-${option.value}`}
                        checked={statusFilter.includes(option.value as StepStatus)}
                        onCheckedChange={(checked) => 
                          handleStatusChange(option.value as StepStatus, checked as boolean)
                        }
                      />
                      <Label 
                        htmlFor={`status-${option.value}`}
                        className="text-sm flex items-center space-x-2 cursor-pointer"
                      >
                        <div 
                          className="w-3 h-3 rounded-full" 
                          style={{ backgroundColor: option.color }}
                        />
                        <span>{option.label}</span>
                      </Label>
                    </div>
                  ))}
                </div>
              </div>

              <Separator />

              {/* 工步模式筛选 */}
              <div className="space-y-3">
                <Label className="text-sm font-medium">工步模式</Label>
                <div className="space-y-2 max-h-40 overflow-y-auto">
                  {STEP_MODE_OPTIONS.map((option) => (
                    <div key={option.value} className="flex items-center space-x-2">
                      <Checkbox
                        id={`mode-${option.value}`}
                        checked={modeFilter.includes(option.value as StepMode)}
                        onCheckedChange={(checked) => 
                          handleModeChange(option.value as StepMode, checked as boolean)
                        }
                      />
                      <Label 
                        htmlFor={`mode-${option.value}`}
                        className="text-sm cursor-pointer"
                      >
                        {option.label}
                      </Label>
                    </div>
                  ))}
                </div>
              </div>

              {/* 筛选结果统计 */}
              {hasActiveFilters && (
                <>
                  <Separator />
                  <div className="text-xs text-muted-foreground">
                    显示 {table.getFilteredRowModel().rows.length} / {table.getCoreRowModel().rows.length} 个工步
                  </div>
                </>
              )}
            </div>
          </PopoverContent>
        </Popover>

        {/* 列可见性控制 */}
        <Popover>
          <PopoverTrigger asChild>
            <Button variant="outline" size="sm">
              <Filter className="h-4 w-4 mr-2" />
              列
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-56" align="end">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h4 className="font-medium">显示列</h4>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => table.toggleAllColumnsVisible(true)}
                  className="h-8 px-2 text-xs"
                >
                  全部显示
                </Button>
              </div>

              <Separator />

              <div className="space-y-2 max-h-60 overflow-y-auto">
                {table
                  .getAllColumns()
                  .filter((column) => column.getCanHide())
                  .map((column) => {
                    const columnDef = column.columnDef;
                    const header = typeof columnDef.header === 'string' 
                      ? columnDef.header 
                      : columnDef.id || column.id;
                    
                    return (
                      <div key={column.id} className="flex items-center space-x-2">
                        <Checkbox
                          id={`column-${column.id}`}
                          checked={column.getIsVisible()}
                          onCheckedChange={(value) => column.toggleVisibility(!!value)}
                        />
                        <Label 
                          htmlFor={`column-${column.id}`}
                          className="text-sm cursor-pointer"
                        >
                          {header}
                        </Label>
                      </div>
                    );
                  })}
              </div>
            </div>
          </PopoverContent>
        </Popover>

        {/* 清除所有筛选器 */}
        {hasActiveFilters && (
          <Button
            variant="ghost"
            size="sm"
            onClick={onClearFilters}
            className="h-8 px-2"
          >
            <X className="h-4 w-4" />
          </Button>
        )}
      </div>
    </div>
  );
}

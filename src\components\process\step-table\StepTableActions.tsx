'use client';

import React, { useState } from 'react';
import { Table } from '@tanstack/react-table';
import { 
  Trash2, 
  Copy, 
  Download, 
  Move, 
  MoreHorizontal, 
  AlertTriangle,
  CheckCircle
} from 'lucide-react';

import { Button } from '@/components/ui/button';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';

import { Step, StepBatchOperation } from '@/lib/process/types/step';
import { BATCH_OPERATION_OPTIONS } from '@/lib/process/constants/stepConstants';

interface StepTableActionsProps {
  table: Table<Step>;
  selectedSteps: Step[];
  onBatchOperation: (operation: StepBatchOperation) => void;
  isLoading?: boolean;
}

export function StepTableActions({ 
  table, 
  selectedSteps, 
  onBatchOperation, 
  isLoading = false 
}: StepTableActionsProps) {
  
  const [confirmDialog, setConfirmDialog] = useState<{
    open: boolean;
    operation: StepBatchOperation | null;
    title: string;
    description: string;
    variant: 'default' | 'destructive';
  }>({
    open: false,
    operation: null,
    title: '',
    description: '',
    variant: 'default'
  });

  const selectedCount = selectedSteps.length;
  const hasSelection = selectedCount > 0;

  const handleBatchAction = (action: string) => {
    if (!hasSelection) return;

    const stepIds = selectedSteps.map(step => step.id);
    const operation: StepBatchOperation = {
      action: action as any,
      stepIds
    };

    switch (action) {
      case 'delete':
        setConfirmDialog({
          open: true,
          operation,
          title: '确认删除',
          description: `确定要删除选中的 ${selectedCount} 个工步吗？此操作不可撤销。`,
          variant: 'destructive'
        });
        break;

      case 'duplicate':
        setConfirmDialog({
          open: true,
          operation,
          title: '确认复制',
          description: `确定要复制选中的 ${selectedCount} 个工步吗？将创建相同配置的新工步。`,
          variant: 'default'
        });
        break;

      case 'export':
        // 直接执行导出，不需要确认
        onBatchOperation(operation);
        break;

      case 'move':
        setConfirmDialog({
          open: true,
          operation,
          title: '移动工步',
          description: `选择要移动 ${selectedCount} 个工步的目标位置。`,
          variant: 'default'
        });
        break;

      default:
        break;
    }
  };

  const handleConfirm = () => {
    if (confirmDialog.operation) {
      onBatchOperation(confirmDialog.operation);
      setConfirmDialog({
        open: false,
        operation: null,
        title: '',
        description: '',
        variant: 'default'
      });
    }
  };

  const handleCancel = () => {
    setConfirmDialog({
      open: false,
      operation: null,
      title: '',
      description: '',
      variant: 'default'
    });
  };

  const getOperationIcon = (action: string) => {
    switch (action) {
      case 'delete':
        return <Trash2 className="h-4 w-4" />;
      case 'duplicate':
        return <Copy className="h-4 w-4" />;
      case 'export':
        return <Download className="h-4 w-4" />;
      case 'move':
        return <Move className="h-4 w-4" />;
      default:
        return <MoreHorizontal className="h-4 w-4" />;
    }
  };

  const getOperationColor = (action: string) => {
    switch (action) {
      case 'delete':
        return 'text-red-600 hover:text-red-700';
      default:
        return '';
    }
  };

  return (
    <>
      <div className="flex items-center justify-between">
        {/* 选择状态显示 */}
        <div className="flex items-center space-x-4">
          {hasSelection ? (
            <div className="flex items-center space-x-2">
              <Badge variant="secondary">
                已选择 {selectedCount} 个工步
              </Badge>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => table.toggleAllRowsSelected(false)}
                className="h-8 px-2 text-xs"
              >
                取消选择
              </Button>
            </div>
          ) : (
            <div className="text-sm text-muted-foreground">
              共 {table.getCoreRowModel().rows.length} 个工步
            </div>
          )}
        </div>

        {/* 批量操作按钮 */}
        {hasSelection && (
          <div className="flex items-center space-x-2">
            {/* 快捷操作按钮 */}
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleBatchAction('duplicate')}
              disabled={isLoading}
            >
              <Copy className="h-4 w-4 mr-1" />
              复制
            </Button>

            <Button
              variant="outline"
              size="sm"
              onClick={() => handleBatchAction('export')}
              disabled={isLoading}
            >
              <Download className="h-4 w-4 mr-1" />
              导出
            </Button>

            {/* 更多操作下拉菜单 */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm" disabled={isLoading}>
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuLabel>批量操作</DropdownMenuLabel>
                <DropdownMenuSeparator />
                
                {BATCH_OPERATION_OPTIONS.map((option) => (
                  <DropdownMenuItem
                    key={option.value}
                    onClick={() => handleBatchAction(option.value)}
                    className={getOperationColor(option.value)}
                  >
                    {getOperationIcon(option.value)}
                    <span className="ml-2">{option.label}</span>
                  </DropdownMenuItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        )}
      </div>

      {/* 确认对话框 */}
      <Dialog open={confirmDialog.open} onOpenChange={(open) => !open && handleCancel()}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle className="flex items-center space-x-2">
              {confirmDialog.variant === 'destructive' ? (
                <AlertTriangle className="h-5 w-5 text-red-500" />
              ) : (
                <CheckCircle className="h-5 w-5 text-blue-500" />
              )}
              <span>{confirmDialog.title}</span>
            </DialogTitle>
            <DialogDescription>
              {confirmDialog.description}
            </DialogDescription>
          </DialogHeader>

          {/* 选中工步预览 */}
          {hasSelection && (
            <div className="space-y-3">
              <div className="text-sm font-medium">选中的工步：</div>
              <div className="max-h-40 overflow-y-auto border rounded-md p-3 space-y-2">
                {selectedSteps.slice(0, 5).map((step, index) => (
                  <div key={step.id} className="flex items-center justify-between text-sm">
                    <span className="font-medium">{step.name}</span>
                    <div className="flex items-center space-x-2">
                      <Badge variant="outline" className="text-xs">
                        {step.status}
                      </Badge>
                      <Badge variant="secondary" className="text-xs">
                        {step.mode}
                      </Badge>
                    </div>
                  </div>
                ))}
                {selectedSteps.length > 5 && (
                  <div className="text-center text-xs text-muted-foreground">
                    还有 {selectedSteps.length - 5} 个工步...
                  </div>
                )}
              </div>
            </div>
          )}

          {/* 警告信息 */}
          {confirmDialog.variant === 'destructive' && (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                此操作不可撤销，请确认后再继续。
              </AlertDescription>
            </Alert>
          )}

          {/* 操作按钮 */}
          <div className="flex items-center justify-end space-x-2">
            <Button variant="outline" onClick={handleCancel}>
              取消
            </Button>
            <Button
              variant={confirmDialog.variant}
              onClick={handleConfirm}
              disabled={isLoading}
            >
              {isLoading ? '处理中...' : '确认'}
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
}

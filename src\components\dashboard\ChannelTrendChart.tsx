'use client';

import React from 'react';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ChannelTrendData } from '@/lib/types/dashboard';
import { format } from 'date-fns';

interface ChannelTrendChartProps {
  data: ChannelTrendData[];
  className?: string;
}

const CustomTooltip = ({ active, payload, label }: any) => {
  if (active && payload && payload.length) {
    const time = format(new Date(label), 'HH:mm');
    return (
      <div className="bg-background border rounded-lg shadow-lg p-3">
        <p className="font-medium mb-2">{time}</p>
        {payload.map((entry: any, index: number) => (
          <p key={index} className="text-sm" style={{ color: entry.color }}>
            {entry.name}: {entry.value} 个
          </p>
        ))}
      </div>
    );
  }
  return null;
};

const formatXAxisLabel = (tickItem: string) => {
  return format(new Date(tickItem), 'HH:mm');
};

export function ChannelTrendChart({ data, className }: ChannelTrendChartProps) {
  const latestData = data[data.length - 1];
  const totalChannels = latestData ? latestData.online + latestData.running + latestData.idle + latestData.error : 0;
  
  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          通道状态趋势
          <Badge variant="outline">{totalChannels} 个通道</Badge>
        </CardTitle>
        <CardDescription>
          过去24小时通道状态变化趋势
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="h-[300px]">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
              <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />
              <XAxis 
                dataKey="timestamp" 
                tickFormatter={formatXAxisLabel}
                className="text-xs"
                interval="preserveStartEnd"
              />
              <YAxis className="text-xs" />
              <Tooltip content={<CustomTooltip />} />
              <Legend />
              <Line 
                type="monotone" 
                dataKey="online" 
                stroke="#16a34a" 
                strokeWidth={2}
                name="在线"
                dot={false}
              />
              <Line 
                type="monotone" 
                dataKey="running" 
                stroke="#2563eb" 
                strokeWidth={2}
                name="运行中"
                dot={false}
              />
              <Line 
                type="monotone" 
                dataKey="idle" 
                stroke="#d97706" 
                strokeWidth={2}
                name="空闲"
                dot={false}
              />
              <Line 
                type="monotone" 
                dataKey="error" 
                stroke="#dc2626" 
                strokeWidth={2}
                name="故障"
                dot={false}
              />
            </LineChart>
          </ResponsiveContainer>
        </div>
        
        {/* 当前状态统计 */}
        {latestData && (
          <div className="grid grid-cols-4 gap-4 mt-4">
            <div className="text-center">
              <div className="text-lg font-semibold text-green-600">
                {latestData.online}
              </div>
              <div className="text-xs text-muted-foreground">在线</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-semibold text-blue-600">
                {latestData.running}
              </div>
              <div className="text-xs text-muted-foreground">运行中</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-semibold text-orange-600">
                {latestData.idle}
              </div>
              <div className="text-xs text-muted-foreground">空闲</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-semibold text-red-600">
                {latestData.error}
              </div>
              <div className="text-xs text-muted-foreground">故障</div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

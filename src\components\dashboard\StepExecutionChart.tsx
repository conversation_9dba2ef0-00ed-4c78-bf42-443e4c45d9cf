'use client';

import React from 'react';
import { <PERSON><PERSON>hart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { StepExecutionData } from '@/lib/types/dashboard';

interface StepExecutionChartProps {
  data: StepExecutionData[];
  className?: string;
}

const CustomTooltip = ({ active, payload, label }: any) => {
  if (active && payload && payload.length) {
    const data = payload[0].payload;
    const completionRate = ((data.completed / data.total) * 100).toFixed(1);
    
    return (
      <div className="bg-background border rounded-lg shadow-lg p-3">
        <p className="font-medium mb-2">{label}</p>
        <div className="space-y-1">
          <p className="text-sm text-green-600">已完成: {data.completed}</p>
          <p className="text-sm text-blue-600">运行中: {data.running}</p>
          <p className="text-sm text-red-600">失败: {data.failed}</p>
          <p className="text-sm text-muted-foreground">总计: {data.total}</p>
          <p className="text-sm font-medium">完成率: {completionRate}%</p>
        </div>
      </div>
    );
  }
  return null;
};

export function StepExecutionChart({ data, className }: StepExecutionChartProps) {
  const totalExecutions = data.reduce((sum, item) => sum + item.total, 0);
  const totalCompleted = data.reduce((sum, item) => sum + item.completed, 0);
  const overallCompletionRate = totalExecutions > 0 ? ((totalCompleted / totalExecutions) * 100).toFixed(1) : '0';
  
  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          工步执行统计
          <Badge variant="outline">完成率 {overallCompletionRate}%</Badge>
        </CardTitle>
        <CardDescription>
          各类工步的执行情况统计
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="h-[300px]">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={data} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
              <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />
              <XAxis 
                dataKey="stepName" 
                className="text-xs"
                angle={-45}
                textAnchor="end"
                height={80}
              />
              <YAxis className="text-xs" />
              <Tooltip content={<CustomTooltip />} />
              <Legend />
              <Bar 
                dataKey="completed" 
                stackId="a" 
                fill="#16a34a" 
                name="已完成"
                radius={[0, 0, 0, 0]}
              />
              <Bar 
                dataKey="running" 
                stackId="a" 
                fill="#2563eb" 
                name="运行中"
                radius={[0, 0, 0, 0]}
              />
              <Bar 
                dataKey="failed" 
                stackId="a" 
                fill="#dc2626" 
                name="失败"
                radius={[2, 2, 0, 0]}
              />
            </BarChart>
          </ResponsiveContainer>
        </div>
        
        {/* 统计摘要 */}
        <div className="grid grid-cols-3 gap-4 mt-4">
          <div className="text-center">
            <div className="text-lg font-semibold text-green-600">
              {totalCompleted}
            </div>
            <div className="text-xs text-muted-foreground">总完成数</div>
          </div>
          <div className="text-center">
            <div className="text-lg font-semibold text-blue-600">
              {data.reduce((sum, item) => sum + item.running, 0)}
            </div>
            <div className="text-xs text-muted-foreground">运行中</div>
          </div>
          <div className="text-center">
            <div className="text-lg font-semibold text-red-600">
              {data.reduce((sum, item) => sum + item.failed, 0)}
            </div>
            <div className="text-xs text-muted-foreground">失败数</div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

'use client';

import React, { useState } from 'react';
import { User, Calendar, FileText, Check, ArrowRight, ArrowLeft, Settings } from 'lucide-react';

import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';

import { Alert, AlertDescription } from '@/components/ui/alert';

import { CreateProcessData } from '@/lib/process/types/process';
import { useAuth } from '@/lib/auth/hooks/useAuth';
import { GlobalParameters, DEFAULT_GLOBAL_PARAMETERS } from '@/lib/process/types/globalParameters';
import { GlobalParametersDialog } from '../global-parameters/GlobalParametersDialog';

interface ProcessWizardProps {
  trigger?: React.ReactNode;
  onCreateProcess: (data: CreateProcessData) => Promise<void>;
  isLoading?: boolean;
  className?: string;
}

interface WizardStep {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
}

const wizardSteps: WizardStep[] = [
  {
    id: 'basic',
    title: '基本信息',
    description: '设置工艺流程的基本信息',
    icon: <FileText className="h-5 w-5" />
  },
  {
    id: 'globalParams',
    title: '全局参数设置',
    description: '配置保护参数、显示设置等全局参数',
    icon: <Settings className="h-5 w-5" />
  },
  {
    id: 'review',
    title: '确认创建',
    description: '检查信息并创建工艺流程',
    icon: <Check className="h-5 w-5" />
  }
];

export function ProcessWizard({
  trigger,
  onCreateProcess,
  isLoading = false,
  className
}: ProcessWizardProps) {
  const { user, getUserDisplayName } = useAuth();

  const [isOpen, setIsOpen] = useState(false);
  const [currentStep, setCurrentStep] = useState(0);
  const [formData, setFormData] = useState<Partial<CreateProcessData>>({
    name: '',
    description: '',
    creatorName: getUserDisplayName(),
    creatorId: user?.id || '',
    steps: [], // 空的工步数组
    metadata: {}
  });
  const [globalParameters, setGlobalParameters] = useState<GlobalParameters>(DEFAULT_GLOBAL_PARAMETERS);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // 如果没有trigger，说明是内嵌模式
  const isEmbedded = !trigger;

  const handleNext = () => {
    if (validateCurrentStep()) {
      setCurrentStep(prev => Math.min(prev + 1, wizardSteps.length - 1));
    }
  };

  const handlePrevious = () => {
    setCurrentStep(prev => Math.max(prev - 1, 0));
  };

  const handleInputChange = (field: keyof CreateProcessData, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    
    // 清除对应字段的错误
    if (errors[field]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
  };

  const validateCurrentStep = (): boolean => {
    const newErrors: Record<string, string> = {};

    switch (currentStep) {
      case 0: // 基本信息
        if (!formData.name?.trim()) {
          newErrors.name = '工艺流程名称不能为空';
        } else if (formData.name.length > 100) {
          newErrors.name = '工艺流程名称不能超过100个字符';
        }
        break;

      case 1: // 确认创建
        // 最终验证
        if (!formData.name?.trim()) {
          newErrors.name = '工艺流程名称不能为空';
        }
        if (!formData.creatorName?.trim()) {
          newErrors.creatorName = '创建者姓名不能为空';
        }
        break;
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleCreate = async () => {
    if (!validateCurrentStep()) {
      return;
    }

    try {
      await onCreateProcess({
        name: formData.name!,
        description: formData.description,
        creatorName: formData.creatorName!,
        creatorId: formData.creatorId,
        steps: [], // 创建空的工艺流程
        globalParameters, // 包含全局参数
        metadata: {
          ...formData.metadata,
          createdViaWizard: true,
          wizardVersion: '1.0'
        }
      });

      // 重置表单并关闭对话框
      setFormData({
        name: '',
        description: '',
        creatorName: getUserDisplayName(),
        creatorId: user?.id || '',
        steps: [],
        metadata: {}
      });
      setGlobalParameters(DEFAULT_GLOBAL_PARAMETERS);
      setCurrentStep(0);
      setErrors({});
      setIsOpen(false);
    } catch (error) {
      console.error('创建工艺流程失败:', error);
      setErrors({ general: '创建工艺流程失败，请重试' });
    }
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 0: // 基本信息
        return (
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="name">工艺流程名称 *</Label>
              <Input
                id="name"
                value={formData.name || ''}
                onChange={(e) => handleInputChange('name', e.target.value)}
                placeholder="请输入工艺流程名称"
                className={errors.name ? 'border-red-500' : ''}
              />
              {errors.name && (
                <p className="text-sm text-red-500">{errors.name}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">描述信息</Label>
              <Textarea
                id="description"
                value={formData.description || ''}
                onChange={(e) => handleInputChange('description', e.target.value)}
                placeholder="请输入工艺流程的描述信息（可选）"
                rows={3}
              />
            </div>

            <div className="bg-muted/50 p-4 rounded-lg space-y-2">
              <div className="flex items-center space-x-2">
                <User className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm font-medium">创建者信息</span>
              </div>
              <div className="text-sm text-muted-foreground">
                <p>姓名: {formData.creatorName}</p>
                <p>ID: {formData.creatorId}</p>
              </div>
            </div>

            <div className="bg-muted/50 p-4 rounded-lg">
              <div className="flex items-center space-x-2 mb-2">
                <Calendar className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm font-medium">创建时间</span>
              </div>
              <p className="text-sm text-muted-foreground">
                {new Date().toLocaleString('zh-CN')}
              </p>
            </div>

            <Alert>
              <FileText className="h-4 w-4" />
              <AlertDescription>
                工艺流程创建后，您可以通过工艺管理器添加工步。
              </AlertDescription>
            </Alert>
          </div>
        );

      case 1: // 全局参数设置
        return (
          <div className="space-y-4">
            <div className="text-center mb-6">
              <h4 className="font-medium mb-2">配置全局参数</h4>
              <p className="text-sm text-muted-foreground">
                设置工艺流程的保护参数、显示设置等全局配置
              </p>
            </div>

            <GlobalParametersDialog
              trigger={
                <Button variant="outline" className="w-full">
                  <Settings className="mr-2 h-4 w-4" />
                  打开全局参数设置
                </Button>
              }
              initialParameters={globalParameters}
              onSave={setGlobalParameters}
            />

            <div className="bg-muted/50 p-4 rounded-lg">
              <h5 className="font-medium mb-2">当前配置概览</h5>
              <div className="space-y-1 text-sm">
                <div className="flex justify-between">
                  <span className="text-muted-foreground">报警容量:</span>
                  <span>{globalParameters.protectionParams.alarmSettings.alarmCapacity.value} mAh</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">报警温度:</span>
                  <span>{globalParameters.protectionParams.temperatureMonitoring.alarmTemperatureLimit.value} ℃</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">开路检测:</span>
                  <span>{globalParameters.protectionParams.abnormalDetection.openCircuitDetection.value ? '启用' : '禁用'}</span>
                </div>
              </div>
            </div>

            <Alert>
              <Settings className="h-4 w-4" />
              <AlertDescription>
                全局参数将应用于此工艺流程的所有工步，您可以稍后在工步编辑器中修改这些设置。
              </AlertDescription>
            </Alert>
          </div>
        );

      case 2: // 确认创建
        return (
          <div className="space-y-4">
            <div className="space-y-4">
              <div>
                <h4 className="font-medium mb-2">基本信息</h4>
                <div className="bg-muted/50 p-3 rounded-lg space-y-1">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">名称:</span>
                    <span className="font-medium">{formData.name}</span>
                  </div>
                  {formData.description && (
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">描述:</span>
                      <span className="font-medium max-w-48 text-right">{formData.description}</span>
                    </div>
                  )}
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">创建者:</span>
                    <span className="font-medium">{formData.creatorName}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">工步数量:</span>
                    <span className="font-medium">0（稍后添加）</span>
                  </div>
                </div>
              </div>

              <div>
                <h4 className="font-medium mb-2">全局参数配置</h4>
                <div className="bg-muted/50 p-3 rounded-lg space-y-1">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">报警容量:</span>
                    <span className="font-medium">{globalParameters.protectionParams.alarmSettings.alarmCapacity.value} mAh</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">报警温度:</span>
                    <span className="font-medium">{globalParameters.protectionParams.temperatureMonitoring.alarmTemperatureLimit.value} ℃</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">开路检测:</span>
                    <span className="font-medium">{globalParameters.protectionParams.abnormalDetection.openCircuitDetection.value ? '启用' : '禁用'}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">时间结束条件:</span>
                    <span className="font-medium">{globalParameters.displayParams.endConditions.timeCondition.value ? '启用' : '禁用'}</span>
                  </div>
                </div>
              </div>

              {errors.general && (
                <Alert variant="destructive">
                  <AlertDescription>{errors.general}</AlertDescription>
                </Alert>
              )}

              {errors.steps && (
                <Alert variant="destructive">
                  <AlertDescription>{errors.steps}</AlertDescription>
                </Alert>
              )}
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  // 内嵌模式的渲染内容
  const renderWizardContent = () => (
    <>
      {/* 步骤指示器 */}
      <div className="flex items-center justify-between mb-6">
        {wizardSteps.map((step, index) => (
          <div key={step.id} className="flex items-center">
            <div className={`
              flex items-center justify-center w-10 h-10 rounded-full border-2 transition-colors
              ${index <= currentStep
                ? 'bg-primary border-primary text-primary-foreground'
                : 'border-muted-foreground text-muted-foreground'
              }
            `}>
              {index < currentStep ? (
                <Check className="h-5 w-5" />
              ) : (
                step.icon
              )}
            </div>
            {index < wizardSteps.length - 1 && (
              <div className={`
                w-12 h-0.5 mx-2 transition-colors
                ${index < currentStep ? 'bg-primary' : 'bg-muted-foreground'}
              `} />
            )}
          </div>
        ))}
      </div>

      {/* 当前步骤内容 */}
      <div className="flex-1 overflow-y-auto">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              {wizardSteps[currentStep].icon}
              <span>{wizardSteps[currentStep].title}</span>
            </CardTitle>
            <CardDescription>
              {wizardSteps[currentStep].description}
            </CardDescription>
          </CardHeader>
          <CardContent>
            {renderStepContent()}
          </CardContent>
        </Card>
      </div>

      {/* 操作按钮 */}
      <div className="flex items-center justify-between pt-4 border-t">
        <Button
          variant="outline"
          onClick={handlePrevious}
          disabled={currentStep === 0}
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          上一步
        </Button>

        <div className="text-sm text-muted-foreground">
          第 {currentStep + 1} 步，共 {wizardSteps.length} 步
        </div>

        {currentStep < wizardSteps.length - 1 ? (
          <Button onClick={handleNext}>
            下一步
            <ArrowRight className="ml-2 h-4 w-4" />
          </Button>
        ) : (
          <Button onClick={handleCreate} disabled={isLoading}>
            {isLoading ? '创建中...' : '创建工艺流程'}
          </Button>
        )}
      </div>
    </>
  );

  // 如果是内嵌模式，直接返回内容
  if (isEmbedded) {
    return (
      <div className="space-y-4">
        {renderWizardContent()}
      </div>
    );
  }

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button className={className}>
          <FileText className="mr-2 h-4 w-4" />
          新建工艺流程
        </Button>
      </DialogTrigger>

      <DialogContent className="max-w-2xl max-h-[90vh] overflow-hidden flex flex-col">
        <DialogHeader>
          <DialogTitle>创建新的工艺流程</DialogTitle>
          <DialogDescription>
            创建一个新的工艺流程，稍后可以添加工步
          </DialogDescription>
        </DialogHeader>

        {renderWizardContent()}
      </DialogContent>
    </Dialog>
  );
}

'use client';

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { 
  Monitor, 
  Sun, 
  Moon, 
  Languages,
  Minimize2,
  Sparkles,
  Type
} from 'lucide-react';
import { useTranslations } from 'next-intl';
import { DisplaySettings } from '@/lib/types';

interface DisplaySettingsPanelProps {
  settings: DisplaySettings;
  onSettingsChange: (settings: DisplaySettings) => void;
}

export function DisplaySettingsPanel({
  settings,
  onSettingsChange
}: DisplaySettingsPanelProps) {
  const t = useTranslations('settings.display');

  const updateSetting = <K extends keyof DisplaySettings>(
    key: K,
    value: DisplaySettings[K]
  ) => {
    onSettingsChange({
      ...settings,
      [key]: value
    });
  };

  const themeOptions = [
    { value: 'light', label: t('theme.light'), icon: Sun },
    { value: 'dark', label: t('theme.dark'), icon: Moon },
    { value: 'system', label: t('theme.system'), icon: Monitor }
  ];

  const languageOptions = [
    { value: 'zh', label: t('language.zh'), flag: '🇨🇳' },
    { value: 'en', label: t('language.en'), flag: '🇺🇸' }
  ];

  const fontSizeOptions = [
    { value: 'small', label: t('fontSize.small') },
    { value: 'medium', label: t('fontSize.medium') },
    { value: 'large', label: t('fontSize.large') }
  ];

  return (
    <div className="space-y-6">
      {/* 主题设置 */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-base">
            <Sun className="h-4 w-4" />
            {t('theme.title')}
          </CardTitle>
          <CardDescription>
            {t('theme.description')}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <Label htmlFor="theme">{t('theme.label')}</Label>
            <Select
              value={settings.theme}
              onValueChange={(value: 'light' | 'dark' | 'system') => 
                updateSetting('theme', value)
              }
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {themeOptions.map((option) => {
                  const Icon = option.icon;
                  return (
                    <SelectItem key={option.value} value={option.value}>
                      <div className="flex items-center gap-2">
                        <Icon className="h-4 w-4" />
                        {option.label}
                      </div>
                    </SelectItem>
                  );
                })}
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* 语言设置 */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-base">
            <Languages className="h-4 w-4" />
            {t('language.title')}
          </CardTitle>
          <CardDescription>
            {t('language.description')}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <Label htmlFor="language">{t('language.label')}</Label>
            <Select
              value={settings.language}
              onValueChange={(value: 'zh' | 'en') => 
                updateSetting('language', value)
              }
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {languageOptions.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    <div className="flex items-center gap-2">
                      <span>{option.flag}</span>
                      {option.label}
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* 界面设置 */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-base">
            <Monitor className="h-4 w-4" />
            {t('interface.title')}
          </CardTitle>
          <CardDescription>
            {t('interface.description')}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* 字体大小 */}
          <div className="space-y-2">
            <Label htmlFor="fontSize" className="flex items-center gap-2">
              <Type className="h-4 w-4" />
              {t('fontSize.label')}
            </Label>
            <Select
              value={settings.fontSize}
              onValueChange={(value: 'small' | 'medium' | 'large') => 
                updateSetting('fontSize', value)
              }
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {fontSizeOptions.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* 紧凑模式 */}
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label className="flex items-center gap-2">
                <Minimize2 className="h-4 w-4" />
                {t('compactMode.label')}
              </Label>
              <p className="text-sm text-muted-foreground">
                {t('compactMode.description')}
              </p>
            </div>
            <Switch
              checked={settings.compactMode}
              onCheckedChange={(checked) => updateSetting('compactMode', checked)}
            />
          </div>

          {/* 动画效果 */}
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label className="flex items-center gap-2">
                <Sparkles className="h-4 w-4" />
                {t('animations.label')}
              </Label>
              <p className="text-sm text-muted-foreground">
                {t('animations.description')}
              </p>
            </div>
            <Switch
              checked={settings.showAnimations}
              onCheckedChange={(checked) => updateSetting('showAnimations', checked)}
            />
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

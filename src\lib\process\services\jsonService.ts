/**
 * JSON导入导出服务
 * 处理工步流程的JSON格式数据交换
 */

import { z } from 'zod';
import { Step, StepExportData, StepFormData } from '../types/step';
import { EXPORT_FORMAT_VERSION } from '../constants/stepConstants';

// JSON导出数据验证Schema
const stepExportSchema = z.object({
  version: z.string(),
  exportedAt: z.string().transform(str => new Date(str)),
  steps: z.array(z.object({
    id: z.string(),
    number: z.number(),
    name: z.string(),
    status: z.string(),
    mode: z.string(),
    parameters: z.object({
      electrical: z.object({
        current: z.object({
          value: z.number(),
          unit: z.literal('mA'),
          range: z.tuple([z.number(), z.number()])
        }),
        upperVoltage: z.object({
          value: z.number(),
          unit: z.literal('mV'),
          range: z.tuple([z.number(), z.number()])
        }),
        lowerVoltage: z.object({
          value: z.number(),
          unit: z.literal('mV'),
          range: z.tuple([z.number(), z.number()])
        })
      }),
      time: z.object({
        duration: z.object({
          value: z.string(),
          format: z.literal('hh:mm:ss.ms')
        })
      }),
      termination: z.object({
        capacity: z.object({
          value: z.number(),
          unit: z.literal('mAh'),
          range: z.tuple([z.number(), z.number()])
        }),
        power: z.object({
          value: z.number(),
          unit: z.literal('mW'),
          range: z.tuple([z.number(), z.number()])
        }),
        upperTemperature: z.object({
          value: z.number(),
          unit: z.literal('℃'),
          range: z.tuple([z.number(), z.number()])
        }),
        lowerTemperature: z.object({
          value: z.number(),
          unit: z.literal('℃'),
          range: z.tuple([z.number(), z.number()])
        })
      }),
      sampling: z.object({
        samplingInterval: z.object({
          value: z.number(),
          unit: z.literal('s'),
          range: z.tuple([z.number(), z.number()])
        })
      })
    }),
    auxiliaryParams: z.object({
      voltage: z.object({
        enabled: z.boolean(),
        checkTime: z.number(),
        upperLimit: z.number(),
        lowerLimit: z.number(),
        upperRange: z.number(),
        lowerRange: z.number(),
        deviation: z.number(),
        absoluteValue: z.number(),
        checkInterval: z.number(),
        fluctuationValue: z.number(),
        continuousCount: z.number(),
        totalCount: z.number(),
        rateValue: z.number(),
        comparator: z.enum(['>', '<', '>=', '<=', '=']),
        timeInterval: z.number()
      }),
      current: z.object({
        enabled: z.boolean(),
        startTime: z.number(),
        upperCurrent: z.number(),
        lowerCurrent: z.number(),
        currentValue: z.number(),
        checkInterval: z.number(),
        dropValue: z.number()
      }),
      capacity: z.object({
        enabled: z.boolean(),
        ccCapacityLimit: z.object({
          value: z.number(),
          enabled: z.boolean()
        }),
        cvCapacityLimit: z.object({
          value: z.number(),
          enabled: z.boolean()
        })
      }),
      samplingInterval: z.object({
        enabled: z.boolean(),
        voltageDiff: z.number(),
        currentDiff: z.number(),
        temperatureDiff: z.number(),
        capacityDiff: z.number(),
        powerDiff: z.number()
      }),
      stepProtection: z.object({
        enabled: z.boolean(),
        impedanceUpperLimit: z.number(),
        impedanceLowerLimit: z.number()
      })
    }),
    createdAt: z.string().transform(str => new Date(str)),
    updatedAt: z.string().transform(str => new Date(str)),
    notes: z.string().optional()
  })),
  metadata: z.object({
    totalSteps: z.number(),
    exportedBy: z.string(),
    description: z.string().optional()
  })
});

export interface JsonImportResult {
  success: boolean;
  data?: Step[];
  errors?: string[];
  warnings?: string[];
  metadata?: {
    version: string;
    totalSteps: number;
    exportedBy: string;
    exportedAt: Date;
  };
}

export interface JsonExportResult {
  success: boolean;
  data?: string;
  filename?: string;
  error?: string;
}

/**
 * 导出工步数据为JSON格式
 */
export function exportStepsToJson(
  steps: Step[], 
  options: {
    exportedBy?: string;
    description?: string;
    filename?: string;
  } = {}
): JsonExportResult {
  try {
    const exportData: StepExportData = {
      version: EXPORT_FORMAT_VERSION,
      exportedAt: new Date(),
      steps: steps.map(step => ({
        ...step,
        // 确保日期格式正确
        createdAt: step.createdAt,
        updatedAt: step.updatedAt
      })),
      metadata: {
        totalSteps: steps.length,
        exportedBy: options.exportedBy || 'Unknown',
        description: options.description
      }
    };

    const jsonString = JSON.stringify(exportData, null, 2);
    const filename = options.filename || `steps_export_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.json`;

    return {
      success: true,
      data: jsonString,
      filename
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : '导出失败'
    };
  }
}

/**
 * 从JSON字符串导入工步数据
 */
export function importStepsFromJson(jsonString: string): JsonImportResult {
  const errors: string[] = [];
  const warnings: string[] = [];

  try {
    // 解析JSON
    let jsonData: any;
    try {
      jsonData = JSON.parse(jsonString);
    } catch (error) {
      return {
        success: false,
        errors: ['JSON格式错误，请检查文件内容']
      };
    }

    // 验证数据结构
    const validationResult = stepExportSchema.safeParse(jsonData);
    if (!validationResult.success) {
      const validationErrors = validationResult.error.issues.map((err: any) =>
        `${err.path.join('.')}: ${err.message}`
      );
      return {
        success: false,
        errors: ['数据格式验证失败', ...validationErrors]
      };
    }

    const validatedData = validationResult.data;

    // 版本兼容性检查
    if (validatedData.version !== EXPORT_FORMAT_VERSION) {
      warnings.push(`文件版本 (${validatedData.version}) 与当前版本 (${EXPORT_FORMAT_VERSION}) 不匹配，可能存在兼容性问题`);
    }

    // 数据完整性检查
    if (validatedData.steps.length === 0) {
      warnings.push('导入文件中没有工步数据');
    }

    if (validatedData.metadata.totalSteps !== validatedData.steps.length) {
      warnings.push(`元数据中的工步数量 (${validatedData.metadata.totalSteps}) 与实际工步数量 (${validatedData.steps.length}) 不匹配`);
    }

    // 业务逻辑验证
    const businessValidationErrors = validateBusinessLogic(validatedData.steps);
    if (businessValidationErrors.length > 0) {
      errors.push(...businessValidationErrors);
    }

    // 转换为Step对象
    const steps: Step[] = validatedData.steps.map((stepData, index) => ({
      ...stepData,
      id: `imported_${Date.now()}_${index}`, // 生成新的ID避免冲突
      number: index + 1, // 重新编号
      createdAt: new Date(), // 使用当前时间作为导入时间
      updatedAt: new Date()
    }));

    return {
      success: errors.length === 0,
      data: steps,
      errors: errors.length > 0 ? errors : undefined,
      warnings: warnings.length > 0 ? warnings : undefined,
      metadata: {
        version: validatedData.version,
        totalSteps: validatedData.metadata.totalSteps,
        exportedBy: validatedData.metadata.exportedBy,
        exportedAt: validatedData.exportedAt
      }
    };

  } catch (error) {
    return {
      success: false,
      errors: [error instanceof Error ? error.message : '导入过程中发生未知错误']
    };
  }
}

/**
 * 业务逻辑验证
 */
function validateBusinessLogic(steps: any[]): string[] {
  const errors: string[] = [];

  steps.forEach((step, index) => {
    const stepPrefix = `工步 ${index + 1}`;

    // 电压范围验证
    if (step.parameters.electrical.upperVoltage.value < step.parameters.electrical.lowerVoltage.value) {
      errors.push(`${stepPrefix}: 上限电压应大于等于下限电压`);
    }

    // 温度范围验证
    if (step.parameters.termination.upperTemperature.value <= step.parameters.termination.lowerTemperature.value) {
      errors.push(`${stepPrefix}: 温度上限应大于温度下限`);
    }

    // 时间格式验证
    const timeFormat = /^(\d{1,2}):(\d{1,2}):(\d{1,2})(\.\d{1,3})?$/;
    if (!timeFormat.test(step.parameters.time.duration.value)) {
      errors.push(`${stepPrefix}: 时间格式不正确，应为 hh:mm:ss.ms`);
    }

    // 工步名称验证
    if (!step.name || step.name.trim().length === 0) {
      errors.push(`${stepPrefix}: 工步名称不能为空`);
    }
  });

  return errors;
}

/**
 * 下载JSON文件
 */
export function downloadJsonFile(jsonString: string, filename: string): void {
  const blob = new Blob([jsonString], { type: 'application/json' });
  const url = URL.createObjectURL(blob);
  
  const link = document.createElement('a');
  link.href = url;
  link.download = filename;
  link.style.display = 'none';
  
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  
  URL.revokeObjectURL(url);
}

/**
 * 读取文件内容
 */
export function readFileAsText(file: File): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    
    reader.onload = (event) => {
      const result = event.target?.result;
      if (typeof result === 'string') {
        resolve(result);
      } else {
        reject(new Error('文件读取失败'));
      }
    };
    
    reader.onerror = () => {
      reject(new Error('文件读取错误'));
    };
    
    reader.readAsText(file, 'utf-8');
  });
}

/**
 * 验证文件类型
 */
export function validateJsonFile(file: File): { valid: boolean; error?: string } {
  // 检查文件扩展名
  if (!file.name.toLowerCase().endsWith('.json')) {
    return { valid: false, error: '请选择JSON格式的文件' };
  }

  // 检查文件大小 (限制为10MB)
  const maxSize = 10 * 1024 * 1024; // 10MB
  if (file.size > maxSize) {
    return { valid: false, error: '文件大小不能超过10MB' };
  }

  // 检查MIME类型
  if (file.type && !['application/json', 'text/json', 'text/plain'].includes(file.type)) {
    return { valid: false, error: '文件类型不正确，请选择JSON文件' };
  }

  return { valid: true };
}

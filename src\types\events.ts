// 事件类型枚举
export enum EventType {
  // 设备事件
  DEVICE_ONLINE = 'device_online',
  DEVICE_OFFLINE = 'device_offline',
  DEVICE_MAINTENANCE = 'device_maintenance',
  DEVICE_ERROR = 'device_error',
  DEVICE_RECOVERED = 'device_recovered',
  
  // 通道事件
  CHANNEL_ENABLED = 'channel_enabled',
  CHANNEL_DISABLED = 'channel_disabled',
  CHANNEL_STEP_CHANGED = 'channel_step_changed',
  CHANNEL_PARAMETER_EXCEEDED = 'channel_parameter_exceeded',
  CHANNEL_PROCESS_STARTED = 'channel_process_started',
  CHANNEL_PROCESS_COMPLETED = 'channel_process_completed',
  CHANNEL_PROCESS_STOPPED = 'channel_process_stopped',
  
  // 用户操作事件
  USER_LOGIN = 'user_login',
  USER_LOGOUT = 'user_logout',
  USER_DEVICE_CONTROL = 'user_device_control',
  USER_CHANNEL_OPERATION = 'user_channel_operation',
  USER_CONFIG_CHANGED = 'user_config_changed',
  USER_SETTINGS_MODIFIED = 'user_settings_modified',
  
  // 系统事件
  SYSTEM_STARTUP = 'system_startup',
  SYSTEM_SHUTDOWN = 'system_shutdown',
  SYSTEM_ERROR = 'system_error',
  SYSTEM_WARNING = 'system_warning',
  SYSTEM_BACKUP_CREATED = 'system_backup_created',
  SYSTEM_UPDATE_INSTALLED = 'system_update_installed',
}

// 事件级别枚举
export enum EventLevel {
  INFO = 'info',
  WARNING = 'warning',
  ERROR = 'error',
  CRITICAL = 'critical',
}

// 事件类别枚举
export enum EventCategory {
  DEVICE = 'device',
  CHANNEL = 'channel',
  USER = 'user',
  SYSTEM = 'system',
}

// 事件源接口
export interface EventSource {
  type: 'device' | 'channel' | 'user' | 'system';
  deviceName?: string;
  channelNumber?: number;
  userId?: string;
  userName?: string;
  systemComponent?: string;
}

// 事件数据接口
export interface EventData {
  // 基础数值数据
  currentValue?: number;
  previousValue?: number;
  threshold?: number;
  minValue?: number;
  maxValue?: number;
  
  // 状态数据
  fromStatus?: string;
  toStatus?: string;
  
  // 配置数据
  configKey?: string;
  oldConfig?: any;
  newConfig?: any;
  
  // 用户操作数据
  operation?: string;
  target?: string;
  parameters?: Record<string, any>;
  
  // 系统数据
  errorCode?: string;
  errorMessage?: string;
  stackTrace?: string;
  
  // 其他扩展数据
  metadata?: Record<string, any>;
}

// 主要事件接口
export interface Event {
  id: string;
  timestamp: number; // Unix时间戳（毫秒）
  type: EventType;
  category: EventCategory;
  level: EventLevel;
  source: EventSource;
  title: string; // 事件标题
  description: string; // 事件描述
  data?: EventData; // 事件相关数据
  acknowledged?: boolean; // 是否已确认
  acknowledgedBy?: string; // 确认人
  acknowledgedAt?: number; // 确认时间
  tags?: string[]; // 事件标签
}

// 事件筛选条件接口
export interface EventFilter {
  // 时间筛选
  startTime?: number;
  endTime?: number;
  
  // 类别筛选
  categories?: EventCategory[];
  types?: EventType[];
  levels?: EventLevel[];
  
  // 源筛选
  deviceNames?: string[];
  channelNumbers?: number[];
  userIds?: string[];
  
  // 数值筛选
  minValue?: number;
  maxValue?: number;
  
  // 文本搜索
  searchKeyword?: string;
  
  // 确认状态
  acknowledgedOnly?: boolean;
  unacknowledgedOnly?: boolean;
  
  // 标签筛选
  tags?: string[];
}

// 事件统计接口
export interface EventStatistics {
  total: number;
  byCategory: Record<EventCategory, number>;
  byLevel: Record<EventLevel, number>;
  byDevice: Record<string, number>;
  recentCount: number; // 最近24小时事件数
}

// 分页参数接口
export interface EventPagination {
  page: number;
  pageSize: number;
  total: number;
  totalPages: number;
}

// 事件查询结果接口
export interface EventQueryResult {
  events: Event[];
  pagination: EventPagination;
  statistics: EventStatistics;
}

// 事件排序选项
export enum EventSortBy {
  TIMESTAMP_DESC = 'timestamp_desc',
  TIMESTAMP_ASC = 'timestamp_asc',
  LEVEL_DESC = 'level_desc',
  LEVEL_ASC = 'level_asc',
  CATEGORY = 'category',
  DEVICE = 'device',
}

// 事件视图模式
export enum EventViewMode {
  LIST = 'list',
  TIMELINE = 'timeline',
  GRID = 'grid',
}

// 快捷时间选项
export enum QuickTimeRange {
  LAST_HOUR = 'last_hour',
  TODAY = 'today',
  YESTERDAY = 'yesterday',
  LAST_7_DAYS = 'last_7_days',
  LAST_30_DAYS = 'last_30_days',
  THIS_MONTH = 'this_month',
  LAST_MONTH = 'last_month',
  CUSTOM = 'custom',
}

// 事件导出格式
export enum EventExportFormat {
  CSV = 'csv',
  JSON = 'json',
  PDF = 'pdf',
  EXCEL = 'excel',
}

// 实时事件订阅配置
export interface EventSubscription {
  categories?: EventCategory[];
  levels?: EventLevel[];
  deviceNames?: string[];
  realtime: boolean;
  maxEvents?: number; // 最大缓存事件数
}

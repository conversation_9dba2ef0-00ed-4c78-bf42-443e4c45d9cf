import { useRouter } from 'next/navigation';
import { useNavigationStore } from '@/stores/navigationStore';

export function useNavigation() {
  const router = useRouter();
  const {
    updateNavigation,
    setNavigationParams,
    getNavigationParam,
    clearNavigationParams
  } = useNavigationStore();

  const navigateTo = (
    path: string,
    options?: {
      replace?: boolean;
      params?: Record<string, any>;
    }
  ) => {
    // 设置隐式参数
    if (options?.params) {
      setNavigationParams(options.params);
    }

    // 更新导航状态
    updateNavigation(path);

    // 执行路由跳转
    if (options?.replace) {
      router.replace(path);
    } else {
      router.push(path);
    }
  };

  const navigateBack = () => {
    router.back();
  };

  const navigateForward = () => {
    router.forward();
  };

  const getParam = (key: string) => {
    return getNavigationParam(key);
  };

  const clearParams = () => {
    clearNavigationParams();
  };

  return {
    navigateTo,
    navigateBack,
    navigateForward,
    getParam,
    clearParams
  };
}

"use client"

import * as React from "react"
import {
  Home,
  Monitor,
  Activity,
  AlertTriangle,
  BarChart3,
  Settings,
  Wrench,
  Factory,
  Zap,
  Shield,
  Battery,
} from "lucide-react"

import { NavMain } from "@/components/nav-main"
import { NavUser } from "@/components/nav-user"
import { TeamSwitcher } from "@/components/team-switcher"
import { SettingsSheet } from "@/components/settings/SettingsSheet"
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarRail,
  SidebarMenu,
  SidebarMenuItem,
  SidebarMenuButton,
} from "@/components/ui/sidebar"
import { useSettings } from "@/lib/hooks/useSettings"

// 朗天PDCS工业控制系统数据
const data = {
  user: {
    name: "系统管理员",
    email: "<EMAIL>",
    avatar: "/avatars/admin.svg",
  },
  teams: [
    {
      name: "朗天PDCS",
      logo: Factory,
      plan: "工业版",
    },
    {
      name: "生产车间A",
      logo: Zap,
      plan: "生产线",
    },
    {
      name: "安全监控",
      logo: Shield,
      plan: "安全系统",
    },
  ],
  navMain: [
    {
      title: "仪表盘",
      url: "/dashboard",
      icon: Home,
      isActive: true,
    },
    {
      title: "设备监控",
      url: "/monitoring",
      icon: Monitor,
    },
    {
      title: "流程设置",
      url: "/process",
      icon: Settings,
    },
    {
      title: "数据分析",
      url: "/analytics",
      icon: BarChart3,
    },
    {
      title: "事件查看",
      url: "/events",
      icon: AlertTriangle,
    },
    {
      title: "电池分选",
      url: "/battery-sorting",
      icon: Battery,
      items: [
        {
          title: "分选配置",
          url: "/battery-sorting",
        },
        {
          title: "分选结果",
          url: "/battery-sorting/results",
        },
      ],
    },
  ]
}

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const { settings, saveSettings } = useSettings();
  const [settingsOpen, setSettingsOpen] = React.useState(false);

  return (
    <>
      <Sidebar collapsible="icon" {...props}>
        <SidebarHeader>
          <TeamSwitcher teams={data.teams} />
        </SidebarHeader>
        <SidebarContent>
          <NavMain items={data.navMain} />
        </SidebarContent>
        <SidebarFooter>
          <SidebarMenu>
            <SidebarMenuItem>
              <SidebarMenuButton onClick={() => setSettingsOpen(true)}>
                <Settings />
                <span>系统设置</span>
              </SidebarMenuButton>
            </SidebarMenuItem>

          </SidebarMenu>
          <NavUser user={data.user} />
        </SidebarFooter>
        <SidebarRail />
      </Sidebar>

      <SettingsSheet
        open={settingsOpen}
        onOpenChange={setSettingsOpen}
        settings={settings}
        onSettingsChange={saveSettings}
      />
    </>
  )
}

'use client';

import React from 'react';
import { useTranslations } from 'next-intl';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { CheckCircle, AlertTriangle, Info } from 'lucide-react';

import { StepFormData } from '@/lib/process/types/step';
import { STEP_STATUS_OPTIONS, STEP_MODE_OPTIONS } from '@/lib/process/constants/stepConstants';

interface ReviewStepProps {
  data: StepFormData;
  onChange: (data: Partial<StepFormData>) => void;
  errors: string[];
}

export function ReviewStep({ data, onChange, errors }: ReviewStepProps) {
  const t = useTranslations('process.stepWizard.review');

  const getStatusInfo = (status: string) => {
    return STEP_STATUS_OPTIONS.find(opt => opt.value === status);
  };

  const getModeInfo = (mode: string) => {
    return STEP_MODE_OPTIONS.find(opt => opt.value === mode);
  };

  const getEnabledAuxiliaryParams = () => {
    const enabled = [];
    if (data.auxiliaryParams.voltage.enabled) enabled.push('电压保护');
    if (data.auxiliaryParams.current.enabled) enabled.push('电流保护');
    if (data.auxiliaryParams.capacity.enabled) enabled.push('容量限制');
    if (data.auxiliaryParams.samplingInterval.enabled) enabled.push('采样间隔');
    if (data.auxiliaryParams.stepProtection.enabled) enabled.push('工步保护');
    return enabled;
  };

  const statusInfo = getStatusInfo(data.status);
  const modeInfo = getModeInfo(data.mode);
  const enabledAuxParams = getEnabledAuxiliaryParams();

  return (
    <div className="space-y-6">
      <Alert>
        <CheckCircle className="h-4 w-4" />
        <AlertDescription>
          请仔细检查以下配置信息，确认无误后点击"完成"按钮保存工步。
        </AlertDescription>
      </Alert>

      {/* 基本信息概览 */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">基本信息</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <div className="text-sm text-muted-foreground">工步名称</div>
              <div className="font-medium">{data.name || '未设置'}</div>
            </div>
            
            <div>
              <div className="text-sm text-muted-foreground">工步状态</div>
              <div className="flex items-center space-x-2 mt-1">
                {statusInfo && (
                  <>
                    <div 
                      className="w-3 h-3 rounded-full" 
                      style={{ backgroundColor: statusInfo.color }}
                    />
                    <span className="font-medium">{statusInfo.label}</span>
                  </>
                )}
              </div>
            </div>
            
            <div className="md:col-span-2">
              <div className="text-sm text-muted-foreground">工步模式</div>
              <div className="flex items-center space-x-2 mt-1">
                <Badge variant="secondary">{modeInfo?.label}</Badge>
                <span className="text-sm text-muted-foreground">{modeInfo?.description}</span>
              </div>
            </div>
          </div>

          {data.notes && (
            <>
              <Separator />
              <div>
                <div className="text-sm text-muted-foreground">备注</div>
                <div className="mt-1 p-3 bg-gray-50 rounded-md text-sm">
                  {data.notes}
                </div>
              </div>
            </>
          )}
        </CardContent>
      </Card>

      {/* 主要参数概览 */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">主要参数</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* 电气参数 */}
          <div>
            <h4 className="font-medium mb-3">电气参数</h4>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="p-3 bg-blue-50 rounded-md">
                <div className="text-sm text-blue-600">电流</div>
                <div className="font-semibold text-blue-800">
                  {data.parameters.electrical.current.value} {data.parameters.electrical.current.unit}
                </div>
              </div>
              
              <div className="p-3 bg-green-50 rounded-md">
                <div className="text-sm text-green-600">上限电压</div>
                <div className="font-semibold text-green-800">
                  {data.parameters.electrical.upperVoltage.value} {data.parameters.electrical.upperVoltage.unit}
                </div>
              </div>
              
              <div className="p-3 bg-orange-50 rounded-md">
                <div className="text-sm text-orange-600">下限电压</div>
                <div className="font-semibold text-orange-800">
                  {data.parameters.electrical.lowerVoltage.value} {data.parameters.electrical.lowerVoltage.unit}
                </div>
              </div>
            </div>
            
            <div className="mt-2 p-2 bg-gray-50 rounded text-sm">
              <strong>电压范围：</strong> 
              {data.parameters.electrical.lowerVoltage.value} - {data.parameters.electrical.upperVoltage.value} mV
              <span className="ml-2 text-muted-foreground">
                (差值: {data.parameters.electrical.upperVoltage.value - data.parameters.electrical.lowerVoltage.value} mV)
              </span>
            </div>
          </div>

          <Separator />

          {/* 时间和终止条件 */}
          <div>
            <h4 className="font-medium mb-3">时间和终止条件</h4>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div>
                <div className="text-sm text-muted-foreground">持续时间</div>
                <div className="font-medium">{data.parameters.time.duration.value}</div>
              </div>
              
              <div>
                <div className="text-sm text-muted-foreground">容量</div>
                <div className="font-medium">
                  {data.parameters.termination.capacity.value} {data.parameters.termination.capacity.unit}
                </div>
              </div>
              
              <div>
                <div className="text-sm text-muted-foreground">功率</div>
                <div className="font-medium">
                  {data.parameters.termination.power.value} {data.parameters.termination.power.unit}
                </div>
              </div>
              
              <div>
                <div className="text-sm text-muted-foreground">采样间隔</div>
                <div className="font-medium">
                  {data.parameters.sampling.samplingInterval.value} {data.parameters.sampling.samplingInterval.unit}
                </div>
              </div>
            </div>

            <div className="mt-3 grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <div className="text-sm text-muted-foreground">温度上限</div>
                <div className="font-medium">
                  {data.parameters.termination.upperTemperature.value} {data.parameters.termination.upperTemperature.unit}
                </div>
              </div>
              
              <div>
                <div className="text-sm text-muted-foreground">温度下限</div>
                <div className="font-medium">
                  {data.parameters.termination.lowerTemperature.value} {data.parameters.termination.lowerTemperature.unit}
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 辅助参数概览 */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">辅助参数</CardTitle>
        </CardHeader>
        <CardContent>
          {enabledAuxParams.length > 0 ? (
            <div className="space-y-4">
              <div>
                <div className="text-sm text-muted-foreground mb-2">已启用的辅助参数组</div>
                <div className="flex flex-wrap gap-2">
                  {enabledAuxParams.map((param, index) => (
                    <Badge key={index} variant="outline" className="text-green-700 border-green-300">
                      <CheckCircle className="h-3 w-3 mr-1" />
                      {param}
                    </Badge>
                  ))}
                </div>
              </div>

              {/* 详细配置信息 */}
              <div className="space-y-3">
                {data.auxiliaryParams.voltage.enabled && (
                  <div className="p-3 border rounded-md">
                    <div className="font-medium text-sm mb-2">电压保护参数</div>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-2 text-xs">
                      <div>检查间隔: {data.auxiliaryParams.voltage.checkTime}s</div>
                      <div>上限阈值: {data.auxiliaryParams.voltage.upperLimit}mV</div>
                      <div>下限阈值: {data.auxiliaryParams.voltage.lowerLimit}mV</div>
                      <div>波动阈值: {data.auxiliaryParams.voltage.fluctuationValue}mV</div>
                    </div>
                  </div>
                )}

                {data.auxiliaryParams.current.enabled && (
                  <div className="p-3 border rounded-md">
                    <div className="font-medium text-sm mb-2">电流保护参数</div>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-2 text-xs">
                      <div>起始时间: {data.auxiliaryParams.current.startTime}s</div>
                      <div>电流上限: {data.auxiliaryParams.current.upperCurrent}mA</div>
                      <div>电流下限: {data.auxiliaryParams.current.lowerCurrent}mA</div>
                      <div>超差阈值: {data.auxiliaryParams.current.currentValue}mA</div>
                    </div>
                  </div>
                )}

                {data.auxiliaryParams.capacity.enabled && (
                  <div className="p-3 border rounded-md">
                    <div className="font-medium text-sm mb-2">容量限制参数</div>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-xs">
                      <div>
                        恒流容量上限: {data.auxiliaryParams.capacity.ccCapacityLimit.value}mAh
                        <Badge variant={data.auxiliaryParams.capacity.ccCapacityLimit.enabled ? "default" : "secondary"} className="ml-2 text-xs">
                          {data.auxiliaryParams.capacity.ccCapacityLimit.enabled ? "启用" : "禁用"}
                        </Badge>
                      </div>
                      <div>
                        恒压容量上限: {data.auxiliaryParams.capacity.cvCapacityLimit.value}mAh
                        <Badge variant={data.auxiliaryParams.capacity.cvCapacityLimit.enabled ? "default" : "secondary"} className="ml-2 text-xs">
                          {data.auxiliaryParams.capacity.cvCapacityLimit.enabled ? "启用" : "禁用"}
                        </Badge>
                      </div>
                    </div>
                  </div>
                )}

                {data.auxiliaryParams.samplingInterval.enabled && (
                  <div className="p-3 border rounded-md">
                    <div className="font-medium text-sm mb-2">采样间隔参数</div>
                    <div className="grid grid-cols-2 md:grid-cols-3 gap-2 text-xs">
                      <div>电压差: {data.auxiliaryParams.samplingInterval.voltageDiff}mV</div>
                      <div>电流差: {data.auxiliaryParams.samplingInterval.currentDiff}mA</div>
                      <div>温度差: {data.auxiliaryParams.samplingInterval.temperatureDiff}℃</div>
                      <div>容量差: {data.auxiliaryParams.samplingInterval.capacityDiff}mAh</div>
                      <div>功率差: {data.auxiliaryParams.samplingInterval.powerDiff}mW</div>
                    </div>
                  </div>
                )}

                {data.auxiliaryParams.stepProtection.enabled && (
                  <div className="p-3 border rounded-md">
                    <div className="font-medium text-sm mb-2">工步保护参数</div>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-xs">
                      <div>阻抗上限: {data.auxiliaryParams.stepProtection.impedanceUpperLimit}mΩ</div>
                      <div>阻抗下限: {data.auxiliaryParams.stepProtection.impedanceLowerLimit}mΩ</div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          ) : (
            <div className="text-center py-6 text-muted-foreground">
              <Info className="h-8 w-8 mx-auto mb-2" />
              <p>未启用任何辅助参数组</p>
              <p className="text-sm">将使用默认的基本参数配置</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* 验证警告 */}
      {errors.length > 0 && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            <div className="font-medium mb-1">配置存在以下问题：</div>
            <ul className="list-disc list-inside space-y-1">
              {errors.map((error, index) => (
                <li key={index} className="text-sm">{error}</li>
              ))}
            </ul>
          </AlertDescription>
        </Alert>
      )}

      {/* 配置摘要 */}
      <Card className="border-green-200 bg-green-50">
        <CardHeader>
          <CardTitle className="text-lg text-green-800">配置摘要</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-700">{data.name ? '✓' : '✗'}</div>
              <div className="text-green-600">工步名称</div>
            </div>
            
            <div className="text-center">
              <div className="text-2xl font-bold text-green-700">✓</div>
              <div className="text-green-600">基本参数</div>
            </div>
            
            <div className="text-center">
              <div className="text-2xl font-bold text-green-700">{enabledAuxParams.length}</div>
              <div className="text-green-600">辅助参数组</div>
            </div>
            
            <div className="text-center">
              <div className="text-2xl font-bold text-green-700">{errors.length === 0 ? '✓' : '✗'}</div>
              <div className="text-green-600">验证状态</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

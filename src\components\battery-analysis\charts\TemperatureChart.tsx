'use client';

import React, { useMemo } from 'react';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  ReferenceLine,
  ComposedChart,
  Area,
  AreaChart
} from 'recharts';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Thermometer, AlertTriangle } from 'lucide-react';
import { BatteryDataRecord, StepType } from '@/lib/types';
import { useTranslations } from 'next-intl';

interface TemperatureChartProps {
  data: BatteryDataRecord[];
  selectedCycles?: number[];
  height?: number;
  temperatureThresholds?: {
    warning: number;
    critical: number;
  };
}

interface TemperatureDataPoint {
  time: number;
  temperature: number;
  voltage: number;
  current: number;
  stepType: StepType;
  cycleNumber: number;
  absoluteTime: string;
}

export function TemperatureChart({
  data,
  selectedCycles,
  height = 400,
  temperatureThresholds = { warning: 45, critical: 60 }
}: TemperatureChartProps) {
  const t = useTranslations('batteryAnalysis.charts');

  // 处理和过滤数据
  const chartData = useMemo(() => {
    let filteredData = data;

    // 按循环号过滤
    if (selectedCycles && selectedCycles.length > 0) {
      filteredData = filteredData.filter(record => 
        selectedCycles.includes(record.cycleNumber)
      );
    }

    // 转换为图表数据格式
    return filteredData.map((record, index) => ({
      time: index,
      temperature: record.temperature,
      voltage: record.voltage,
      current: record.current,
      stepType: record.stepType,
      cycleNumber: record.cycleNumber,
      absoluteTime: record.absoluteTime.toLocaleString()
    }));
  }, [data, selectedCycles]);

  // 计算温度统计信息
  const temperatureStats = useMemo(() => {
    if (chartData.length === 0) return null;

    const temperatures = chartData.map(d => d.temperature);
    const maxTemp = Math.max(...temperatures);
    const minTemp = Math.min(...temperatures);
    const avgTemp = temperatures.reduce((sum, t) => sum + t, 0) / temperatures.length;
    
    // 计算温度变化率
    const tempChanges = temperatures.slice(1).map((temp, i) => 
      Math.abs(temp - temperatures[i])
    );
    const maxTempChange = Math.max(...tempChanges, 0);
    const avgTempChange = tempChanges.length > 0 
      ? tempChanges.reduce((sum, change) => sum + change, 0) / tempChanges.length 
      : 0;

    // 检查温度警告
    const warningPoints = chartData.filter(d => d.temperature >= temperatureThresholds.warning);
    const criticalPoints = chartData.filter(d => d.temperature >= temperatureThresholds.critical);

    return {
      maxTemp,
      minTemp,
      avgTemp,
      tempRange: maxTemp - minTemp,
      maxTempChange,
      avgTempChange,
      warningCount: warningPoints.length,
      criticalCount: criticalPoints.length,
      dataPoints: chartData.length
    };
  }, [chartData, temperatureThresholds]);

  // 获取工步类型颜色
  const getStepTypeColor = (stepType: StepType): string => {
    const colorMap: Record<StepType, string> = {
      [StepType.CC_CHARGE]: '#22c55e',
      [StepType.CC_DISCHARGE]: '#ef4444',
      [StepType.CV_CHARGE]: '#3b82f6',
      [StepType.CCCV_CHARGE]: '#8b5cf6',
      [StepType.REST]: '#6b7280'
    };
    return colorMap[stepType] || '#6b7280';
  };

  // 自定义工具提示
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload as TemperatureDataPoint;
      return (
        <div className="bg-background border rounded-lg p-3 shadow-lg">
          <p className="font-medium mb-2">{t('temperature.tooltip.title')}</p>
          <div className="space-y-1 text-sm">
            <div className="flex justify-between gap-4">
              <span className="text-muted-foreground">{t('temperature.temperature')}:</span>
              <span className="font-medium flex items-center gap-1">
                <Thermometer className="h-3 w-3" />
                {data.temperature.toFixed(1)} °C
              </span>
            </div>
            <div className="flex justify-between gap-4">
              <span className="text-muted-foreground">{t('temperature.voltage')}:</span>
              <span className="font-medium">{data.voltage.toFixed(4)} V</span>
            </div>
            <div className="flex justify-between gap-4">
              <span className="text-muted-foreground">{t('temperature.current')}:</span>
              <span className="font-medium">{data.current.toFixed(4)} A</span>
            </div>
            <div className="flex justify-between gap-4">
              <span className="text-muted-foreground">{t('temperature.stepType')}:</span>
              <Badge 
                variant="secondary" 
                style={{ backgroundColor: getStepTypeColor(data.stepType) + '20' }}
              >
                {data.stepType}
              </Badge>
            </div>
            <div className="flex justify-between gap-4">
              <span className="text-muted-foreground">{t('temperature.cycle')}:</span>
              <span className="font-medium">{data.cycleNumber}</span>
            </div>
            <div className="flex justify-between gap-4">
              <span className="text-muted-foreground">{t('temperature.time')}:</span>
              <span className="font-medium text-xs">{data.absoluteTime}</span>
            </div>
          </div>
        </div>
      );
    }
    return null;
  };

  if (chartData.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Thermometer className="h-5 w-5" />
            {t('temperature.title')}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-64 text-muted-foreground">
            {t('temperature.noData')}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Thermometer className="h-5 w-5" />
            {t('temperature.title')}
          </CardTitle>
          {temperatureStats && (
            <div className="flex gap-2">
              <Badge variant="outline">
                {t('temperature.dataPoints')}: {temperatureStats.dataPoints.toLocaleString()}
              </Badge>
              {temperatureStats.criticalCount > 0 && (
                <Badge variant="destructive">
                  {t('temperature.criticalAlerts')}: {temperatureStats.criticalCount}
                </Badge>
              )}
              {temperatureStats.warningCount > 0 && temperatureStats.criticalCount === 0 && (
                <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">
                  {t('temperature.warnings')}: {temperatureStats.warningCount}
                </Badge>
              )}
            </div>
          )}
        </div>
        
        {temperatureStats && (
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div>
              <span className="text-muted-foreground">{t('temperature.maxTemp')}:</span>
              <div className="font-medium">{temperatureStats.maxTemp.toFixed(1)} °C</div>
            </div>
            <div>
              <span className="text-muted-foreground">{t('temperature.minTemp')}:</span>
              <div className="font-medium">{temperatureStats.minTemp.toFixed(1)} °C</div>
            </div>
            <div>
              <span className="text-muted-foreground">{t('temperature.avgTemp')}:</span>
              <div className="font-medium">{temperatureStats.avgTemp.toFixed(1)} °C</div>
            </div>
            <div>
              <span className="text-muted-foreground">{t('temperature.tempRange')}:</span>
              <div className="font-medium">{temperatureStats.tempRange.toFixed(1)} °C</div>
            </div>
          </div>
        )}
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* 温度警告提示 */}
        {temperatureStats && temperatureStats.criticalCount > 0 && (
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              {t('temperature.criticalWarning', { 
                count: temperatureStats.criticalCount,
                threshold: temperatureThresholds.critical 
              })}
            </AlertDescription>
          </Alert>
        )}
        
        {temperatureStats && temperatureStats.warningCount > 0 && temperatureStats.criticalCount === 0 && (
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              {t('temperature.temperatureWarning', { 
                count: temperatureStats.warningCount,
                threshold: temperatureThresholds.warning 
              })}
            </AlertDescription>
          </Alert>
        )}

        {/* 温度图表 */}
        <ResponsiveContainer width="100%" height={height}>
          <AreaChart data={chartData} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
            <defs>
              <linearGradient id="temperatureGradient" x1="0" y1="0" x2="0" y2="1">
                <stop offset="5%" stopColor="#f59e0b" stopOpacity={0.8}/>
                <stop offset="95%" stopColor="#f59e0b" stopOpacity={0.1}/>
              </linearGradient>
            </defs>
            <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
            <XAxis 
              dataKey="time"
              type="number"
              scale="linear"
              domain={['dataMin', 'dataMax']}
              tickFormatter={(value) => `${Math.floor(value / 100)}`}
              label={{ value: t('temperature.timeAxis'), position: 'insideBottom', offset: -5 }}
            />
            <YAxis 
              domain={['dataMin - 2', 'dataMax + 2']}
              label={{ value: t('temperature.temperatureAxis'), angle: -90, position: 'insideLeft' }}
            />
            <Tooltip content={<CustomTooltip />} />
            <Legend />
            
            <Area
              type="monotone"
              dataKey="temperature"
              stroke="#f59e0b"
              strokeWidth={2}
              fill="url(#temperatureGradient)"
              name={t('temperature.temperature')}
            />
            
            {/* 温度阈值参考线 */}
            <ReferenceLine 
              y={temperatureThresholds.warning} 
              stroke="#f59e0b" 
              strokeDasharray="5 5" 
              label={`${t('temperature.warningThreshold')}: ${temperatureThresholds.warning}°C`}
            />
            <ReferenceLine 
              y={temperatureThresholds.critical} 
              stroke="#ef4444" 
              strokeDasharray="5 5" 
              label={`${t('temperature.criticalThreshold')}: ${temperatureThresholds.critical}°C`}
            />
            
            {/* 平均温度参考线 */}
            {temperatureStats && (
              <ReferenceLine 
                y={temperatureStats.avgTemp} 
                stroke="#6b7280" 
                strokeDasharray="3 3" 
                label={`${t('temperature.avgLine')}: ${temperatureStats.avgTemp.toFixed(1)}°C`}
              />
            )}
          </AreaChart>
        </ResponsiveContainer>
      </CardContent>
    </Card>
  );
}

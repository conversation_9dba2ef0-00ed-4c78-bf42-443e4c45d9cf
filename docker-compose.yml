version: '3.8'

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
    restart: unless-stopped
    volumes:
      - ./data:/app/data  # 数据持久化
    networks:
      - pdcs-network

  # Nginx反向代理（可选）
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl  # SSL证书目录
    depends_on:
      - app
    restart: unless-stopped
    networks:
      - pdcs-network

networks:
  pdcs-network:
    driver: bridge

volumes:
  data:
    driver: local

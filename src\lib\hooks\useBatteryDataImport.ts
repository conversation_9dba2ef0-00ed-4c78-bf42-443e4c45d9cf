import { useState, useCallback } from 'react';
import { BatteryDataSet, CSVImportConfig } from '@/lib/types';
import { parseCSVToBatteryData, createBatteryDataSet } from '@/lib/utils/batteryDataUtils';

interface ImportState {
  isLoading: boolean;
  progress: number;
  error: string | null;
  result: BatteryDataSet | null;
}

interface UseFileImportReturn {
  importState: ImportState;
  importFile: (file: File, config?: Partial<CSVImportConfig>) => Promise<BatteryDataSet | null>;
  resetImport: () => void;
}

/**
 * 电池数据文件导入Hook
 */
export function useBatteryDataImport(): UseFileImportReturn {
  const [importState, setImportState] = useState<ImportState>({
    isLoading: false,
    progress: 0,
    error: null,
    result: null
  });

  const importFile = useCallback(async (
    file: File,
    config: Partial<CSVImportConfig> = {}
  ): Promise<BatteryDataSet | null> => {
    // 重置状态
    setImportState({
      isLoading: true,
      progress: 0,
      error: null,
      result: null
    });

    try {
      // 验证文件类型
      if (!file.name.toLowerCase().endsWith('.csv')) {
        throw new Error('请选择CSV格式的文件');
      }

      // 验证文件大小 (限制为50MB)
      const maxSize = 50 * 1024 * 1024;
      if (file.size > maxSize) {
        throw new Error('文件大小不能超过50MB');
      }

      setImportState(prev => ({ ...prev, progress: 10 }));

      // 读取文件内容
      const fileContent = await readFileAsText(file);
      setImportState(prev => ({ ...prev, progress: 30 }));

      // 默认配置
      const defaultConfig: CSVImportConfig = {
        delimiter: ',',
        encoding: 'utf-8',
        hasHeader: true,
        skipRows: 0,
        fieldMapping: {}
      };

      const finalConfig = { ...defaultConfig, ...config };
      setImportState(prev => ({ ...prev, progress: 50 }));

      // 解析CSV数据
      const batteryData = parseCSVToBatteryData(fileContent, finalConfig);
      setImportState(prev => ({ ...prev, progress: 80 }));

      if (batteryData.length === 0) {
        throw new Error('未能解析到有效的电池数据记录');
      }

      // 创建数据集
      const dataSet = createBatteryDataSet(
        file.name.replace('.csv', ''),
        batteryData,
        `从文件 ${file.name} 导入的电池测试数据`
      );

      setImportState({
        isLoading: false,
        progress: 100,
        error: null,
        result: dataSet
      });

      return dataSet;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '导入文件时发生未知错误';
      
      setImportState({
        isLoading: false,
        progress: 0,
        error: errorMessage,
        result: null
      });

      return null;
    }
  }, []);

  const resetImport = useCallback(() => {
    setImportState({
      isLoading: false,
      progress: 0,
      error: null,
      result: null
    });
  }, []);

  return {
    importState,
    importFile,
    resetImport
  };
}

/**
 * 读取文件内容为文本
 */
function readFileAsText(file: File): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    
    reader.onload = (event) => {
      const result = event.target?.result;
      if (typeof result === 'string') {
        resolve(result);
      } else {
        reject(new Error('无法读取文件内容'));
      }
    };
    
    reader.onerror = () => {
      reject(new Error('读取文件时发生错误'));
    };
    
    reader.readAsText(file, 'utf-8');
  });
}

/**
 * 电池数据导出Hook
 */
export function useBatteryDataExport() {
  const [isExporting, setIsExporting] = useState(false);

  const exportToCSV = useCallback(async (
    dataSet: BatteryDataSet,
    filename?: string
  ): Promise<void> => {
    setIsExporting(true);
    
    try {
      const { batteryDataToCSV } = await import('@/lib/utils/batteryDataUtils');
      const csvContent = batteryDataToCSV(dataSet.data);
      
      // 创建Blob并下载
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const link = document.createElement('a');
      
      if (link.download !== undefined) {
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', filename || `${dataSet.name}_export.csv`);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);
      }
    } catch (error) {
      console.error('导出文件时发生错误:', error);
      throw error;
    } finally {
      setIsExporting(false);
    }
  }, []);

  const exportToJSON = useCallback(async (
    dataSet: BatteryDataSet,
    filename?: string
  ): Promise<void> => {
    setIsExporting(true);
    
    try {
      const jsonContent = JSON.stringify(dataSet, null, 2);
      
      // 创建Blob并下载
      const blob = new Blob([jsonContent], { type: 'application/json;charset=utf-8;' });
      const link = document.createElement('a');
      
      if (link.download !== undefined) {
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', filename || `${dataSet.name}_export.json`);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);
      }
    } catch (error) {
      console.error('导出JSON文件时发生错误:', error);
      throw error;
    } finally {
      setIsExporting(false);
    }
  }, []);

  return {
    isExporting,
    exportToCSV,
    exportToJSON
  };
}

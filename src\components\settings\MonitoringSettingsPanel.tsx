'use client';

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { 
  Activity, 
  RefreshCw, 
  Database, 
  AlertTriangle,
  Zap,
  Thermometer
} from 'lucide-react';
import { useTranslations } from 'next-intl';

interface MonitoringSettings {
  refreshInterval: number;
  dataRetention: number;
  alertThresholds: {
    voltage: { min: number; max: number };
    current: { min: number; max: number };
    temperature: { min: number; max: number };
  };
}

interface MonitoringSettingsPanelProps {
  settings: MonitoringSettings;
  onSettingsChange: (settings: MonitoringSettings) => void;
}

export function MonitoringSettingsPanel({
  settings,
  onSettingsChange
}: MonitoringSettingsPanelProps) {
  const t = useTranslations('settings.monitoring');

  const updateSetting = <K extends keyof MonitoringSettings>(
    key: K,
    value: MonitoringSettings[K]
  ) => {
    onSettingsChange({
      ...settings,
      [key]: value
    });
  };

  const updateThreshold = (
    type: keyof MonitoringSettings['alertThresholds'],
    bound: 'min' | 'max',
    value: number
  ) => {
    onSettingsChange({
      ...settings,
      alertThresholds: {
        ...settings.alertThresholds,
        [type]: {
          ...settings.alertThresholds[type],
          [bound]: value
        }
      }
    });
  };

  const refreshIntervalOptions = [
    { value: 1, label: t('refresh.options.1s') },
    { value: 2, label: t('refresh.options.2s') },
    { value: 5, label: t('refresh.options.5s') },
    { value: 10, label: t('refresh.options.10s') },
    { value: 30, label: t('refresh.options.30s') },
    { value: 60, label: t('refresh.options.1m') }
  ];

  const dataRetentionOptions = [
    { value: 7, label: t('retention.options.7d') },
    { value: 14, label: t('retention.options.14d') },
    { value: 30, label: t('retention.options.30d') },
    { value: 60, label: t('retention.options.60d') },
    { value: 90, label: t('retention.options.90d') },
    { value: 365, label: t('retention.options.1y') }
  ];

  return (
    <div className="space-y-6">
      {/* 刷新设置 */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-base">
            <RefreshCw className="h-4 w-4" />
            {t('refresh.title')}
          </CardTitle>
          <CardDescription>
            {t('refresh.description')}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <Label htmlFor="refreshInterval">{t('refresh.interval')}</Label>
            <Select
              value={settings.refreshInterval.toString()}
              onValueChange={(value) => updateSetting('refreshInterval', parseInt(value))}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {refreshIntervalOptions.map((option) => (
                  <SelectItem key={option.value} value={option.value.toString()}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* 数据保留设置 */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-base">
            <Database className="h-4 w-4" />
            {t('retention.title')}
          </CardTitle>
          <CardDescription>
            {t('retention.description')}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <Label htmlFor="dataRetention">{t('retention.period')}</Label>
            <Select
              value={settings.dataRetention.toString()}
              onValueChange={(value) => updateSetting('dataRetention', parseInt(value))}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {dataRetentionOptions.map((option) => (
                  <SelectItem key={option.value} value={option.value.toString()}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* 报警阈值设置 */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-base">
            <AlertTriangle className="h-4 w-4" />
            {t('thresholds.title')}
          </CardTitle>
          <CardDescription>
            {t('thresholds.description')}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* 电压阈值 */}
          <div className="space-y-3">
            <Label className="flex items-center gap-2">
              <Zap className="h-4 w-4" />
              {t('thresholds.voltage')}
            </Label>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="voltageMin" className="text-sm">
                  {t('thresholds.min')}
                </Label>
                <Input
                  id="voltageMin"
                  type="number"
                  step="0.1"
                  value={settings.alertThresholds.voltage.min}
                  onChange={(e) => updateThreshold('voltage', 'min', parseFloat(e.target.value) || 0)}
                  placeholder="2.5"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="voltageMax" className="text-sm">
                  {t('thresholds.max')}
                </Label>
                <Input
                  id="voltageMax"
                  type="number"
                  step="0.1"
                  value={settings.alertThresholds.voltage.max}
                  onChange={(e) => updateThreshold('voltage', 'max', parseFloat(e.target.value) || 0)}
                  placeholder="4.5"
                />
              </div>
            </div>
          </div>

          {/* 电流阈值 */}
          <div className="space-y-3">
            <Label className="flex items-center gap-2">
              <Activity className="h-4 w-4" />
              {t('thresholds.current')}
            </Label>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="currentMin" className="text-sm">
                  {t('thresholds.min')}
                </Label>
                <Input
                  id="currentMin"
                  type="number"
                  step="0.1"
                  value={settings.alertThresholds.current.min}
                  onChange={(e) => updateThreshold('current', 'min', parseFloat(e.target.value) || 0)}
                  placeholder="-10"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="currentMax" className="text-sm">
                  {t('thresholds.max')}
                </Label>
                <Input
                  id="currentMax"
                  type="number"
                  step="0.1"
                  value={settings.alertThresholds.current.max}
                  onChange={(e) => updateThreshold('current', 'max', parseFloat(e.target.value) || 0)}
                  placeholder="10"
                />
              </div>
            </div>
          </div>

          {/* 温度阈值 */}
          <div className="space-y-3">
            <Label className="flex items-center gap-2">
              <Thermometer className="h-4 w-4" />
              {t('thresholds.temperature')}
            </Label>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="temperatureMin" className="text-sm">
                  {t('thresholds.min')}
                </Label>
                <Input
                  id="temperatureMin"
                  type="number"
                  step="1"
                  value={settings.alertThresholds.temperature.min}
                  onChange={(e) => updateThreshold('temperature', 'min', parseFloat(e.target.value) || 0)}
                  placeholder="-10"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="temperatureMax" className="text-sm">
                  {t('thresholds.max')}
                </Label>
                <Input
                  id="temperatureMax"
                  type="number"
                  step="1"
                  value={settings.alertThresholds.temperature.max}
                  onChange={(e) => updateThreshold('temperature', 'max', parseFloat(e.target.value) || 0)}
                  placeholder="60"
                />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

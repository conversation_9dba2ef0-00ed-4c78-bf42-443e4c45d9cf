import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { LabeledProgress, CircularProgress } from '@/components/ui/progress';
import { ChannelCard } from '@/components/ui/interactive-card';
import { 
  Battery, 
  Thermometer, 
  Zap, 
  Activity,
  Clock,
  RotateCcw,
  TrendingUp,
  AlertTriangle
} from 'lucide-react';

interface Channel {
  channelNumber: number;
  stepStatus: string;
  stepMode: string;
  current: number;
  voltage: number;
  capacity: number;
  energy: number;
  temperature: number;
  auxVoltage: number;
  isEnabled: boolean;
  totalStepNumber: number;
  cycleCount: number;
  stepNumber: number;
  stepTime: number;
  processTime: number;
  alarmStatus: number;
  processName: string;
  trayCode: string;
  colorStatus: string;
  deviceName: string;
}

interface EnhancedChannelCardProps {
  channel: Channel;
  onChannelClick?: (channel: Channel) => void;
}

// 获取状态颜色
const getStatusColor = (status: string) => {
  const colors = {
    '充电': 'bg-green-500',
    '放电': 'bg-blue-500', 
    '搁置': 'bg-yellow-500',
    '暂停': 'bg-red-500',
    '完成': 'bg-gray-500',
    '复位': 'bg-purple-500',
    '运行中': 'bg-green-500',
    '待机': 'bg-yellow-500',
    '故障': 'bg-red-500'
  };
  return colors[status as keyof typeof colors] || 'bg-gray-500';
};

// 获取状态变体
const getStatusVariant = (status: string): 'default' | 'success' | 'warning' | 'danger' => {
  const variants: Record<string, 'default' | 'success' | 'warning' | 'danger'> = {
    '充电': 'success',
    '放电': 'default',
    '搁置': 'warning',
    '暂停': 'danger',
    '完成': 'default',
    '复位': 'default',
    '运行中': 'success',
    '待机': 'warning',
    '故障': 'danger'
  };
  return variants[status] || 'default';
};

// 格式化时间
const formatTime = (seconds: number) => {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = seconds % 60;
  return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
};

export const EnhancedChannelCard: React.FC<EnhancedChannelCardProps> = ({ 
  channel, 
  onChannelClick 
}) => {
  // 计算进度百分比
  const stepProgress = (channel.stepNumber / channel.totalStepNumber) * 100;
  const voltageProgress = (channel.voltage / 5.0) * 100; // 假设最大电压5V
  const currentProgress = Math.abs(channel.current / 3.0) * 100; // 假设最大电流3A
  const tempProgress = (channel.temperature / 60) * 100; // 假设最大温度60°C
  
  // 获取温度状态
  const getTempVariant = (temp: number): 'default' | 'success' | 'warning' | 'danger' => {
    if (temp < 25) return 'success';
    if (temp < 40) return 'default';
    if (temp < 50) return 'warning';
    return 'danger';
  };

  return (
    <ChannelCard 
      isDisabled={!channel.isEnabled}
      onClick={() => onChannelClick?.(channel)}
      className="h-full"
    >
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <div className={`w-3 h-3 rounded-full ${getStatusColor(channel.stepStatus)}`} />
            <CardTitle className="text-lg">通道 {channel.channelNumber}</CardTitle>
            {channel.alarmStatus > 0 && (
              <AlertTriangle className="h-4 w-4 text-red-500" />
            )}
          </div>
          <Badge className={getStatusColor(channel.stepStatus)}>
            {channel.stepStatus}
          </Badge>
        </div>
        <div className="text-sm text-muted-foreground">{channel.stepMode}</div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* 主要参数网格 */}
        <div className="grid grid-cols-2 gap-3">
          {/* 电流 */}
          <div className="space-y-2">
            <div className="flex items-center gap-1">
              <Zap className="h-3 w-3 text-blue-500" />
              <span className="text-xs text-muted-foreground">电流</span>
            </div>
            <div className="text-lg font-bold text-blue-600">
              {channel.current.toFixed(3)}A
            </div>
            <LabeledProgress
              value={Math.abs(channel.current)}
              max={3.0}
              label=""
              variant={channel.current > 0 ? 'success' : 'default'}
              size="sm"
              className="text-xs"
            />
          </div>

          {/* 电压 */}
          <div className="space-y-2">
            <div className="flex items-center gap-1">
              <Battery className="h-3 w-3 text-green-500" />
              <span className="text-xs text-muted-foreground">电压</span>
            </div>
            <div className="text-lg font-bold text-green-600">
              {channel.voltage.toFixed(3)}V
            </div>
            <LabeledProgress
              value={channel.voltage}
              max={5.0}
              label=""
              variant="success"
              size="sm"
              className="text-xs"
            />
          </div>
        </div>

        {/* 容量和能量 */}
        <div className="grid grid-cols-2 gap-3">
          <div>
            <div className="flex items-center gap-1 mb-1">
              <Activity className="h-3 w-3 text-purple-500" />
              <span className="text-xs text-muted-foreground">容量</span>
            </div>
            <div className="text-sm font-medium">{channel.capacity.toFixed(1)}mAh</div>
          </div>
          <div>
            <div className="flex items-center gap-1 mb-1">
              <TrendingUp className="h-3 w-3 text-orange-500" />
              <span className="text-xs text-muted-foreground">能量</span>
            </div>
            <div className="text-sm font-medium">{channel.energy.toFixed(2)}Wh</div>
          </div>
        </div>

        {/* 温度圆形进度 */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <CircularProgress
              value={channel.temperature}
              max={60}
              size={40}
              strokeWidth={3}
              variant={getTempVariant(channel.temperature)}
              showValue={false}
            >
              <Thermometer className="h-3 w-3" />
            </CircularProgress>
            <div>
              <div className="text-xs text-muted-foreground">温度</div>
              <div className="text-sm font-medium">{channel.temperature.toFixed(1)}°C</div>
            </div>
          </div>

          {/* 工步进度 */}
          <div className="text-right">
            <div className="text-xs text-muted-foreground">工步进度</div>
            <div className="text-sm font-medium">
              {channel.stepNumber}/{channel.totalStepNumber}
            </div>
            <div className="text-xs text-green-600">
              {stepProgress.toFixed(1)}%
            </div>
          </div>
        </div>

        {/* 时间信息 */}
        <div className="pt-2 border-t space-y-2">
          <div className="grid grid-cols-2 gap-3 text-xs">
            <div>
              <div className="flex items-center gap-1 text-muted-foreground">
                <Clock className="h-3 w-3" />
                <span>工步时间</span>
              </div>
              <div className="font-medium">{formatTime(channel.stepTime)}</div>
            </div>
            <div>
              <div className="flex items-center gap-1 text-muted-foreground">
                <RotateCcw className="h-3 w-3" />
                <span>循环次数</span>
              </div>
              <div className="font-medium">{channel.cycleCount}</div>
            </div>
          </div>
          
          <div className="text-xs">
            <div className="text-muted-foreground">进程时间</div>
            <div className="font-medium">{formatTime(channel.processTime)}</div>
          </div>
        </div>
      </CardContent>
    </ChannelCard>
  );
};

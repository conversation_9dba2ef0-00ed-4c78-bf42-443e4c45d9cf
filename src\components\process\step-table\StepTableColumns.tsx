'use client';

import React from 'react';
import { ColumnDef } from '@tanstack/react-table';
import { ArrowUpDown, MoreHorizontal, Edit, Copy, Trash2 } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';

import { Step } from '@/lib/process/types/step';
import { STEP_STATUS_OPTIONS, STEP_MODE_OPTIONS } from '@/lib/process/constants/stepConstants';

interface StepTableActionsProps {
  step: Step;
  onEdit: (step: Step) => void;
  onDuplicate: (step: Step) => void;
  onDelete: (step: Step) => void;
}

function StepTableActions({ step, onEdit, onDuplicate, onDelete }: StepTableActionsProps) {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" className="h-8 w-8 p-0">
          <span className="sr-only">打开菜单</span>
          <MoreHorizontal className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuLabel>操作</DropdownMenuLabel>
        <DropdownMenuItem onClick={() => onEdit(step)}>
          <Edit className="mr-2 h-4 w-4" />
          编辑
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => onDuplicate(step)}>
          <Copy className="mr-2 h-4 w-4" />
          复制
        </DropdownMenuItem>
        <DropdownMenuSeparator />
        <DropdownMenuItem 
          onClick={() => onDelete(step)}
          className="text-red-600"
        >
          <Trash2 className="mr-2 h-4 w-4" />
          删除
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

export function createStepTableColumns(
  onEdit: (step: Step) => void,
  onDuplicate: (step: Step) => void,
  onDelete: (step: Step) => void
): ColumnDef<Step>[] {
  
  const getStatusBadge = (status: string) => {
    const statusOption = STEP_STATUS_OPTIONS.find(opt => opt.value === status);
    if (!statusOption) return <Badge variant="outline">{status}</Badge>;

    return (
      <Badge 
        variant="outline" 
        style={{ 
          borderColor: statusOption.color,
          color: statusOption.color 
        }}
      >
        {statusOption.label}
      </Badge>
    );
  };

  const getModeBadge = (mode: string) => {
    const modeOption = STEP_MODE_OPTIONS.find(opt => opt.value === mode);
    if (!modeOption) return <Badge variant="secondary">{mode}</Badge>;

    return (
      <Badge variant="secondary" title={modeOption.description}>
        {modeOption.label}
      </Badge>
    );
  };

  return [
    {
      id: "select",
      header: ({ table }) => (
        <Checkbox
          checked={table.getIsAllPageRowsSelected()}
          onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
          aria-label="选择全部"
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label="选择行"
        />
      ),
      enableSorting: false,
      enableHiding: false,
      size: 50,
    },
    {
      accessorKey: "number",
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            className="h-8 px-2"
          >
            序号
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        )
      },
      cell: ({ row }) => (
        <div className="text-center font-medium">
          {row.getValue("number")}
        </div>
      ),
      size: 80,
    },
    {
      accessorKey: "name",
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            className="h-8 px-2"
          >
            工步名称
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        )
      },
      cell: ({ row }) => (
        <div className="font-medium">
          {row.getValue("name")}
        </div>
      ),
      size: 200,
    },
    {
      accessorKey: "status",
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            className="h-8 px-2"
          >
            状态
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        )
      },
      cell: ({ row }) => getStatusBadge(row.getValue("status")),
      size: 100,
    },
    {
      accessorKey: "mode",
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            className="h-8 px-2"
          >
            模式
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        )
      },
      cell: ({ row }) => getModeBadge(row.getValue("mode")),
      size: 120,
    },
    {
      id: "current",
      header: "电流(mA)",
      cell: ({ row }) => {
        const step = row.original;
        return (
          <div className="text-right font-mono">
            {step.parameters.electrical.current.value}
          </div>
        );
      },
      size: 120,
    },
    {
      id: "voltage",
      header: "电压范围(mV)",
      cell: ({ row }) => {
        const step = row.original;
        const lower = step.parameters.electrical.lowerVoltage.value;
        const upper = step.parameters.electrical.upperVoltage.value;
        return (
          <div className="text-right font-mono">
            {lower} - {upper}
          </div>
        );
      },
      size: 150,
    },
    {
      id: "duration",
      header: "持续时间",
      cell: ({ row }) => {
        const step = row.original;
        return (
          <div className="text-center font-mono">
            {step.parameters.time.duration.value}
          </div>
        );
      },
      size: 120,
    },
    {
      id: "capacity",
      header: "容量(mAh)",
      cell: ({ row }) => {
        const step = row.original;
        return (
          <div className="text-right font-mono">
            {step.parameters.termination.capacity.value}
          </div>
        );
      },
      size: 120,
    },
    {
      id: "temperature",
      header: "温度范围(℃)",
      cell: ({ row }) => {
        const step = row.original;
        const lower = step.parameters.termination.lowerTemperature.value;
        const upper = step.parameters.termination.upperTemperature.value;
        return (
          <div className="text-right font-mono text-sm">
            {lower} - {upper}
          </div>
        );
      },
      size: 130,
    },
    {
      accessorKey: "createdAt",
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            className="h-8 px-2"
          >
            创建时间
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        )
      },
      cell: ({ row }) => {
        const date = row.getValue("createdAt") as Date;
        return (
          <div className="text-sm text-muted-foreground">
            {date.toLocaleDateString()}
          </div>
        );
      },
      size: 120,
    },
    {
      id: "auxiliaryParams",
      header: "辅助参数",
      cell: ({ row }) => {
        const step = row.original;
        const enabledParams = [];
        
        if (step.auxiliaryParams.voltage.enabled) enabledParams.push('电压');
        if (step.auxiliaryParams.current.enabled) enabledParams.push('电流');
        if (step.auxiliaryParams.capacity.enabled) enabledParams.push('容量');
        if (step.auxiliaryParams.samplingInterval.enabled) enabledParams.push('采样');
        if (step.auxiliaryParams.stepProtection.enabled) enabledParams.push('保护');

        return (
          <div className="flex flex-wrap gap-1">
            {enabledParams.length > 0 ? (
              enabledParams.map((param, index) => (
                <Badge key={index} variant="outline" className="text-xs">
                  {param}
                </Badge>
              ))
            ) : (
              <span className="text-xs text-muted-foreground">无</span>
            )}
          </div>
        );
      },
      size: 150,
    },
    {
      id: "actions",
      header: "操作",
      cell: ({ row }) => (
        <StepTableActions
          step={row.original}
          onEdit={onEdit}
          onDuplicate={onDuplicate}
          onDelete={onDelete}
        />
      ),
      enableSorting: false,
      enableHiding: false,
      size: 80,
    },
  ];
}

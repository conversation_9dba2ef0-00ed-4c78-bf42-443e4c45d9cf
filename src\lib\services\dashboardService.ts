/**
 * 仪表盘数据服务
 */

import { 
  DashboardData, 
  KPIMetric, 
  DeviceStatusData, 
  ChannelTrendData, 
  StepExecutionData, 
  RealTimeData,
  SystemHealthData,
  RecentActivity,
  AlertData,
  PerformanceMetric
} from '@/lib/types/dashboard';

// 生成模拟KPI数据
function generateKPIData(): KPIMetric[] {
  return [
    {
      id: 'device-online-rate',
      title: '设备在线率',
      value: 94.5,
      unit: '%',
      change: 2.1,
      changeType: 'increase',
      status: 'good',
      icon: 'monitor'
    },
    {
      id: 'channel-utilization',
      title: '通道利用率',
      value: 78.3,
      unit: '%',
      change: -1.2,
      changeType: 'decrease',
      status: 'warning',
      icon: 'activity'
    },
    {
      id: 'process-completion',
      title: '工步完成率',
      value: 96.8,
      unit: '%',
      change: 0.5,
      changeType: 'increase',
      status: 'good',
      icon: 'check-circle'
    },
    {
      id: 'system-health',
      title: '系统健康度',
      value: 92.1,
      unit: '%',
      change: 0,
      changeType: 'stable',
      status: 'good',
      icon: 'heart'
    }
  ];
}

// 生成设备状态分布数据
function generateDeviceStatusData(): DeviceStatusData[] {
  return [
    { status: 'online', count: 15, percentage: 75, color: '#16a34a' },
    { status: 'offline', count: 3, percentage: 15, color: '#6b7280' },
    { status: 'maintenance', count: 1, percentage: 5, color: '#d97706' },
    { status: 'error', count: 1, percentage: 5, color: '#dc2626' }
  ];
}

// 生成通道状态趋势数据
function generateChannelTrendData(): ChannelTrendData[] {
  const data: ChannelTrendData[] = [];
  const now = new Date();
  
  for (let i = 23; i >= 0; i--) {
    const timestamp = new Date(now.getTime() - i * 60 * 60 * 1000);
    data.push({
      timestamp: timestamp.toISOString(),
      online: 14 + Math.floor(Math.random() * 2),
      running: 8 + Math.floor(Math.random() * 4),
      idle: 4 + Math.floor(Math.random() * 3),
      error: Math.floor(Math.random() * 2)
    });
  }
  
  return data;
}

// 生成工步执行统计数据
function generateStepExecutionData(): StepExecutionData[] {
  return [
    { stepName: '恒流充电', completed: 45, running: 8, failed: 2, total: 55 },
    { stepName: '恒压充电', completed: 38, running: 5, failed: 1, total: 44 },
    { stepName: '放电测试', completed: 32, running: 6, failed: 3, total: 41 },
    { stepName: '静置', completed: 28, running: 12, failed: 0, total: 40 },
    { stepName: '循环测试', completed: 15, running: 3, failed: 2, total: 20 }
  ];
}

// 生成实时监控数据
function generateRealTimeData(): RealTimeData[] {
  const data: RealTimeData[] = [];
  const now = new Date();
  
  for (let i = 59; i >= 0; i--) {
    const timestamp = new Date(now.getTime() - i * 60 * 1000);
    data.push({
      timestamp: timestamp.toISOString(),
      voltage: 3.7 + Math.random() * 0.6,
      current: 1.0 + Math.random() * 0.5,
      temperature: 25 + Math.random() * 10,
      power: 3.7 + Math.random() * 2.0
    });
  }
  
  return data;
}

// 生成系统健康度数据
function generateSystemHealthData(): SystemHealthData {
  return {
    overall: 92,
    components: {
      devices: 95,
      channels: 88,
      processes: 94,
      storage: 91
    }
  };
}

// 生成最近活动数据
function generateRecentActivities(): RecentActivity[] {
  const activities: RecentActivity[] = [
    {
      id: '1',
      type: 'process_complete',
      title: '工艺流程完成',
      description: '锂电池充放电测试流程已完成',
      timestamp: new Date(Date.now() - 5 * 60 * 1000),
      status: 'success',
      deviceId: 'device1',
      processId: 'proc1'
    },
    {
      id: '2',
      type: 'device_online',
      title: '设备上线',
      description: '设备 Device-003 已重新连接',
      timestamp: new Date(Date.now() - 15 * 60 * 1000),
      status: 'info',
      deviceId: 'device3'
    },
    {
      id: '3',
      type: 'alarm',
      title: '温度告警',
      description: '通道 CH-008 温度超过阈值',
      timestamp: new Date(Date.now() - 25 * 60 * 1000),
      status: 'warning',
      deviceId: 'device2',
      channelId: 'ch8'
    },
    {
      id: '4',
      type: 'process_start',
      title: '工艺流程启动',
      description: '电池容量标定流程已启动',
      timestamp: new Date(Date.now() - 45 * 60 * 1000),
      status: 'info',
      deviceId: 'device1',
      processId: 'proc2'
    }
  ];
  
  return activities;
}

// 生成告警数据
function generateAlertData(): AlertData[] {
  return [
    {
      id: 'alert1',
      level: 'warning',
      title: '通道温度异常',
      message: '通道 CH-008 温度达到 45°C，超过安全阈值',
      timestamp: new Date(Date.now() - 10 * 60 * 1000),
      acknowledged: false,
      deviceId: 'device2',
      channelId: 'ch8',
      source: 'temperature_monitor'
    },
    {
      id: 'alert2',
      level: 'info',
      title: '设备维护提醒',
      message: '设备 Device-001 建议进行定期维护',
      timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
      acknowledged: true,
      deviceId: 'device1',
      source: 'maintenance_scheduler'
    },
    {
      id: 'alert3',
      level: 'critical',
      title: '通道通信故障',
      message: '通道 CH-015 通信中断，请检查连接',
      timestamp: new Date(Date.now() - 30 * 60 * 1000),
      acknowledged: false,
      deviceId: 'device2',
      channelId: 'ch15',
      source: 'communication_monitor'
    }
  ];
}

// 生成性能指标数据
function generatePerformanceMetrics(): PerformanceMetric[] {
  return [
    {
      name: 'CPU使用率',
      current: 45.2,
      average: 42.8,
      max: 78.5,
      min: 12.3,
      unit: '%',
      trend: 'up'
    },
    {
      name: '内存使用率',
      current: 68.7,
      average: 65.4,
      max: 89.2,
      min: 34.1,
      unit: '%',
      trend: 'stable'
    },
    {
      name: '网络延迟',
      current: 12.5,
      average: 15.2,
      max: 45.8,
      min: 8.1,
      unit: 'ms',
      trend: 'down'
    },
    {
      name: '磁盘使用率',
      current: 34.8,
      average: 32.1,
      max: 67.9,
      min: 18.5,
      unit: '%',
      trend: 'up'
    }
  ];
}

// 获取仪表盘数据
export async function getDashboardData(): Promise<DashboardData> {
  // 模拟API延迟
  await new Promise(resolve => setTimeout(resolve, 500));
  
  return {
    kpis: generateKPIData(),
    deviceStatus: generateDeviceStatusData(),
    channelTrends: generateChannelTrendData(),
    stepExecution: generateStepExecutionData(),
    realTimeData: generateRealTimeData(),
    systemHealth: generateSystemHealthData(),
    recentActivities: generateRecentActivities(),
    alerts: generateAlertData(),
    performanceMetrics: generatePerformanceMetrics(),
    lastUpdated: new Date()
  };
}

// 刷新仪表盘数据
export async function refreshDashboardData(): Promise<DashboardData> {
  return getDashboardData();
}

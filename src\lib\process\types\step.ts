/**
 * 工步相关类型定义
 * 基于step-specification.md规范
 */

// 工步状态枚举
export enum StepStatus {
  DISCHARGE = 'discharge',    // 放电
  CHARGE = 'charge',         // 充电
  REST = 'rest',             // 搁置
  SUSPEND = 'suspend',       // 挂起
  CYCLE = 'cycle'            // 循环
}

// 工步模式枚举
export enum StepMode {
  CC = 'cc',                 // 恒流
  CCCV = 'cccv',            // 恒流恒压
  CP = 'cp',                 // 恒功率
  CR = 'cr',                 // 恒阻
  DCIR = 'dcir',            // 直流内阻
  PULSE = 'pulse',          // 脉冲
  CV = 'cv',                // 恒压
  RATE_CC = 'rate_cc',      // 倍率恒流
  RATE_CV = 'rate_cv'       // 倍率恒压
}

// 电气参数
export interface ElectricalParams {
  current: {
    value: number;
    unit: 'mA';
    range: [number, number];
  };
  upperVoltage: {
    value: number;
    unit: 'mV';
    range: [number, number];
  };
  lowerVoltage: {
    value: number;
    unit: 'mV';
    range: [number, number];
  };
}

// 时间参数
export interface TimeParams {
  duration: {
    value: string; // HH:MM:SS.ms格式
    format: 'hh:mm:ss.ms';
  };
}

// 终止条件参数
export interface TerminationParams {
  capacity: {
    value: number;
    unit: 'mAh';
    range: [number, number];
  };
  power: {
    value: number;
    unit: 'mW';
    range: [number, number];
  };
  upperTemperature: {
    value: number;
    unit: '℃';
    range: [number, number];
  };
  lowerTemperature: {
    value: number;
    unit: '℃';
    range: [number, number];
  };
}

// 采样设置参数
export interface SamplingParams {
  samplingInterval: {
    value: number;
    unit: 's';
    range: [number, number];
  };
}

// 工步主要参数
export interface StepParameters {
  electrical: ElectricalParams;
  time: TimeParams;
  termination: TerminationParams;
  sampling: SamplingParams;
}

// 电压参数组
export interface VoltageParams {
  enabled: boolean;
  checkTime: number;
  upperLimit: number;
  lowerLimit: number;
  upperRange: number;
  lowerRange: number;
  deviation: number;
  absoluteValue: number;
  checkInterval: number;
  fluctuationValue: number;
  continuousCount: number;
  totalCount: number;
  rateValue: number;
  comparator: '>' | '<' | '>=' | '<=' | '=';
  timeInterval: number;
}

// 电流参数组
export interface CurrentParams {
  enabled: boolean;
  startTime: number;
  upperCurrent: number;
  lowerCurrent: number;
  currentValue: number;
  checkInterval: number;
  dropValue: number;
}

// 容量参数组
export interface CapacityParams {
  enabled: boolean;
  ccCapacityLimit: {
    value: number;
    enabled: boolean;
  };
  cvCapacityLimit: {
    value: number;
    enabled: boolean;
  };
}

// 采样间隔参数组
export interface SamplingIntervalParams {
  enabled: boolean;
  voltageDiff: number;
  currentDiff: number;
  temperatureDiff: number;
  capacityDiff: number;
  powerDiff: number;
}

// 工步保护参数组
export interface StepProtectionParams {
  enabled: boolean;
  impedanceUpperLimit: number;
  impedanceLowerLimit: number;
}

// 辅助参数组
export interface AuxiliaryParameters {
  voltage: VoltageParams;
  current: CurrentParams;
  capacity: CapacityParams;
  samplingInterval: SamplingIntervalParams;
  stepProtection: StepProtectionParams;
}

// 工步完整数据结构
export interface Step {
  id: string;
  number: number;
  name: string;
  status: StepStatus;
  mode: StepMode;
  parameters: StepParameters;
  auxiliaryParams: AuxiliaryParameters;
  createdAt: Date;
  updatedAt: Date;
  notes?: string;
}

// 工步创建/编辑表单数据
export interface StepFormData {
  name: string;
  status: StepStatus;
  mode: StepMode;
  parameters: StepParameters;
  auxiliaryParams: AuxiliaryParameters;
  notes?: string;
}

// 工步验证错误
export interface StepValidationError {
  field: string;
  message: string;
  code: string;
}

// 工步操作结果
export interface StepOperationResult {
  success: boolean;
  data?: Step;
  errors?: StepValidationError[];
  message?: string;
}

// 工步列表查询参数
export interface StepListQuery {
  page?: number;
  pageSize?: number;
  search?: string;
  status?: StepStatus[];
  mode?: StepMode[];
  sortBy?: 'number' | 'name' | 'createdAt' | 'updatedAt';
  sortOrder?: 'asc' | 'desc';
}

// 工步列表响应
export interface StepListResponse {
  steps: Step[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

// 工步批量操作
export interface StepBatchOperation {
  action: 'delete' | 'duplicate' | 'export' | 'move';
  stepIds: string[];
  targetPosition?: number; // for move operation
}

// 工步导入/导出格式
export interface StepExportData {
  version: string;
  exportedAt: Date;
  steps: Step[];
  metadata: {
    totalSteps: number;
    exportedBy: string;
    description?: string;
  };
}

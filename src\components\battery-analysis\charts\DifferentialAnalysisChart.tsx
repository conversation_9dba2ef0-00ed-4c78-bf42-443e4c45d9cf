'use client';

import React, { useMemo } from 'react';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  ReferenceLine
} from 'recharts';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { BatteryDataRecord, StepType } from '@/lib/types';
import { useTranslations } from 'next-intl';

interface DifferentialAnalysisChartProps {
  data: BatteryDataRecord[];
  selectedCycles?: number[];
  height?: number;
}

interface DQDVDataPoint {
  voltage: number;
  dQdV: number;
  dQmdV: number;
  cycleNumber: number;
  stepType: StepType;
  capacity: number;
  current: number;
}

export function DifferentialAnalysisChart({
  data,
  selectedCycles,
  height = 400
}: DifferentialAnalysisChartProps) {
  const t = useTranslations('batteryAnalysis.charts');

  // 处理dQ/dV数据
  const dqdvData = useMemo(() => {
    let filteredData = data;

    // 按循环号过滤
    if (selectedCycles && selectedCycles.length > 0) {
      filteredData = filteredData.filter(record => 
        selectedCycles.includes(record.cycleNumber)
      );
    }

    // 只保留有效的dQ/dV数据点（非零且有限）
    const validData = filteredData.filter(record => 
      record.dQdV !== 0 && 
      isFinite(record.dQdV) && 
      record.voltage > 0 &&
      (record.stepType === StepType.CC_CHARGE || record.stepType === StepType.CC_DISCHARGE)
    );

    // 转换为图表数据格式并按电压排序
    return validData
      .map(record => ({
        voltage: record.voltage,
        dQdV: Math.abs(record.dQdV), // 取绝对值便于比较
        dQmdV: Math.abs(record.dQmdV),
        cycleNumber: record.cycleNumber,
        stepType: record.stepType,
        capacity: record.capacity,
        current: record.current
      }))
      .sort((a, b) => a.voltage - b.voltage);
  }, [data, selectedCycles]);

  // 按循环分组数据
  const cycleGroupedData = useMemo(() => {
    const groups: Record<number, DQDVDataPoint[]> = {};
    
    dqdvData.forEach(point => {
      if (!groups[point.cycleNumber]) {
        groups[point.cycleNumber] = [];
      }
      groups[point.cycleNumber].push(point);
    });

    return groups;
  }, [dqdvData]);

  // 按工步类型分组数据
  const stepTypeGroupedData = useMemo(() => {
    const chargeData = dqdvData.filter(d => d.stepType === StepType.CC_CHARGE);
    const dischargeData = dqdvData.filter(d => d.stepType === StepType.CC_DISCHARGE);
    
    return {
      charge: chargeData,
      discharge: dischargeData
    };
  }, [dqdvData]);

  // 计算统计信息
  const stats = useMemo(() => {
    if (dqdvData.length === 0) return null;

    const dqdvValues = dqdvData.map(d => d.dQdV);
    const voltages = dqdvData.map(d => d.voltage);
    const cycles = [...new Set(dqdvData.map(d => d.cycleNumber))];

    const maxDQDV = Math.max(...dqdvValues);
    const minDQDV = Math.min(...dqdvValues);
    const avgDQDV = dqdvValues.reduce((sum, val) => sum + val, 0) / dqdvValues.length;

    const maxVoltage = Math.max(...voltages);
    const minVoltage = Math.min(...voltages);

    return {
      dataPoints: dqdvData.length,
      cycleCount: cycles.length,
      maxDQDV,
      minDQDV,
      avgDQDV,
      voltageRange: { min: minVoltage, max: maxVoltage }
    };
  }, [dqdvData]);

  // 生成循环颜色
  const getCycleColor = (cycleNumber: number): string => {
    const colors = [
      '#3b82f6', '#ef4444', '#22c55e', '#f59e0b', '#8b5cf6',
      '#06b6d4', '#f97316', '#84cc16', '#ec4899', '#6366f1'
    ];
    return colors[cycleNumber % colors.length];
  };

  // 自定义工具提示
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload as DQDVDataPoint;
      return (
        <div className="bg-background border rounded-lg p-3 shadow-lg">
          <p className="font-medium mb-2">{t('differential.tooltip.title')}</p>
          <div className="space-y-1 text-sm">
            <div className="flex justify-between gap-4">
              <span className="text-muted-foreground">{t('differential.voltage')}:</span>
              <span className="font-medium">{data.voltage.toFixed(4)} V</span>
            </div>
            <div className="flex justify-between gap-4">
              <span className="text-muted-foreground">dQ/dV:</span>
              <span className="font-medium">{data.dQdV.toFixed(2)} mAh/V</span>
            </div>
            <div className="flex justify-between gap-4">
              <span className="text-muted-foreground">dQm/dV:</span>
              <span className="font-medium">{data.dQmdV.toFixed(2)} mAh/V·g</span>
            </div>
            <div className="flex justify-between gap-4">
              <span className="text-muted-foreground">{t('differential.cycle')}:</span>
              <span className="font-medium">{data.cycleNumber}</span>
            </div>
            <div className="flex justify-between gap-4">
              <span className="text-muted-foreground">{t('differential.stepType')}:</span>
              <Badge variant="secondary">
                {data.stepType}
              </Badge>
            </div>
            <div className="flex justify-between gap-4">
              <span className="text-muted-foreground">{t('differential.current')}:</span>
              <span className="font-medium">{data.current.toFixed(4)} A</span>
            </div>
          </div>
        </div>
      );
    }
    return null;
  };

  if (dqdvData.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>{t('differential.title')}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-64 text-muted-foreground">
            {t('differential.noData')}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle>{t('differential.title')}</CardTitle>
          {stats && (
            <div className="flex gap-2">
              <Badge variant="outline">
                {t('differential.dataPoints')}: {stats.dataPoints.toLocaleString()}
              </Badge>
              <Badge variant="outline">
                {t('differential.cycles')}: {stats.cycleCount}
              </Badge>
            </div>
          )}
        </div>
        {stats && (
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div>
              <span className="text-muted-foreground">{t('differential.maxDQDV')}:</span>
              <div className="font-medium">{stats.maxDQDV.toFixed(2)} mAh/V</div>
            </div>
            <div>
              <span className="text-muted-foreground">{t('differential.avgDQDV')}:</span>
              <div className="font-medium">{stats.avgDQDV.toFixed(2)} mAh/V</div>
            </div>
            <div>
              <span className="text-muted-foreground">{t('differential.voltageRange')}:</span>
              <div className="font-medium">
                {stats.voltageRange.min.toFixed(2)} - {stats.voltageRange.max.toFixed(2)} V
              </div>
            </div>
            <div>
              <span className="text-muted-foreground">{t('differential.totalPoints')}:</span>
              <div className="font-medium">{stats.dataPoints}</div>
            </div>
          </div>
        )}
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="byCycle" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="byCycle">{t('differential.tabs.byCycle')}</TabsTrigger>
            <TabsTrigger value="byStepType">{t('differential.tabs.byStepType')}</TabsTrigger>
            <TabsTrigger value="combined">{t('differential.tabs.combined')}</TabsTrigger>
          </TabsList>
          
          <TabsContent value="byCycle" className="mt-4">
            <ResponsiveContainer width="100%" height={height}>
              <LineChart margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
                <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
                <XAxis 
                  type="number"
                  dataKey="voltage"
                  domain={['dataMin', 'dataMax']}
                  label={{ value: t('differential.voltageAxis'), position: 'insideBottom', offset: -5 }}
                />
                <YAxis 
                  label={{ value: 'dQ/dV (mAh/V)', angle: -90, position: 'insideLeft' }}
                />
                <Tooltip content={<CustomTooltip />} />
                <Legend />
                
                {Object.entries(cycleGroupedData).map(([cycle, cycleData]) => (
                  <Line
                    key={cycle}
                    data={cycleData}
                    type="monotone"
                    dataKey="dQdV"
                    stroke={getCycleColor(parseInt(cycle))}
                    strokeWidth={1.5}
                    dot={false}
                    name={`${t('differential.cycle')} ${cycle}`}
                    connectNulls={false}
                  />
                ))}
              </LineChart>
            </ResponsiveContainer>
          </TabsContent>
          
          <TabsContent value="byStepType" className="mt-4">
            <ResponsiveContainer width="100%" height={height}>
              <LineChart margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
                <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
                <XAxis 
                  type="number"
                  dataKey="voltage"
                  domain={['dataMin', 'dataMax']}
                  label={{ value: t('differential.voltageAxis'), position: 'insideBottom', offset: -5 }}
                />
                <YAxis 
                  label={{ value: 'dQ/dV (mAh/V)', angle: -90, position: 'insideLeft' }}
                />
                <Tooltip content={<CustomTooltip />} />
                <Legend />
                
                <Line
                  data={stepTypeGroupedData.charge}
                  type="monotone"
                  dataKey="dQdV"
                  stroke="#22c55e"
                  strokeWidth={2}
                  dot={false}
                  name={t('differential.charge')}
                />
                <Line
                  data={stepTypeGroupedData.discharge}
                  type="monotone"
                  dataKey="dQdV"
                  stroke="#ef4444"
                  strokeWidth={2}
                  dot={false}
                  name={t('differential.discharge')}
                />
              </LineChart>
            </ResponsiveContainer>
          </TabsContent>
          
          <TabsContent value="combined" className="mt-4">
            <ResponsiveContainer width="100%" height={height}>
              <LineChart data={dqdvData} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
                <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
                <XAxis 
                  dataKey="voltage"
                  type="number"
                  domain={['dataMin', 'dataMax']}
                  label={{ value: t('differential.voltageAxis'), position: 'insideBottom', offset: -5 }}
                />
                <YAxis 
                  label={{ value: 'dQ/dV (mAh/V)', angle: -90, position: 'insideLeft' }}
                />
                <Tooltip content={<CustomTooltip />} />
                <Legend />
                
                <Line
                  type="monotone"
                  dataKey="dQdV"
                  stroke="#3b82f6"
                  strokeWidth={1}
                  dot={false}
                  name="dQ/dV"
                />
                
                {stats && (
                  <ReferenceLine 
                    y={stats.avgDQDV} 
                    stroke="#6b7280" 
                    strokeDasharray="5 5" 
                    label={`${t('differential.avgLine')}: ${stats.avgDQDV.toFixed(2)}`}
                  />
                )}
              </LineChart>
            </ResponsiveContainer>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}

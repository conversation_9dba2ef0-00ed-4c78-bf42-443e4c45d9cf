'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { 
  Search, 
  Filter, 
  Download, 
  Eye, 
  CheckCircle, 
  XCircle, 
  AlertTriangle,
  RotateCcw
} from 'lucide-react';
import { SortingResult, SortingQueryParams } from '@/lib/types/battery-sorting';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';

interface SortingResultsListProps {
  results: SortingResult[];
  isLoading?: boolean;
  onViewDetail?: (result: SortingResult) => void;
  onExport?: (format: 'excel' | 'csv' | 'pdf') => void;
  onRetest?: (batteryId: string) => void;
  className?: string;
}

const GRADE_COLORS = {
  'A+': 'bg-green-100 text-green-800 border-green-200',
  'A': 'bg-green-100 text-green-700 border-green-200',
  'B+': 'bg-blue-100 text-blue-800 border-blue-200',
  'B': 'bg-blue-100 text-blue-700 border-blue-200',
  'C': 'bg-yellow-100 text-yellow-800 border-yellow-200',
  'D': 'bg-orange-100 text-orange-800 border-orange-200',
  'Reject': 'bg-red-100 text-red-800 border-red-200'
};

const DECISION_ICONS = {
  accept: CheckCircle,
  reject: XCircle,
  retest: RotateCcw
};

const DECISION_COLORS = {
  accept: 'text-green-600',
  reject: 'text-red-600',
  retest: 'text-orange-600'
};

export function SortingResultsList({
  results,
  isLoading = false,
  onViewDetail,
  onExport,
  onRetest,
  className
}: SortingResultsListProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [gradeFilter, setGradeFilter] = useState<string>('all');
  const [decisionFilter, setDecisionFilter] = useState<string>('all');
  const [sortBy, setSortBy] = useState<string>('date');

  // 过滤和排序结果
  const filteredResults = results
    .filter(result => {
      const matchesSearch = result.batteryId.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           result.testData.batchNumber.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesGrade = gradeFilter === 'all' || result.grade === gradeFilter;
      const matchesDecision = decisionFilter === 'all' || result.decision === decisionFilter;
      
      return matchesSearch && matchesGrade && matchesDecision;
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'score':
          return b.overallScore - a.overallScore;
        case 'grade':
          return a.grade.localeCompare(b.grade);
        case 'batch':
          return a.testData.batchNumber.localeCompare(b.testData.batchNumber);
        default:
          return new Date(b.completedAt).getTime() - new Date(a.completedAt).getTime();
      }
    });

  const getDecisionLabel = (decision: string) => {
    switch (decision) {
      case 'accept': return '合格';
      case 'reject': return '不合格';
      case 'retest': return '重测';
      default: return decision;
    }
  };

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          分选结果列表
          <div className="flex items-center space-x-2">
            <Badge variant="outline">{filteredResults.length} 条记录</Badge>
            {onExport && (
              <Select onValueChange={(value) => onExport(value as any)}>
                <SelectTrigger className="w-32">
                  <Download className="h-4 w-4 mr-2" />
                  <SelectValue placeholder="导出" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="excel">Excel</SelectItem>
                  <SelectItem value="csv">CSV</SelectItem>
                  <SelectItem value="pdf">PDF</SelectItem>
                </SelectContent>
              </Select>
            )}
          </div>
        </CardTitle>
        <CardDescription>
          电池分选测试结果和质量评级
        </CardDescription>
      </CardHeader>
      <CardContent>
        {/* 筛选控件 */}
        <div className="flex flex-wrap gap-4 mb-6">
          <div className="flex-1 min-w-[200px]">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="搜索电池ID或批次号..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>
          
          <Select value={gradeFilter} onValueChange={setGradeFilter}>
            <SelectTrigger className="w-32">
              <SelectValue placeholder="等级" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">所有等级</SelectItem>
              <SelectItem value="A+">A+</SelectItem>
              <SelectItem value="A">A</SelectItem>
              <SelectItem value="B+">B+</SelectItem>
              <SelectItem value="B">B</SelectItem>
              <SelectItem value="C">C</SelectItem>
              <SelectItem value="D">D</SelectItem>
              <SelectItem value="Reject">不合格</SelectItem>
            </SelectContent>
          </Select>
          
          <Select value={decisionFilter} onValueChange={setDecisionFilter}>
            <SelectTrigger className="w-32">
              <SelectValue placeholder="决策" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">所有决策</SelectItem>
              <SelectItem value="accept">合格</SelectItem>
              <SelectItem value="reject">不合格</SelectItem>
              <SelectItem value="retest">重测</SelectItem>
            </SelectContent>
          </Select>
          
          <Select value={sortBy} onValueChange={setSortBy}>
            <SelectTrigger className="w-32">
              <SelectValue placeholder="排序" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="date">按时间</SelectItem>
              <SelectItem value="score">按评分</SelectItem>
              <SelectItem value="grade">按等级</SelectItem>
              <SelectItem value="batch">按批次</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* 结果表格 */}
        <div className="border rounded-lg">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>电池ID</TableHead>
                <TableHead>批次号</TableHead>
                <TableHead>等级</TableHead>
                <TableHead>评分</TableHead>
                <TableHead>决策</TableHead>
                <TableHead>测试时间</TableHead>
                <TableHead>操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {isLoading ? (
                <TableRow>
                  <TableCell colSpan={7} className="text-center py-8">
                    <div className="flex items-center justify-center">
                      <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary mr-2" />
                      加载中...
                    </div>
                  </TableCell>
                </TableRow>
              ) : filteredResults.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={7} className="text-center py-8 text-muted-foreground">
                    暂无分选结果
                  </TableCell>
                </TableRow>
              ) : (
                filteredResults.map((result) => {
                  const DecisionIcon = DECISION_ICONS[result.decision];
                  const decisionColor = DECISION_COLORS[result.decision];
                  
                  return (
                    <TableRow key={result.id}>
                      <TableCell className="font-medium">
                        {result.batteryId}
                      </TableCell>
                      <TableCell>
                        {result.testData.batchNumber}
                      </TableCell>
                      <TableCell>
                        <Badge 
                          variant="outline" 
                          className={cn('font-medium', GRADE_COLORS[result.grade])}
                        >
                          {result.grade}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <span className="font-medium">{result.overallScore.toFixed(1)}</span>
                          <div className="w-16 h-2 bg-gray-200 rounded-full overflow-hidden">
                            <div 
                              className="h-full bg-gradient-to-r from-red-500 via-yellow-500 to-green-500"
                              style={{ width: `${result.overallScore}%` }}
                            />
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <DecisionIcon className={cn('h-4 w-4', decisionColor)} />
                          <span>{getDecisionLabel(result.decision)}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        {format(result.completedAt, 'yyyy-MM-dd HH:mm')}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          {onViewDetail && (
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => onViewDetail(result)}
                            >
                              <Eye className="h-3 w-3 mr-1" />
                              查看
                            </Button>
                          )}
                          {onRetest && result.decision === 'retest' && (
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => onRetest(result.batteryId)}
                            >
                              <RotateCcw className="h-3 w-3 mr-1" />
                              重测
                            </Button>
                          )}
                        </div>
                      </TableCell>
                    </TableRow>
                  );
                })
              )}
            </TableBody>
          </Table>
        </div>
      </CardContent>
    </Card>
  );
}

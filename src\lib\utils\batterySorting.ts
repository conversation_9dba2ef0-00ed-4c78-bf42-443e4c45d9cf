import {
  BatteryDataRecord,
  BatteryParameterType,
  SortingVariable,
  SortingGrade,
  SortingRule,
  SortingResult,
  ConditionNode,
  ComparisonOperator,
  LogicalOperator,
  StepType
} from '@/lib/types';

/**
 * 电池分选算法核心类
 */
export class BatterySortingEngine {
  
  /**
   * 执行电池分选
   */
  static sortBattery(
    data: BatteryDataRecord[],
    rule: SortingRule,
    channelId: string,
    deviceId: string,
    batteryId?: string
  ): SortingResult {
    try {
      // 1. 计算变量值
      const calculatedValues = this.calculateVariableValues(data, rule.variables);
      
      // 2. 评估分选条件，找到匹配的等级
      const matchedGrade = this.evaluateGradeConditions(calculatedValues, rule.grades);
      
      // 3. 创建分选结果
      return {
        id: this.generateId(),
        channelId,
        deviceId,
        batteryId,
        ruleId: rule.id,
        grade: matchedGrade,
        sortingTime: new Date(),
        testDataSnapshot: [...data],
        calculatedValues
      };
      
    } catch (error) {
      // 分选失败，返回错误结果
      return {
        id: this.generateId(),
        channelId,
        deviceId,
        batteryId,
        ruleId: rule.id,
        grade: null,
        errorMessage: error instanceof Error ? error.message : '分选过程中发生未知错误',
        sortingTime: new Date(),
        testDataSnapshot: [...data],
        calculatedValues: {}
      };
    }
  }

  /**
   * 计算变量值
   */
  private static calculateVariableValues(
    data: BatteryDataRecord[],
    variables: SortingVariable[]
  ): Record<string, number> {
    const values: Record<string, number> = {};

    variables.forEach(variable => {
      try {
        const value = this.calculateSingleVariable(data, variable);
        values[variable.id] = value;
      } catch (error) {
        console.warn(`计算变量 ${variable.name} 失败:`, error);
        values[variable.id] = 0; // 默认值
      }
    });

    return values;
  }

  /**
   * 计算单个变量的值
   */
  private static calculateSingleVariable(
    data: BatteryDataRecord[],
    variable: SortingVariable
  ): number {
    // 根据条件筛选数据
    let filteredData = data;

    // 按工步号筛选
    if (variable.stepNumber !== undefined) {
      filteredData = filteredData.filter(record => record.stepNumber === variable.stepNumber);
    }

    // 按循环号筛选
    if (variable.cycleNumber !== undefined) {
      filteredData = filteredData.filter(record => record.cycleNumber === variable.cycleNumber);
    }

    if (filteredData.length === 0) {
      throw new Error(`没有找到符合条件的数据: 工步${variable.stepNumber}, 循环${variable.cycleNumber}`);
    }

    // 根据参数类型计算值
    switch (variable.parameterType) {
      case BatteryParameterType.VOLTAGE:
        return this.getEndValue(filteredData, 'voltage');
      
      case BatteryParameterType.CURRENT:
        return this.getEndValue(filteredData, 'current');
      
      case BatteryParameterType.CAPACITY:
        return this.getMaxValue(filteredData, 'capacity');
      
      case BatteryParameterType.CHARGE_CAPACITY:
        return this.getMaxValue(filteredData, 'chargeCapacity');
      
      case BatteryParameterType.DISCHARGE_CAPACITY:
        return this.getMaxValue(filteredData, 'dischargeCapacity');
      
      case BatteryParameterType.ENERGY:
        return this.getMaxValue(filteredData, 'energy');
      
      case BatteryParameterType.CHARGE_ENERGY:
        return this.getMaxValue(filteredData, 'chargeEnergy');
      
      case BatteryParameterType.DISCHARGE_ENERGY:
        return this.getMaxValue(filteredData, 'dischargeEnergy');
      
      case BatteryParameterType.TEMPERATURE:
        return this.getAverageValue(filteredData, 'temperature');
      
      case BatteryParameterType.EFFICIENCY:
        return this.calculateEfficiency(filteredData);
      
      case BatteryParameterType.CAPACITY_RETENTION:
        return this.calculateCapacityRetention(data, variable.cycleNumber);
      
      case BatteryParameterType.CYCLE_COUNT:
        return this.getCycleCount(data);
      
      default:
        throw new Error(`不支持的参数类型: ${variable.parameterType}`);
    }
  }

  /**
   * 获取结束值（最后一个记录的值）
   */
  private static getEndValue(data: BatteryDataRecord[], field: keyof BatteryDataRecord): number {
    if (data.length === 0) return 0;
    const lastRecord = data[data.length - 1];
    return Number(lastRecord[field]) || 0;
  }

  /**
   * 获取最大值
   */
  private static getMaxValue(data: BatteryDataRecord[], field: keyof BatteryDataRecord): number {
    const values = data.map(record => Number(record[field]) || 0);
    return Math.max(...values);
  }

  /**
   * 获取平均值
   */
  private static getAverageValue(data: BatteryDataRecord[], field: keyof BatteryDataRecord): number {
    const values = data.map(record => Number(record[field]) || 0);
    return values.reduce((sum, val) => sum + val, 0) / values.length;
  }

  /**
   * 计算效率
   */
  private static calculateEfficiency(data: BatteryDataRecord[]): number {
    const chargeRecords = data.filter(r => r.stepType === StepType.CC_CHARGE);
    const dischargeRecords = data.filter(r => r.stepType === StepType.CC_DISCHARGE);
    
    const maxChargeCapacity = this.getMaxValue(chargeRecords, 'chargeCapacity');
    const maxDischargeCapacity = this.getMaxValue(dischargeRecords, 'dischargeCapacity');
    
    return maxChargeCapacity > 0 ? (maxDischargeCapacity / maxChargeCapacity) * 100 : 0;
  }

  /**
   * 计算容量保持率
   */
  private static calculateCapacityRetention(data: BatteryDataRecord[], targetCycle?: number): number {
    const cycles = [...new Set(data.map(r => r.cycleNumber))].sort((a, b) => a - b);
    
    if (cycles.length < 2) return 100;
    
    const firstCycle = cycles[0];
    const lastCycle = targetCycle || cycles[cycles.length - 1];
    
    const firstCycleData = data.filter(r => r.cycleNumber === firstCycle);
    const lastCycleData = data.filter(r => r.cycleNumber === lastCycle);
    
    const initialCapacity = this.getMaxValue(firstCycleData, 'dischargeCapacity');
    const currentCapacity = this.getMaxValue(lastCycleData, 'dischargeCapacity');
    
    return initialCapacity > 0 ? (currentCapacity / initialCapacity) * 100 : 0;
  }

  /**
   * 获取循环次数
   */
  private static getCycleCount(data: BatteryDataRecord[]): number {
    return new Set(data.map(r => r.cycleNumber)).size;
  }

  /**
   * 评估等级条件
   */
  private static evaluateGradeConditions(
    calculatedValues: Record<string, number>,
    grades: SortingGrade[]
  ): SortingGrade | null {
    // 按优先级排序
    const sortedGrades = [...grades].sort((a, b) => a.priority - b.priority);
    
    for (const grade of sortedGrades) {
      try {
        if (this.evaluateConditionExpression(grade.conditions, calculatedValues)) {
          return grade;
        }
      } catch (error) {
        console.warn(`评估等级 ${grade.name} 条件失败:`, error);
      }
    }
    
    return null; // 没有匹配的等级
  }

  /**
   * 评估条件表达式
   */
  private static evaluateConditionExpression(
    expression: string,
    values: Record<string, number>
  ): boolean {
    try {
      // 简单的表达式解析和评估
      // 这里实现一个基础的表达式解析器
      // 支持格式如: "结束容量 > 3000 AND 中值电压 > 3.7"
      
      // 替换变量名为实际值
      let processedExpression = expression;
      
      // 查找所有变量引用并替换为数值
      Object.entries(values).forEach(([variableId, value]) => {
        // 这里需要根据变量ID找到变量名，简化处理直接使用ID
        const regex = new RegExp(`\\b${variableId}\\b`, 'g');
        processedExpression = processedExpression.replace(regex, value.toString());
      });
      
      // 替换逻辑运算符
      processedExpression = processedExpression
        .replace(/\bAND\b/g, '&&')
        .replace(/\bOR\b/g, '||')
        .replace(/\bNOT\b/g, '!');
      
      // 使用Function构造器安全地评估表达式
      const result = new Function('return ' + processedExpression)();
      return Boolean(result);
      
    } catch (error) {
      console.error('表达式评估失败:', expression, error);
      return false;
    }
  }

  /**
   * 生成唯一ID
   */
  private static generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }
}

/**
 * 获取可用的电池参数类型列表
 */
export function getBatteryParameterTypes(): Array<{
  value: BatteryParameterType;
  label: string;
  category: string;
  unit?: string;
}> {
  return [
    // 电学参数
    { value: BatteryParameterType.VOLTAGE, label: '电压', category: '电学参数', unit: 'V' },
    { value: BatteryParameterType.CURRENT, label: '电流', category: '电学参数', unit: 'A' },
    { value: BatteryParameterType.POWER, label: '功率', category: '电学参数', unit: 'W' },
    { value: BatteryParameterType.RESISTANCE, label: '接触电阻', category: '电学参数', unit: 'mΩ' },
    
    // 容量参数
    { value: BatteryParameterType.CAPACITY, label: '容量', category: '容量参数', unit: 'Ah' },
    { value: BatteryParameterType.SPECIFIC_CAPACITY, label: '比容量', category: '容量参数', unit: 'mAh/g' },
    { value: BatteryParameterType.CHARGE_CAPACITY, label: '充电容量', category: '容量参数', unit: 'Ah' },
    { value: BatteryParameterType.DISCHARGE_CAPACITY, label: '放电容量', category: '容量参数', unit: 'Ah' },
    
    // 能量参数
    { value: BatteryParameterType.ENERGY, label: '能量', category: '能量参数', unit: 'Wh' },
    { value: BatteryParameterType.SPECIFIC_ENERGY, label: '比能量', category: '能量参数', unit: 'mWh/g' },
    { value: BatteryParameterType.CHARGE_ENERGY, label: '充电能量', category: '能量参数', unit: 'Wh' },
    { value: BatteryParameterType.DISCHARGE_ENERGY, label: '放电能量', category: '能量参数', unit: 'Wh' },
    
    // 环境参数
    { value: BatteryParameterType.TEMPERATURE, label: '温度', category: '环境参数', unit: '℃' },
    
    // 计算参数
    { value: BatteryParameterType.EFFICIENCY, label: '效率', category: '计算参数', unit: '%' },
    { value: BatteryParameterType.CAPACITY_RETENTION, label: '容量保持率', category: '计算参数', unit: '%' },
    { value: BatteryParameterType.CYCLE_COUNT, label: '循环次数', category: '计算参数', unit: '次' }
  ];
}

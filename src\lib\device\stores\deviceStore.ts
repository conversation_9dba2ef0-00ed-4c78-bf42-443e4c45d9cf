'use client';

import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { Channel, ChannelType, ChannelStatus } from '@/lib/process/types/channel';

// 设备信息接口
export interface Device {
  id: string;
  name: string;
  type: string;
  status: 'online' | 'offline' | 'error' | 'maintenance';
  channels: Channel[];
  location?: string;
  serialNumber?: string;
  firmwareVersion?: string;
  lastHeartbeat?: Date;
  createdAt: Date;
  updatedAt: Date;
}

// 设备状态接口
interface DeviceState {
  // 设备列表
  devices: Device[];
  
  // 所有通道（扁平化）
  channels: Channel[];
  
  // 加载状态
  isLoading: boolean;
  
  // 错误信息
  error: string | null;
  
  // 是否已初始化
  isInitialized: boolean;
}

// 设备操作接口
interface DeviceActions {
  // 初始化设备数据
  initialize: () => Promise<void>;
  
  // 加载设备列表
  loadDevices: () => Promise<void>;
  
  // 加载通道列表
  loadChannels: () => Promise<void>;
  
  // 获取设备的通道
  getDeviceChannels: (deviceId: string) => Channel[];
  
  // 获取在线通道
  getOnlineChannels: () => Channel[];
  
  // 更新通道状态
  updateChannelStatus: (channelId: string, status: ChannelStatus) => void;
  
  // 清除错误
  clearError: () => void;
  
  // 重置状态
  reset: () => void;
}

type DeviceStore = DeviceState & DeviceActions;

// 创建模拟设备数据
const createMockDevices = (): Device[] => {
  const now = new Date();
  
  return [
    {
      id: 'device1',
      name: '电池测试设备-01',
      type: 'BatteryTester',
      status: 'online',
      location: '实验室A-01',
      serialNumber: 'BT001-2024',
      firmwareVersion: 'v2.1.0',
      lastHeartbeat: now,
      createdAt: now,
      updatedAt: now,
      channels: []
    },
    {
      id: 'device2', 
      name: '电池测试设备-02',
      type: 'BatteryTester',
      status: 'online',
      location: '实验室A-02',
      serialNumber: 'BT002-2024',
      firmwareVersion: 'v2.1.0',
      lastHeartbeat: now,
      createdAt: now,
      updatedAt: now,
      channels: []
    }
  ];
};

// 创建模拟通道数据
const createMockChannels = (): Channel[] => {
  const now = new Date();
  const channels: Channel[] = [];
  
  // 为每个设备创建8个通道
  ['device1', 'device2'].forEach((deviceId, deviceIndex) => {
    for (let i = 1; i <= 8; i++) {
      channels.push({
        id: `${deviceId}_ch${i}`,
        deviceId,
        channelNumber: i,
        name: `通道${i}`,
        type: ChannelType.HYBRID,
        status: i <= 6 ? ChannelStatus.IDLE : ChannelStatus.OFFLINE,
        isSelected: false,
        capabilities: {
          maxCurrent: 5000,
          maxVoltage: 5000,
          maxPower: 25000,
          minVoltage: 0,
          temperatureRange: [-40, 85],
          supportedModes: ['cc', 'cv', 'cccv']
        },
        config: {
          name: `通道${i}`,
          enabled: i <= 6,
          autoStart: false,
          safetyLimits: {
            maxCurrent: 5000,
            maxVoltage: 5000,
            maxTemperature: 85,
            minVoltage: 0,
            minTemperature: -40
          }
        },
        statistics: {
          totalRunTime: Math.floor(Math.random() * 10000),
          totalCycles: Math.floor(Math.random() * 1000),
          totalCapacity: Math.floor(Math.random() * 50000)
        },
        createdAt: now,
        updatedAt: now
      });
    }
  });
  
  return channels;
};

// 创建设备Store
export const useDeviceStore = create<DeviceStore>()(
  devtools(
    (set, get) => ({
      // 初始状态
      devices: [],
      channels: [],
      isLoading: false,
      error: null,
      isInitialized: false,

      // 初始化
      initialize: async () => {
        if (get().isInitialized) {
          return;
        }

        set({ isLoading: true, error: null }, false, 'initialize/start');

        try {
          const devices = createMockDevices();
          const channels = createMockChannels();
          
          // 将通道分配给对应的设备
          devices.forEach(device => {
            device.channels = channels.filter(channel => channel.deviceId === device.id);
          });

          set({
            devices,
            channels,
            isLoading: false,
            isInitialized: true
          }, false, 'initialize/success');
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : '初始化设备数据失败';
          set({
            error: errorMessage,
            isLoading: false
          }, false, 'initialize/error');
        }
      },

      // 加载设备列表
      loadDevices: async () => {
        set({ isLoading: true, error: null }, false, 'loadDevices/start');

        try {
          // 这里可以调用实际的API
          const devices = createMockDevices();
          const { channels } = get();
          
          // 将通道分配给对应的设备
          devices.forEach(device => {
            device.channels = channels.filter(channel => channel.deviceId === device.id);
          });

          set({
            devices,
            isLoading: false
          }, false, 'loadDevices/success');
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : '加载设备列表失败';
          set({
            error: errorMessage,
            isLoading: false
          }, false, 'loadDevices/error');
        }
      },

      // 加载通道列表
      loadChannels: async () => {
        set({ isLoading: true, error: null }, false, 'loadChannels/start');

        try {
          const channels = createMockChannels();
          const { devices } = get();
          
          // 更新设备的通道信息
          devices.forEach(device => {
            device.channels = channels.filter(channel => channel.deviceId === device.id);
          });

          set({
            channels,
            devices: [...devices],
            isLoading: false
          }, false, 'loadChannels/success');
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : '加载通道列表失败';
          set({
            error: errorMessage,
            isLoading: false
          }, false, 'loadChannels/error');
        }
      },

      // 获取设备的通道
      getDeviceChannels: (deviceId: string) => {
        const { channels } = get();
        return channels.filter(channel => channel.deviceId === deviceId);
      },

      // 获取在线通道
      getOnlineChannels: () => {
        const { channels } = get();
        return channels.filter(channel => 
          channel.status !== ChannelStatus.OFFLINE && 
          channel.status !== ChannelStatus.ERROR
        );
      },

      // 更新通道状态
      updateChannelStatus: (channelId: string, status: ChannelStatus) => {
        set((state) => ({
          channels: state.channels.map(channel =>
            channel.id === channelId
              ? { ...channel, status, updatedAt: new Date() }
              : channel
          ),
          devices: state.devices.map(device => ({
            ...device,
            channels: device.channels.map(channel =>
              channel.id === channelId
                ? { ...channel, status, updatedAt: new Date() }
                : channel
            )
          }))
        }), false, 'updateChannelStatus');
      },

      // 清除错误
      clearError: () => {
        set({ error: null }, false, 'clearError');
      },

      // 重置状态
      reset: () => {
        set({
          devices: [],
          channels: [],
          isLoading: false,
          error: null,
          isInitialized: false
        }, false, 'reset');
      }
    }),
    {
      name: 'device-store'
    }
  )
);

// 便利的hooks
export const useDevices = () => useDeviceStore(state => state.devices);
export const useChannels = () => useDeviceStore(state => state.channels);
export const useOnlineChannels = () => useDeviceStore(state => state.getOnlineChannels());
export const useDeviceLoading = () => useDeviceStore(state => state.isLoading);
export const useDeviceError = () => useDeviceStore(state => state.error);

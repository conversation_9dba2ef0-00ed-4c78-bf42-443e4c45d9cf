import { useTranslations } from 'next-intl';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Search, Filter, CheckCircle, AlertTriangle, Info, XCircle } from 'lucide-react';

// 模拟报警数据
const mockAlarms = [
  {
    id: 'ALM001',
    deviceId: 'DEV001',
    deviceName: '主控制器 A1',
    type: 'critical',
    message: '温度超过安全阈值',
    timestamp: '2025-07-21 11:20:15',
    acknowledged: false,
    value: '65.2°C',
    threshold: '60°C',
  },
  {
    id: 'ALM002',
    deviceId: 'DEV002',
    deviceName: '传感器 B2',
    type: 'warning',
    message: '压力值接近上限',
    timestamp: '2025-07-21 11:15:30',
    acknowledged: true,
    acknowledgedBy: '张工程师',
    value: '2.8 MPa',
    threshold: '3.0 MPa',
  },
  {
    id: 'ALM003',
    deviceId: 'DEV004',
    deviceName: '监控摄像头 D4',
    type: 'critical',
    message: '设备离线',
    timestamp: '2025-07-21 09:30:22',
    acknowledged: false,
    value: '离线',
    threshold: '-',
  },
  {
    id: 'ALM004',
    deviceId: 'DEV003',
    deviceName: '执行器 C3',
    type: 'info',
    message: '计划维护开始',
    timestamp: '2025-07-21 08:00:00',
    acknowledged: true,
    acknowledgedBy: '李技师',
    value: '维护模式',
    threshold: '-',
  },
];

const getAlarmIcon = (type: string) => {
  switch (type) {
    case 'critical':
      return <XCircle className="h-4 w-4 text-red-500" />;
    case 'warning':
      return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
    case 'info':
      return <Info className="h-4 w-4 text-blue-500" />;
    default:
      return <Info className="h-4 w-4 text-gray-500" />;
  }
};

const getAlarmBadge = (type: string) => {
  switch (type) {
    case 'critical':
      return <Badge variant="destructive">严重</Badge>;
    case 'warning':
      return <Badge className="bg-yellow-500">警告</Badge>;
    case 'info':
      return <Badge className="bg-blue-500">信息</Badge>;
    default:
      return <Badge variant="secondary">未知</Badge>;
  }
};

export default function AlarmsPage() {
  const t = useTranslations('alarms');
  const tCommon = useTranslations('common');

  const criticalCount = mockAlarms.filter(alarm => alarm.type === 'critical' && !alarm.acknowledged).length;
  const warningCount = mockAlarms.filter(alarm => alarm.type === 'warning' && !alarm.acknowledged).length;
  const totalActive = mockAlarms.filter(alarm => !alarm.acknowledged).length;

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">{t('title')}</h1>
          <p className="text-muted-foreground">
            监控和管理系统报警信息
          </p>
        </div>
        <Button variant="outline">
          <CheckCircle className="mr-2 h-4 w-4" />
          确认所有
        </Button>
      </div>

      {/* 统计卡片 */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('activeAlarms')}</CardTitle>
            <AlertTriangle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalActive}</div>
            <p className="text-xs text-muted-foreground">
              需要处理的报警
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('critical')}</CardTitle>
            <XCircle className="h-4 w-4 text-red-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{criticalCount}</div>
            <p className="text-xs text-muted-foreground">
              严重报警
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('warning')}</CardTitle>
            <AlertTriangle className="h-4 w-4 text-yellow-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">{warningCount}</div>
            <p className="text-xs text-muted-foreground">
              警告报警
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('acknowledged')}</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {mockAlarms.filter(alarm => alarm.acknowledged).length}
            </div>
            <p className="text-xs text-muted-foreground">
              已确认报警
            </p>
          </CardContent>
        </Card>
      </div>

      {/* 报警列表 */}
      <Card>
        <CardHeader>
          <CardTitle>报警列表</CardTitle>
          <CardDescription>
            查看和处理系统报警信息
          </CardDescription>
        </CardHeader>
        <CardContent>
          {/* 搜索和筛选 */}
          <div className="flex items-center space-x-2 mb-4">
            <div className="relative flex-1">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input placeholder="搜索报警..." className="pl-8" />
            </div>
            <Button variant="outline">
              <Filter className="mr-2 h-4 w-4" />
              筛选
            </Button>
          </div>

          {/* 报警表格 */}
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>级别</TableHead>
                <TableHead>设备</TableHead>
                <TableHead>报警信息</TableHead>
                <TableHead>当前值</TableHead>
                <TableHead>阈值</TableHead>
                <TableHead>时间</TableHead>
                <TableHead>状态</TableHead>
                <TableHead>操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {mockAlarms.map((alarm) => (
                <TableRow key={alarm.id} className={!alarm.acknowledged ? 'bg-muted/50' : ''}>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      {getAlarmIcon(alarm.type)}
                      {getAlarmBadge(alarm.type)}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div>
                      <div className="font-medium">{alarm.deviceName}</div>
                      <div className="text-sm text-muted-foreground">{alarm.deviceId}</div>
                    </div>
                  </TableCell>
                  <TableCell>{alarm.message}</TableCell>
                  <TableCell className="font-mono">{alarm.value}</TableCell>
                  <TableCell className="font-mono">{alarm.threshold}</TableCell>
                  <TableCell className="text-sm text-muted-foreground">
                    {alarm.timestamp}
                  </TableCell>
                  <TableCell>
                    {alarm.acknowledged ? (
                      <div>
                        <Badge className="bg-green-500">已确认</Badge>
                        <div className="text-xs text-muted-foreground mt-1">
                          {alarm.acknowledgedBy}
                        </div>
                      </div>
                    ) : (
                      <Badge variant="outline">待确认</Badge>
                    )}
                  </TableCell>
                  <TableCell>
                    {!alarm.acknowledged && (
                      <Button size="sm">
                        <CheckCircle className="mr-1 h-3 w-3" />
                        确认
                      </Button>
                    )}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
}

'use client';

import { usePathname } from 'next/navigation';
import { useTranslations } from 'next-intl';
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbPage, BreadcrumbSeparator } from '@/components/ui/breadcrumb';

const PAGE_NAMES: Record<string, string> = {
  '/': '首页',
  '/dashboard': '仪表盘',
  '/monitoring': '设备监控',
  '/process': '工步流程管理',
  '/analytics': '数据分析',
  '/events': '事件查看',
  '/battery-sorting': '电池分选',
  '/devices': '设备管理',
  '/alarms': '报警管理',
  '/settings': '系统设置'
};

export function DynamicBreadcrumb() {
  const pathname = usePathname();
  const t = useTranslations('common');

  // 移除语言前缀 (如 /zh 或 /en)
  const cleanPath = pathname.replace(/^\/[a-z]{2}/, '') || '/';
  
  // 获取页面名称
  const pageName = PAGE_NAMES[cleanPath] || '未知页面';

  return (
    <Breadcrumb>
      <BreadcrumbList>
        <BreadcrumbItem className="hidden md:block">
          <BreadcrumbLink href="/">
            朗天PDCS
          </BreadcrumbLink>
        </BreadcrumbItem>
        <BreadcrumbSeparator className="hidden md:block" />
        <BreadcrumbItem>
          <BreadcrumbPage>{pageName}</BreadcrumbPage>
        </BreadcrumbItem>
      </BreadcrumbList>
    </Breadcrumb>
  );
}

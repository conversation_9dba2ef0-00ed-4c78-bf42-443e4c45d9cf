'use client';

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  CheckCircle, 
  AlertTriangle, 
  Info, 
  Play, 
  Monitor, 
  Wrench,
  Clock
} from 'lucide-react';
import { RecentActivity } from '@/lib/types/dashboard';
import { formatDistanceToNow } from 'date-fns';
import { zhCN } from 'date-fns/locale';
import { cn } from '@/lib/utils';

interface RecentActivitiesProps {
  activities: RecentActivity[];
  className?: string;
}

const ACTIVITY_ICONS = {
  process_start: Play,
  process_complete: CheckCircle,
  device_online: Monitor,
  device_offline: Monitor,
  alarm: AlertTriangle,
  maintenance: Wrench
};

const ACTIVITY_COLORS = {
  success: 'text-green-600',
  warning: 'text-orange-600',
  error: 'text-red-600',
  info: 'text-blue-600'
};

const ACTIVITY_BADGE_VARIANTS = {
  success: 'default' as const,
  warning: 'secondary' as const,
  error: 'destructive' as const,
  info: 'outline' as const
};

export function RecentActivities({ activities, className }: RecentActivitiesProps) {
  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          最近活动
          <Badge variant="outline">{activities.length} 条记录</Badge>
        </CardTitle>
        <CardDescription>
          系统最近的重要活动和事件
        </CardDescription>
      </CardHeader>
      <CardContent>
        <ScrollArea className="h-[400px] pr-4">
          <div className="space-y-4">
            {activities.map((activity) => {
              const IconComponent = ACTIVITY_ICONS[activity.type] || Info;
              const iconColor = ACTIVITY_COLORS[activity.status];
              const badgeVariant = ACTIVITY_BADGE_VARIANTS[activity.status];
              
              return (
                <div key={activity.id} className="flex items-start space-x-3 p-3 rounded-lg border bg-card">
                  <div className={cn('mt-0.5', iconColor)}>
                    <IconComponent className="h-4 w-4" />
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between mb-1">
                      <h4 className="text-sm font-medium truncate">
                        {activity.title}
                      </h4>
                      <Badge variant={badgeVariant} className="text-xs ml-2">
                        {getStatusLabel(activity.status)}
                      </Badge>
                    </div>
                    
                    <p className="text-sm text-muted-foreground mb-2">
                      {activity.description}
                    </p>
                    
                    <div className="flex items-center justify-between text-xs text-muted-foreground">
                      <div className="flex items-center space-x-2">
                        <Clock className="h-3 w-3" />
                        <span>
                          {formatDistanceToNow(activity.timestamp, { 
                            addSuffix: true,
                            locale: zhCN 
                          })}
                        </span>
                      </div>
                      
                      {(activity.deviceId || activity.channelId || activity.processId) && (
                        <div className="flex items-center space-x-1">
                          {activity.deviceId && (
                            <Badge variant="outline" className="text-xs">
                              {activity.deviceId}
                            </Badge>
                          )}
                          {activity.channelId && (
                            <Badge variant="outline" className="text-xs">
                              {activity.channelId}
                            </Badge>
                          )}
                          {activity.processId && (
                            <Badge variant="outline" className="text-xs">
                              {activity.processId}
                            </Badge>
                          )}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              );
            })}
            
            {activities.length === 0 && (
              <div className="text-center py-8">
                <Info className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                <p className="text-muted-foreground">暂无最近活动</p>
              </div>
            )}
          </div>
        </ScrollArea>
      </CardContent>
    </Card>
  );
}

function getStatusLabel(status: string): string {
  switch (status) {
    case 'success':
      return '成功';
    case 'warning':
      return '警告';
    case 'error':
      return '错误';
    case 'info':
      return '信息';
    default:
      return status;
  }
}

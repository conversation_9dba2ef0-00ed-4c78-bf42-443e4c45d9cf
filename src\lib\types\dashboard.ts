/**
 * 仪表盘数据类型定义
 */

// KPI指标数据
export interface KPIMetric {
  id: string;
  title: string;
  value: number;
  unit?: string;
  change?: number; // 变化百分比
  changeType?: 'increase' | 'decrease' | 'stable';
  status?: 'good' | 'warning' | 'error';
  icon?: string;
}

// 设备状态分布数据
export interface DeviceStatusData {
  status: 'online' | 'offline' | 'maintenance' | 'error';
  count: number;
  percentage: number;
  color: string;
}

// 通道状态趋势数据
export interface ChannelTrendData {
  timestamp: string;
  online: number;
  running: number;
  idle: number;
  error: number;
}

// 工步执行统计数据
export interface StepExecutionData {
  stepName: string;
  completed: number;
  running: number;
  failed: number;
  total: number;
}

// 实时监控数据
export interface RealTimeData {
  timestamp: string;
  voltage: number;
  current: number;
  temperature: number;
  power: number;
}

// 系统健康度数据
export interface SystemHealthData {
  overall: number; // 总体健康度 0-100
  components: {
    devices: number;
    channels: number;
    processes: number;
    storage: number;
  };
}

// 最近活动数据
export interface RecentActivity {
  id: string;
  type: 'process_start' | 'process_complete' | 'device_online' | 'device_offline' | 'alarm' | 'maintenance';
  title: string;
  description: string;
  timestamp: Date;
  status: 'success' | 'warning' | 'error' | 'info';
  deviceId?: string;
  channelId?: string;
  processId?: string;
}

// 告警信息数据
export interface AlertData {
  id: string;
  level: 'critical' | 'warning' | 'info';
  title: string;
  message: string;
  timestamp: Date;
  acknowledged: boolean;
  deviceId?: string;
  channelId?: string;
  source: string;
}

// 性能指标数据
export interface PerformanceMetric {
  name: string;
  current: number;
  average: number;
  max: number;
  min: number;
  unit: string;
  trend: 'up' | 'down' | 'stable';
}

// 仪表盘完整数据结构
export interface DashboardData {
  kpis: KPIMetric[];
  deviceStatus: DeviceStatusData[];
  channelTrends: ChannelTrendData[];
  stepExecution: StepExecutionData[];
  realTimeData: RealTimeData[];
  systemHealth: SystemHealthData;
  recentActivities: RecentActivity[];
  alerts: AlertData[];
  performanceMetrics: PerformanceMetric[];
  lastUpdated: Date;
}

// 图表配置
export interface ChartConfig {
  colors: {
    primary: string;
    success: string;
    warning: string;
    error: string;
    info: string;
  };
  animation: boolean;
  responsive: boolean;
}

// 仪表盘配置
export interface DashboardConfig {
  refreshInterval: number; // 刷新间隔（秒）
  autoRefresh: boolean;
  charts: ChartConfig;
  layout: {
    kpiColumns: number;
    chartColumns: number;
  };
}

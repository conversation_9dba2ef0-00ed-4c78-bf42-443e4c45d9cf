'use client';

import { useTranslations } from 'next-intl';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Tabs<PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Play, Pause, RotateCcw, CheckCircle } from 'lucide-react';

import { StepEditor } from '@/components/process/step-editor/StepEditor';
import { ProcessMonitor } from '@/components/process/process-monitor/ProcessMonitor';



export default function ProcessPage() {
  const tCommon = useTranslations('common');

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">工步流程管理</h1>
          <p className="text-muted-foreground">
            配置和管理电池测试工步流程
          </p>
        </div>
      </div>

      <Tabs defaultValue="editor" className="space-y-6">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="editor">工步编辑</TabsTrigger>
          <TabsTrigger value="monitor">工艺流程监控</TabsTrigger>
        </TabsList>

        <TabsContent value="editor" className="space-y-6">
          <StepEditor useWizard={true} />
        </TabsContent>

        <TabsContent value="monitor" className="space-y-6">
          <ProcessMonitor />
        </TabsContent>
      </Tabs>
    </div>
  );
}

'use client';

import React, { useState, useEffect } from 'react';
import { useTranslations } from 'next-intl';
import { Button } from '@/components/ui/button';
import { RefreshCw, Download } from 'lucide-react';

import { KPIGrid } from '@/components/dashboard/KPICard';
import { DeviceStatusChart } from '@/components/dashboard/DeviceStatusChart';
import { ChannelTrendChart } from '@/components/dashboard/ChannelTrendChart';
import { StepExecutionChart } from '@/components/dashboard/StepExecutionChart';
import { RealTimeMonitor } from '@/components/dashboard/RealTimeMonitor';
import { RecentActivities } from '@/components/dashboard/RecentActivities';
import { AlertsPanel } from '@/components/dashboard/AlertsPanel';

import { DashboardData } from '@/lib/types/dashboard';
import { getDashboardData, refreshDashboardData } from '@/lib/services/dashboardService';

export default function DashboardPage() {
  const t = useTranslations('dashboard');
  const tCommon = useTranslations('common');

  const [dashboardData, setDashboardData] = useState<DashboardData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);

  // 加载仪表盘数据
  useEffect(() => {
    loadDashboardData();
  }, []);

  // 自动刷新数据
  useEffect(() => {
    const interval = setInterval(() => {
      if (!isRefreshing) {
        handleRefresh();
      }
    }, 30000); // 每30秒刷新一次

    return () => clearInterval(interval);
  }, [isRefreshing]);

  const loadDashboardData = async () => {
    try {
      setIsLoading(true);
      const data = await getDashboardData();
      setDashboardData(data);
    } catch (error) {
      console.error('加载仪表盘数据失败:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleRefresh = async () => {
    try {
      setIsRefreshing(true);
      const data = await refreshDashboardData();
      setDashboardData(data);
    } catch (error) {
      console.error('刷新仪表盘数据失败:', error);
    } finally {
      setIsRefreshing(false);
    }
  };

  const handleExport = () => {
    // TODO: 实现数据导出功能
    console.log('导出仪表盘数据');
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="text-center">
          <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-2" />
          <p className="text-muted-foreground">加载仪表盘数据中...</p>
        </div>
      </div>
    );
  }

  if (!dashboardData) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="text-center">
          <p className="text-muted-foreground mb-4">无法加载仪表盘数据</p>
          <Button onClick={loadDashboardData}>
            <RefreshCw className="mr-2 h-4 w-4" />
            重新加载
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">系统仪表盘</h1>
          <p className="text-muted-foreground">
            实时监控系统状态和关键性能指标
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            onClick={handleRefresh}
            disabled={isRefreshing}
          >
            <RefreshCw className={`mr-2 h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} />
            刷新
          </Button>
          <Button onClick={handleExport}>
            <Download className="mr-2 h-4 w-4" />
            导出报表
          </Button>
        </div>
      </div>

      {/* KPI指标卡片 */}
      <KPIGrid metrics={dashboardData.kpis} />

      {/* 图表区域 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <DeviceStatusChart data={dashboardData.deviceStatus} />
        <ChannelTrendChart data={dashboardData.channelTrends} />
        <StepExecutionChart data={dashboardData.stepExecution} />
        <RealTimeMonitor data={dashboardData.realTimeData} />
      </div>

      {/* 详细数据区域 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <RecentActivities activities={dashboardData.recentActivities} />
        <AlertsPanel
          alerts={dashboardData.alerts}
          onAcknowledge={(alertId) => {
            console.log('确认告警:', alertId);
            // TODO: 实现告警确认功能
          }}
          onDismiss={(alertId) => {
            console.log('忽略告警:', alertId);
            // TODO: 实现告警忽略功能
          }}
        />
      </div>

      {/* 数据更新时间 */}
      <div className="text-center text-xs text-muted-foreground">
        最后更新: {dashboardData.lastUpdated.toLocaleString()}
      </div>
    </div>
  );
}

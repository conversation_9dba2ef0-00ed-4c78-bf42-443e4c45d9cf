'use client';

import { useState, useEffect } from 'react';
import { useDeviceStore } from '@/stores/deviceStore';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { 
  Settings, 
  Wifi, 
  Download, 
  Save, 
  X,
  Network,
  Activity,
} from 'lucide-react';
import { Device, DeviceStatus } from '@/lib/types';
import { useEventStore } from '@/stores/eventStore';
import { EventType, EventLevel, EventCategory } from '@/types/events';

interface DeviceSettingsDialogProps {
  device: Device | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  mode?: 'add' | 'edit';
}

// 设备类型选项
const deviceTypes = [
  { value: 'battery_tester', label: '电池测试仪' },
  { value: 'charge_discharge', label: '充放电设备' },
  { value: 'sorting_machine', label: '分选设备' },
  { value: 'data_collector', label: '数据采集器' },
  { value: 'controller', label: '控制器' },
  { value: 'sensor', label: '传感器' },
];

export function DeviceSettingsDialog({ device, open, onOpenChange, mode = 'edit' }: DeviceSettingsDialogProps) {
  const { updateDevice, addDevice } = useDeviceStore();
  const { addEvent } = useEventStore();

  // 判断是否为添加模式
  const isAddMode = mode === 'add';

  // 表单状态
  const [formData, setFormData] = useState({
    deviceName: device?.deviceName || '',
    ip: device?.ipAddress ? device.ipAddress.join('.') : '',
    port: 502, // 默认端口
    deviceType: 'battery_tester', // 默认设备类型
    channelCount: 64, // 默认通道数量
    enabled: device?.status !== DeviceStatus.OFFLINE,
    monitoring: true, // 默认启用监听
  });

  const [isConnecting, setIsConnecting] = useState(false);
  const [isFetching, setIsFetching] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState<'idle' | 'success' | 'failed'>('idle');

  // 当模式或设备变化时重置表单
  useEffect(() => {
    if (isAddMode) {
      setFormData({
        deviceName: '',
        ip: '',
        port: 502,
        deviceType: 'battery_tester',
        channelCount: 64,
        enabled: true,
        monitoring: true,
      });
    } else if (device) {
      setFormData({
        deviceName: device.deviceName,
        ip: device.ipAddress.join('.'),
        port: 502,
        deviceType: 'battery_tester',
        channelCount: 64,
        enabled: device.status !== DeviceStatus.OFFLINE,
        monitoring: true,
      });
    }
    setConnectionStatus('idle');
  }, [isAddMode, device]);

  // 处理表单字段变更
  const handleFieldChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  // 处理IP地址变更
  const handleIpChange = (value: string) => {
    setFormData(prev => ({
      ...prev,
      ip: value,
    }));
  };

  // 连接测试（Ping）
  const handleConnect = async () => {
    setIsConnecting(true);
    setConnectionStatus('idle');
    
    try {
      // 模拟连接测试
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // 随机成功或失败（实际应用中应该是真实的ping测试）
      const success = Math.random() > 0.3;
      setConnectionStatus(success ? 'success' : 'failed');
    } catch (error) {
      setConnectionStatus('failed');
    } finally {
      setIsConnecting(false);
    }
  };

  // 从设备获取配置
  const handleFetchConfig = async () => {
    setIsFetching(true);
    
    try {
      // 模拟从设备获取配置
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // 模拟获取到的配置数据
      const fetchedConfig = {
        deviceType: 'battery_tester',
        channelCount: 64,
        port: 502,
      };
      
      setFormData(prev => ({
        ...prev,
        ...fetchedConfig,
      }));
    } catch (error) {
      console.error('获取设备配置失败:', error);
    } finally {
      setIsFetching(false);
    }
  };

  // 保存设置
  const handleSave = () => {
    const ipArray = formData.ip.split('.').map(Number);

    if (isAddMode) {
      // 添加新设备
      const newDevice: Device = {
        deviceName: formData.deviceName,
        ipAddress: ipArray,
        status: formData.enabled ? DeviceStatus.WORKING : DeviceStatus.OFFLINE,
        ambientTemp: Array(4).fill(null),
        probeTemp: Array(8).fill(null),
        boardTemp: Array(2).fill(null),
        channelTemp: Array(formData.channelCount).fill(null),
        version: 'v1.0.0',
        otherInfo: {
          location: '未设置',
          installDate: new Date().toLocaleDateString(),
          lastMaintenance: new Date().toLocaleDateString()
        }
      };

      addDevice(newDevice);

      // 记录添加设备事件
      addEvent({
        type: EventType.USER_DEVICE_CONTROL,
        category: EventCategory.USER,
        level: EventLevel.INFO,
        source: {
          type: 'user',
          userId: 'admin',
          userName: '管理员'
        },
        title: '设备添加成功',
        description: `成功添加新设备 ${formData.deviceName}`,
        data: {
          operation: 'add_device',
          target: formData.deviceName,
          parameters: {
            deviceName: formData.deviceName,
            ipAddress: formData.ip,
            deviceType: formData.deviceType,
            channelCount: formData.channelCount,
            enabled: formData.enabled
          }
        }
      });
    } else {
      // 更新现有设备
      if (!device) return;

      const updatedDevice: Device = {
        ...device,
        deviceName: formData.deviceName,
        ipAddress: ipArray,
        status: formData.enabled ? device.status : DeviceStatus.OFFLINE,
      };

      updateDevice(updatedDevice);

      // 记录编辑设备事件
      addEvent({
        type: EventType.USER_DEVICE_CONTROL,
        category: EventCategory.USER,
        level: EventLevel.INFO,
        source: {
          type: 'user',
          userId: 'admin',
          userName: '管理员'
        },
        title: '设备设置已更新',
        description: `设备 ${device.deviceName} 的设置已更新`,
        data: {
          operation: 'update_device',
          target: device.deviceName,
          parameters: {
            oldDeviceName: device.deviceName,
            newDeviceName: formData.deviceName,
            oldIpAddress: device.ipAddress.join('.'),
            newIpAddress: formData.ip,
            enabled: formData.enabled
          }
        }
      });
    }

    onOpenChange(false);
  };

  // 重置表单
  const handleReset = () => {
    if (isAddMode) {
      setFormData({
        deviceName: '',
        ip: '',
        port: 502,
        deviceType: 'battery_tester',
        channelCount: 64,
        enabled: true,
        monitoring: true,
      });
    } else {
      if (!device) return;
      setFormData({
        deviceName: device.deviceName,
        ip: device.ipAddress.join('.'),
        port: 502,
        deviceType: 'battery_tester',
        channelCount: 64,
        enabled: device.status !== DeviceStatus.OFFLINE,
        monitoring: true,
      });
    }
    setConnectionStatus('idle');
  };

  // 在添加模式下不需要device，在编辑模式下需要device
  if (!isAddMode && !device) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <div className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            <DialogTitle>{isAddMode ? '添加设备' : '设备设置'}</DialogTitle>
          </div>
          <DialogDescription>
            {isAddMode ? '添加新设备到系统中' : '配置设备的基本信息、网络参数和运行状态'}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6 py-4">
          {/* 基本信息 */}
          <div className="space-y-4">
            <h3 className="text-sm font-medium text-muted-foreground">基本信息</h3>
            
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="deviceName">设备名称</Label>
                <Input
                  id="deviceName"
                  value={formData.deviceName}
                  onChange={(e) => handleFieldChange('deviceName', e.target.value)}
                  placeholder={isAddMode ? "输入设备名称" : "设备名称"}
                  disabled={!isAddMode}
                  className={!isAddMode ? "bg-muted" : ""}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="deviceType">设备类型</Label>
                <Select
                  value={formData.deviceType}
                  onValueChange={(value) => handleFieldChange('deviceType', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="选择设备类型" />
                  </SelectTrigger>
                  <SelectContent>
                    {deviceTypes.map((type) => (
                      <SelectItem key={type.value} value={type.value}>
                        {type.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>

          <Separator />

          {/* 网络配置 */}
          <div className="space-y-4">
            <h3 className="text-sm font-medium text-muted-foreground">网络配置</h3>
            
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="ip">IP地址</Label>
                <Input
                  id="ip"
                  value={formData.ip}
                  onChange={(e) => handleIpChange(e.target.value)}
                  placeholder="*************"
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="port">端口</Label>
                <Input
                  id="port"
                  type="number"
                  value={formData.port}
                  onChange={(e) => handleFieldChange('port', parseInt(e.target.value))}
                  placeholder="502"
                />
              </div>
            </div>
          </div>

          <Separator />

          {/* 设备配置 */}
          <div className="space-y-4">
            <h3 className="text-sm font-medium text-muted-foreground">设备配置</h3>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="deviceTypeConfig">设备类型</Label>
                <Select
                  value={formData.deviceType}
                  onValueChange={(value) => handleFieldChange('deviceType', value)}
                  disabled={!isAddMode}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="选择设备类型" />
                  </SelectTrigger>
                  <SelectContent>
                    {deviceTypes.map((type) => (
                      <SelectItem key={type.value} value={type.value}>
                        {type.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="channelCountConfig">通道数量</Label>
                <Input
                  id="channelCountConfig"
                  type="number"
                  value={formData.channelCount}
                  onChange={(e) => handleFieldChange('channelCount', parseInt(e.target.value) || 64)}
                  placeholder="64"
                  min="1"
                  max="256"
                  disabled={!isAddMode}
                  className={!isAddMode ? "bg-muted" : ""}
                />
              </div>
            </div>
          </div>

          <Separator />

          {/* 运行状态 */}
          <div className="space-y-4">
            <h3 className="text-sm font-medium text-muted-foreground">运行状态</h3>
            
            <div className="flex items-center gap-6">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="enabled"
                  checked={formData.enabled}
                  onCheckedChange={(checked) => handleFieldChange('enabled', checked)}
                />
                <Label htmlFor="enabled" className="text-sm">启用设备</Label>
              </div>
              
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="monitoring"
                  checked={formData.monitoring}
                  onCheckedChange={(checked) => handleFieldChange('monitoring', checked)}
                />
                <Label htmlFor="monitoring" className="text-sm">启用监听</Label>
              </div>
            </div>
          </div>

          <Separator />

          {/* 操作按钮 */}
          <div className="space-y-4">
            <h3 className="text-sm font-medium text-muted-foreground">设备操作</h3>
            
            <div className="flex items-center gap-3">
              <Button
                variant="outline"
                size="sm"
                onClick={handleFetchConfig}
                disabled={isFetching}
                className="flex items-center gap-2"
              >
                <Download className="h-4 w-4" />
                {isFetching ? '获取中...' : '从设备获取配置'}
              </Button>
              
              <Button
                variant="outline"
                size="sm"
                onClick={handleConnect}
                disabled={isConnecting}
                className="flex items-center gap-2"
              >
                <Network className="h-4 w-4" />
                {isConnecting ? '连接中...' : '连接测试'}
              </Button>
              
              {connectionStatus !== 'idle' && (
                <Badge variant={connectionStatus === 'success' ? 'default' : 'destructive'}>
                  <Activity className="h-3 w-3 mr-1" />
                  {connectionStatus === 'success' ? '连接成功' : '连接失败'}
                </Badge>
              )}
            </div>
          </div>
        </div>

        {/* 底部按钮 */}
        <div className="flex items-center justify-end gap-3 pt-4 border-t">
          <Button variant="outline" onClick={handleReset}>
            重置
          </Button>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            取消
          </Button>
          <Button onClick={handleSave}>
            <Save className="h-4 w-4 mr-2" />
            保存设置
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}

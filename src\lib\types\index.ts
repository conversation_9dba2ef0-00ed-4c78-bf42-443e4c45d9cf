// 设备状态枚举 - 与deviceStore保持一致
export enum DeviceStatus {
  WORKING = '运行中',
  OFFLINE = '离线',
  ERROR = '故障',
  INITIALIZING = '初始化',
  CALIBRATING = '校准中'
}

// 报警级别枚举
export enum AlarmLevel {
  NONE = 0,
  INFO = 1,
  WARNING = 2,
  CRITICAL = 3
}

// 设备相关类型 - 与deviceStore统一
export interface Device {
  deviceName: string;
  ipAddress: number[];
  status: DeviceStatus;
  ambientTemp: (number | null)[];
  probeTemp: (number | null)[];
  boardTemp: (number | null)[];
  channelTemp: (number | null)[];
  version: string;
  otherInfo: {
    location: string;
    installDate: string;
    lastMaintenance: string;
  };
}

// 通道接口 - 与deviceStore统一
export interface Channel {
  channelNumber: number;
  stepStatus: string;
  stepMode: string;
  current: number;
  voltage: number;
  capacity: number;
  energy: number;
  temperature: number;
  auxVoltage: number;
  isEnabled: boolean;
  totalStepNumber: number;
  cycleCount: number;
  stepNumber: number;
  stepTime: number;
  processTime: number;
  alarmStatus: number;
  processName: string;
  trayCode: string;
  colorStatus: string;
  deviceName: string; // 关联的设备名称
}

export interface DeviceParameter {
  id: string;
  name: string;
  value: number | string | boolean;
  unit?: string;
  min?: number;
  max?: number;
  alarmThreshold?: {
    high?: number;
    low?: number;
  };
}

// 报警相关类型 - 与deviceStore统一
export interface Alarm {
  id: string;
  deviceName: string;
  channelNumber?: number;
  level: AlarmLevel;
  message: string;
  timestamp: Date;
  acknowledged: boolean;
  acknowledgedBy?: string;
  acknowledgedAt?: Date;
}

// 用户相关类型
export interface User {
  id: string;
  username: string;
  email: string;
  role: 'admin' | 'operator' | 'viewer';
  permissions: string[];
  lastLogin?: Date;
}

// API 响应类型
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// 分页类型
export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

// 图表数据类型
export interface ChartDataPoint {
  timestamp: Date;
  value: number;
  label?: string;
}

export interface ChartSeries {
  name: string;
  data: ChartDataPoint[];
  color?: string;
}

// 系统状态类型
export interface SystemStatus {
  overall: 'normal' | 'warning' | 'critical';
  activeDevices: number;
  totalDevices: number;
  activeAlarms: number;
  energyConsumption: number;
  productionRate: number;
  uptime: number;
}

// 设备统计数据类型
export interface DeviceStatistics {
  totalDevices: number;
  onlineDevices: number;
  totalChannels: number;
  activeChannels: number;
  totalAlarms: number;
  criticalAlarms: number;
  lastUpdateTime: Date | null;
}

// ==================== 电池数据分析相关类型 ====================

// 工步类型枚举
export enum StepType {
  REST = '搁置',
  CC_CHARGE = '恒流充电',
  CC_DISCHARGE = '恒流放电',
  CV_CHARGE = '恒压充电',
  CCCV_CHARGE = '恒流恒压充电'
}

// 模块开关状态
export enum ModuleSwitch {
  ON = '开启',
  OFF = '关闭'
}

// 电池测试数据记录接口
export interface BatteryDataRecord {
  // 基础信息
  dataSequence: number;           // 数据序号
  cycleNumber: number;            // 循环号
  stepNumber: number;             // 工步号
  stepType: StepType;             // 工步类型

  // 时间信息
  time: string;                   // 时间 (HH:MM:SS)
  totalTime: string;              // 总时间 (HH:MM:SS)
  absoluteTime: Date;             // 绝对时间

  // 电学参数
  current: number;                // 电流(A)
  voltage: number;                // 电压(V)
  power: number;                  // 功率(W)
  contactResistance: number;      // 接触电阻(mΩ)

  // 容量参数
  capacity: number;               // 容量(Ah)
  specificCapacity: number;       // 比容量(mAh/g)
  chargeCapacity: number;         // 充电容量(Ah)
  chargeSpecificCapacity: number; // 充电比容量(mAh/g)
  dischargeCapacity: number;      // 放电容量(Ah)
  dischargeSpecificCapacity: number; // 放电比容量(mAh/g)

  // 能量参数
  energy: number;                 // 能量(Wh)
  specificEnergy: number;         // 比能量(mWh/g)
  chargeEnergy: number;           // 充电能量(Wh)
  chargeSpecificEnergy: number;   // 充电比能量(mWh/g)
  dischargeEnergy: number;        // 放电能量(Wh)
  dischargeSpecificEnergy: number; // 放电比能量(mWh/g)

  // 微分参数
  dQdV: number;                   // dQ/dV(mAh/V)
  dQmdV: number;                  // dQm/dV(mAh/V.g)

  // 环境参数
  moduleSwitch: ModuleSwitch;     // 模块启停开关
  v1: number;                     // V1(V)
  temperature: number;            // T1(℃)
  auxVoltageDiff: number;         // 辅助通道电压差(V)
  auxTempDiff: number;            // 辅助通道温差(℃)
}

// 电池数据集合接口
export interface BatteryDataSet {
  id: string;
  name: string;
  description?: string;
  uploadTime: Date;
  fileSize: number;
  recordCount: number;
  data: BatteryDataRecord[];
  metadata: BatteryDataMetadata;
}

// 电池数据元信息
export interface BatteryDataMetadata {
  batteryType?: string;           // 电池类型
  nominalCapacity?: number;       // 标称容量(Ah)
  nominalVoltage?: number;        // 标称电压(V)
  testConditions?: {
    temperature?: number;         // 测试温度(℃)
    humidity?: number;           // 湿度(%)
    chargeRate?: number;         // 充电倍率(C)
    dischargeRate?: number;      // 放电倍率(C)
  };
  cycleRange?: {
    start: number;               // 起始循环
    end: number;                 // 结束循环
  };
  dateRange?: {
    start: Date;                 // 开始时间
    end: Date;                   // 结束时间
  };
}

// 电池性能统计分析结果
export interface BatteryPerformanceStats {
  // 容量统计
  capacityStats: {
    initial: number;             // 初始容量(Ah)
    current: number;             // 当前容量(Ah)
    retention: number;           // 容量保持率(%)
    degradationRate: number;     // 容量衰减率(%/cycle)
    maxCapacity: number;         // 最大容量(Ah)
    minCapacity: number;         // 最小容量(Ah)
    avgCapacity: number;         // 平均容量(Ah)
  };

  // 能量统计
  energyStats: {
    chargeEfficiency: number;    // 充电效率(%)
    dischargeEfficiency: number; // 放电效率(%)
    roundTripEfficiency: number; // 往返效率(%)
    energyDensity: number;       // 能量密度(Wh/kg)
    powerDensity: number;        // 功率密度(W/kg)
  };

  // 循环统计
  cycleStats: {
    totalCycles: number;         // 总循环次数
    completedCycles: number;     // 完成循环次数
    avgCycleTime: number;        // 平均循环时间(h)
    maxCycleTime: number;        // 最大循环时间(h)
    minCycleTime: number;        // 最小循环时间(h)
  };

  // 温度统计
  temperatureStats: {
    avgTemperature: number;      // 平均温度(℃)
    maxTemperature: number;      // 最高温度(℃)
    minTemperature: number;      // 最低温度(℃)
    tempVariation: number;       // 温度变化范围(℃)
  };

  // 电压统计
  voltageStats: {
    avgVoltage: number;          // 平均电压(V)
    maxVoltage: number;          // 最高电压(V)
    minVoltage: number;          // 最低电压(V)
    voltageRange: number;        // 电压范围(V)
  };
}

// 图表数据类型 - 电池专用
export interface BatteryChartData {
  // 电压-时间曲线数据
  voltageTimeData: Array<{
    time: number;                // 时间戳或相对时间
    voltage: number;             // 电压值
    stepType: StepType;          // 工步类型
    cycleNumber: number;         // 循环号
  }>;

  // 容量-循环曲线数据
  capacityCycleData: Array<{
    cycle: number;               // 循环次数
    chargeCapacity: number;      // 充电容量
    dischargeCapacity: number;   // 放电容量
    efficiency: number;          // 效率
  }>;

  // 温度-时间曲线数据
  temperatureTimeData: Array<{
    time: number;                // 时间戳
    temperature: number;         // 温度值
    voltage: number;             // 对应电压
    current: number;             // 对应电流
  }>;

  // dQ/dV微分曲线数据
  dQdVData: Array<{
    voltage: number;             // 电压
    dQdV: number;               // dQ/dV值
    cycleNumber: number;         // 循环号
  }>;
}

// 数据过滤器接口
export interface BatteryDataFilter {
  cycleRange?: {
    min: number;
    max: number;
  };
  stepTypes?: StepType[];
  timeRange?: {
    start: Date;
    end: Date;
  };
  voltageRange?: {
    min: number;
    max: number;
  };
  temperatureRange?: {
    min: number;
    max: number;
  };
}

// 数据导出配置
export interface DataExportConfig {
  format: 'csv' | 'xlsx' | 'json';
  includeMetadata: boolean;
  includeStats: boolean;
  selectedFields?: string[];
  filter?: BatteryDataFilter;
  filename?: string;
}

// CSV导入配置
export interface CSVImportConfig {
  delimiter: string;
  encoding: string;
  hasHeader: boolean;
  skipRows: number;
  fieldMapping: Record<string, string>; // CSV列名到数据字段的映射
}

// 数据分析任务状态
export enum AnalysisTaskStatus {
  PENDING = 'pending',
  RUNNING = 'running',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled'
}

// 数据分析任务接口
export interface AnalysisTask {
  id: string;
  name: string;
  type: 'capacity_analysis' | 'cycle_life' | 'efficiency_analysis' | 'temperature_analysis';
  status: AnalysisTaskStatus;
  progress: number;              // 0-100
  dataSetId: string;
  config: Record<string, any>;
  result?: any;
  error?: string;
  createdAt: Date;
  updatedAt: Date;
  completedAt?: Date;
}

// ==================== 电池分选相关类型 ====================

// 数据类型枚举 - 可用于分选的电池参数
export enum BatteryParameterType {
  // 电学参数
  VOLTAGE = 'voltage',                    // 电压
  CURRENT = 'current',                    // 电流
  POWER = 'power',                        // 功率
  RESISTANCE = 'resistance',              // 接触电阻

  // 容量参数
  CAPACITY = 'capacity',                  // 容量
  SPECIFIC_CAPACITY = 'specificCapacity', // 比容量
  CHARGE_CAPACITY = 'chargeCapacity',     // 充电容量
  DISCHARGE_CAPACITY = 'dischargeCapacity', // 放电容量

  // 能量参数
  ENERGY = 'energy',                      // 能量
  SPECIFIC_ENERGY = 'specificEnergy',     // 比能量
  CHARGE_ENERGY = 'chargeEnergy',         // 充电能量
  DISCHARGE_ENERGY = 'dischargeEnergy',   // 放电能量

  // 环境参数
  TEMPERATURE = 'temperature',            // 温度

  // 微分参数
  DQDV = 'dQdV',                         // dQ/dV
  DQMDV = 'dQmdV',                       // dQm/dV

  // 计算参数
  EFFICIENCY = 'efficiency',              // 效率
  CAPACITY_RETENTION = 'capacityRetention', // 容量保持率
  CYCLE_COUNT = 'cycleCount'              // 循环次数
}

// 比较运算符
export enum ComparisonOperator {
  GREATER_THAN = '>',
  LESS_THAN = '<',
  GREATER_EQUAL = '>=',
  LESS_EQUAL = '<=',
  EQUAL = '==',
  NOT_EQUAL = '!='
}

// 逻辑运算符
export enum LogicalOperator {
  AND = 'AND',
  OR = 'OR',
  NOT = 'NOT'
}

// 变量定义
export interface SortingVariable {
  id: string;
  name: string;                          // 变量名称（如"结束容量"）
  parameterType: BatteryParameterType;   // 数据类型
  stepNumber?: number;                   // 工步号
  cycleNumber?: number;                  // 循环次数
  description?: string;                  // 描述
}

// 分选等级定义
export interface SortingGrade {
  id: string;
  name: string;                          // 等级名称（如"A级"、"B级"）
  color: string;                         // 分选颜色（十六进制）
  priority: number;                      // 优先级（数字越小优先级越高）
  conditions: string;                    // 等级条件表达式
  description?: string;                  // 描述
}

// 条件表达式节点
export interface ConditionNode {
  id: string;
  type: 'comparison' | 'logical' | 'variable' | 'value';

  // 比较节点
  variable?: string;                     // 变量ID
  operator?: ComparisonOperator;         // 比较运算符
  value?: number;                        // 比较值

  // 逻辑节点
  logicalOperator?: LogicalOperator;     // 逻辑运算符
  children?: ConditionNode[];            // 子节点

  // 变量节点
  variableId?: string;                   // 变量引用ID

  // 值节点
  numericValue?: number;                 // 数值
}

// 分选规则
export interface SortingRule {
  id: string;
  name: string;                          // 规则名称
  description?: string;                  // 规则描述
  variables: SortingVariable[];          // 变量定义
  grades: SortingGrade[];               // 等级定义
  conditionTree: ConditionNode;         // 条件表达式树
  isActive: boolean;                     // 是否启用
  createdAt: Date;
  updatedAt: Date;
}

// 设备信息
export interface BatteryDevice {
  id: string;
  name: string;                          // 设备名称
  type: string;                          // 设备类型
  location: string;                      // 设备位置
  channels: BatteryChannel[];            // 通道列表
  status: 'online' | 'offline' | 'error'; // 设备状态
  lastUpdate: Date;                      // 最后更新时间
}

// 电池通道
export interface BatteryChannel {
  id: string;
  deviceId: string;                      // 所属设备ID
  channelNumber: number;                 // 通道号
  batteryId?: string;                    // 电池ID
  status: 'idle' | 'testing' | 'completed' | 'error'; // 通道状态
  currentStep?: number;                  // 当前工步
  currentCycle?: number;                 // 当前循环
  testData?: BatteryDataRecord[];        // 测试数据
  lastUpdate: Date;                      // 最后更新时间
}

// 分选结果
export interface SortingResult {
  id: string;
  channelId: string;                     // 通道ID
  deviceId: string;                      // 设备ID
  batteryId?: string;                    // 电池ID
  ruleId: string;                        // 使用的分选规则ID
  grade: SortingGrade | null;            // 分选等级（null表示未分级）
  errorMessage?: string;                 // 错误信息
  sortingTime: Date;                     // 分选时间
  testDataSnapshot: BatteryDataRecord[]; // 测试数据快照
  calculatedValues: Record<string, number>; // 计算得出的变量值
}

// 分选任务
export interface SortingTask {
  id: string;
  name: string;                          // 任务名称
  ruleId: string;                        // 分选规则ID
  deviceIds: string[];                   // 目标设备ID列表
  channelIds?: string[];                 // 指定通道ID列表（可选）
  status: 'pending' | 'running' | 'completed' | 'failed'; // 任务状态
  progress: number;                      // 进度百分比
  results: SortingResult[];              // 分选结果
  startTime?: Date;                      // 开始时间
  endTime?: Date;                        // 结束时间
  errorMessage?: string;                 // 错误信息
  createdAt: Date;
}

// 分选统计
export interface SortingStatistics {
  totalChannels: number;                 // 总通道数
  sortedChannels: number;                // 已分选通道数
  gradeDistribution: Record<string, number>; // 等级分布
  errorCount: number;                    // 错误数量
  averageSortingTime: number;            // 平均分选时间（毫秒）
  lastSortingTime: Date | null;          // 最后分选时间
}

// 筛选条件
export interface SortingFilter {
  dateRange?: {
    start: Date;
    end: Date;
  };
  deviceIds?: string[];                  // 设备ID筛选
  stepNumbers?: number[];                // 工步号筛选
  cycleNumbers?: number[];               // 循环号筛选
  grades?: string[];                     // 等级筛选
  status?: ('idle' | 'testing' | 'completed' | 'error')[]; // 状态筛选
}

// 分选配置
export interface SortingConfig {
  autoSorting: boolean;                  // 是否自动分选
  sortingInterval: number;               // 分选间隔（秒）
  defaultRuleId?: string;                // 默认分选规则ID
  enableNotifications: boolean;          // 是否启用通知
  maxRetries: number;                    // 最大重试次数
}

// 系统设置相关类型
export interface DisplaySettings {
  theme: 'light' | 'dark' | 'system';
  language: 'zh' | 'en';
  compactMode: boolean;
  showAnimations: boolean;
  fontSize: 'small' | 'medium' | 'large';
}

export interface UnitSettings {
  voltage: 'V' | 'mV';
  current: 'A' | 'mA';
  power: 'W' | 'mW';
  capacity: 'Ah' | 'mAh';
  energy: 'Wh' | 'mWh';
  temperature: '°C' | '°F' | 'K';
  resistance: 'Ω' | 'mΩ' | 'kΩ';
  time: 's' | 'min' | 'h';
  decimalPlaces: number; // 小数点后位数
}

export interface SystemSettings {
  display: DisplaySettings;
  units: UnitSettings;
  notifications: {
    enabled: boolean;
    sound: boolean;
    desktop: boolean;
    email: boolean;
  };
  monitoring: {
    refreshInterval: number; // 刷新间隔(秒)
    dataRetention: number; // 数据保留天数
    alertThresholds: {
      voltage: { min: number; max: number };
      current: { min: number; max: number };
      temperature: { min: number; max: number };
    };
  };
}

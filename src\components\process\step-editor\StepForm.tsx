'use client';

import React from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useTranslations } from 'next-intl';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

import { StepFormData, StepStatus, StepMode } from '@/lib/process/types/step';
import { STEP_STATUS_OPTIONS, STEP_MODE_OPTIONS } from '@/lib/process/constants/stepConstants';
import { useStepStore } from '@/lib/process/stores/stepStore';

// 表单验证Schema
const stepFormSchema = z.object({
  name: z.string().min(1, '工步名称不能为空').max(100, '工步名称不能超过100字符'),
  status: z.nativeEnum(StepStatus),
  mode: z.nativeEnum(StepMode),
  notes: z.string().max(500, '备注不能超过500字符').optional(),
  // 主要参数
  current: z.number().min(0.1, '电流最小值为0.1mA').max(10000, '电流最大值为10000mA'),
  upperVoltage: z.number().min(0, '电压最小值为0mV').max(5000, '电压最大值为5000mV'),
  lowerVoltage: z.number().min(0, '电压最小值为0mV').max(5000, '电压最大值为5000mV'),
  duration: z.string().regex(/^(\d{1,2}):(\d{1,2}):(\d{1,2})(\.\d{1,3})?$/, '时间格式应为 hh:mm:ss.ms'),
  capacity: z.number().min(0, '容量最小值为0mAh').max(50000, '容量最大值为50000mAh'),
  power: z.number().min(0, '功率最小值为0mW').max(10000, '功率最大值为10000mW'),
  upperTemperature: z.number().min(-40, '温度最小值为-40℃').max(100, '温度最大值为100℃'),
  lowerTemperature: z.number().min(-40, '温度最小值为-40℃').max(100, '温度最大值为100℃'),
  samplingInterval: z.number().min(1, '采样间隔最小值为1s').max(3600, '采样间隔最大值为3600s')
}).refine((data) => data.upperVoltage >= data.lowerVoltage, {
  message: '上限电压应大于等于下限电压',
  path: ['upperVoltage']
}).refine((data) => data.upperTemperature > data.lowerTemperature, {
  message: '温度上限应大于温度下限',
  path: ['upperTemperature']
});

type StepFormValues = z.infer<typeof stepFormSchema>;

interface StepFormProps {
  initialData?: StepFormData;
  onSubmit: (data: StepFormData) => void;
  onCancel: () => void;
  isLoading?: boolean;
}

export function StepForm({ initialData, onSubmit, onCancel, isLoading = false }: StepFormProps) {
  const t = useTranslations('process.stepForm');
  const { validationErrors } = useStepStore();

  // 转换初始数据格式
  const getDefaultValues = (): Partial<StepFormValues> => {
    if (!initialData) {
      return {
        name: '',
        status: StepStatus.DISCHARGE,
        mode: StepMode.CC,
        current: 1000,
        upperVoltage: 4200,
        lowerVoltage: 3000,
        duration: '00:01:00.000',
        capacity: 5000,
        power: 1000,
        upperTemperature: 60,
        lowerTemperature: 0,
        samplingInterval: 2,
        notes: ''
      };
    }

    return {
      name: initialData.name,
      status: initialData.status,
      mode: initialData.mode,
      current: initialData.parameters.electrical.current.value,
      upperVoltage: initialData.parameters.electrical.upperVoltage.value,
      lowerVoltage: initialData.parameters.electrical.lowerVoltage.value,
      duration: initialData.parameters.time.duration.value,
      capacity: initialData.parameters.termination.capacity.value,
      power: initialData.parameters.termination.power.value,
      upperTemperature: initialData.parameters.termination.upperTemperature.value,
      lowerTemperature: initialData.parameters.termination.lowerTemperature.value,
      samplingInterval: initialData.parameters.sampling.samplingInterval.value,
      notes: initialData.notes || ''
    };
  };

  const form = useForm<StepFormValues>({
    resolver: zodResolver(stepFormSchema),
    defaultValues: getDefaultValues()
  });

  const handleSubmit = (values: StepFormValues) => {
    // 转换为StepFormData格式
    const stepData: StepFormData = {
      name: values.name,
      status: values.status,
      mode: values.mode,
      parameters: {
        electrical: {
          current: { value: values.current, unit: 'mA', range: [0.1, 10000] },
          upperVoltage: { value: values.upperVoltage, unit: 'mV', range: [0, 5000] },
          lowerVoltage: { value: values.lowerVoltage, unit: 'mV', range: [0, 5000] }
        },
        time: {
          duration: { value: values.duration, format: 'hh:mm:ss.ms' }
        },
        termination: {
          capacity: { value: values.capacity, unit: 'mAh', range: [0, 50000] },
          power: { value: values.power, unit: 'mW', range: [0, 10000] },
          upperTemperature: { value: values.upperTemperature, unit: '℃', range: [-40, 100] },
          lowerTemperature: { value: values.lowerTemperature, unit: '℃', range: [-40, 100] }
        },
        sampling: {
          samplingInterval: { value: values.samplingInterval, unit: 's', range: [1, 3600] }
        }
      },
      auxiliaryParams: initialData?.auxiliaryParams || {
        voltage: { enabled: false, checkTime: 0, upperLimit: 0, lowerLimit: 0, upperRange: 12, lowerRange: 121, deviation: 0, absoluteValue: 0, checkInterval: 0, fluctuationValue: 0, continuousCount: 0, totalCount: 0, rateValue: 0, comparator: '>', timeInterval: 0 },
        current: { enabled: false, startTime: 0, upperCurrent: 0, lowerCurrent: 0, currentValue: 0, checkInterval: 0, dropValue: 0 },
        capacity: { enabled: false, ccCapacityLimit: { value: 0, enabled: true }, cvCapacityLimit: { value: 0, enabled: false } },
        samplingInterval: { enabled: false, voltageDiff: 5000, currentDiff: 100000, temperatureDiff: 2, capacityDiff: 100000, powerDiff: 100000 },
        stepProtection: { enabled: false, impedanceUpperLimit: 0, impedanceLowerLimit: 0 }
      },
      notes: values.notes
    };

    onSubmit(stepData);
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">
            {initialData ? '编辑工步' : '新建工步'}
          </h2>
          <p className="text-muted-foreground">
            配置工步的基本参数和测试条件
          </p>
        </div>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
          <Tabs defaultValue="basic" className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="basic">基本参数</TabsTrigger>
              <TabsTrigger value="advanced">高级设置</TabsTrigger>
            </TabsList>

            <TabsContent value="basic" className="space-y-6">
              {/* 基本信息 */}
              <Card>
                <CardHeader>
                  <CardTitle>基本信息</CardTitle>
                  <CardDescription>设置工步的基本属性</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="name"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>工步名称 *</FormLabel>
                          <FormControl>
                            <Input placeholder="请输入工步名称" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="status"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>工步状态 *</FormLabel>
                          <Select onValueChange={field.onChange} defaultValue={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="选择工步状态" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {STEP_STATUS_OPTIONS.map((option) => (
                                <SelectItem key={option.value} value={option.value}>
                                  <div className="flex items-center space-x-2">
                                    <div 
                                      className="w-3 h-3 rounded-full" 
                                      style={{ backgroundColor: option.color }}
                                    />
                                    <span>{option.label}</span>
                                  </div>
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="mode"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>工步模式 *</FormLabel>
                          <Select onValueChange={field.onChange} defaultValue={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="选择工步模式" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {STEP_MODE_OPTIONS.map((option) => (
                                <SelectItem key={option.value} value={option.value}>
                                  <div>
                                    <div className="font-medium">{option.label}</div>
                                    <div className="text-sm text-muted-foreground">{option.description}</div>
                                  </div>
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </CardContent>
              </Card>

              {/* 电气参数 */}
              <Card>
                <CardHeader>
                  <CardTitle>电气参数</CardTitle>
                  <CardDescription>设置电流和电压相关参数</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <FormField
                      control={form.control}
                      name="current"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>电流 (mA) *</FormLabel>
                          <FormControl>
                            <Input 
                              type="number" 
                              step="0.1"
                              placeholder="1000"
                              {...field}
                              onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                            />
                          </FormControl>
                          <FormDescription>范围: 0.1 - 10000 mA</FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="upperVoltage"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>上限电压 (mV) *</FormLabel>
                          <FormControl>
                            <Input 
                              type="number" 
                              step="1"
                              placeholder="4200"
                              {...field}
                              onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                            />
                          </FormControl>
                          <FormDescription>范围: 0 - 5000 mV</FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="lowerVoltage"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>下限电压 (mV) *</FormLabel>
                          <FormControl>
                            <Input 
                              type="number" 
                              step="1"
                              placeholder="3000"
                              {...field}
                              onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                            />
                          </FormControl>
                          <FormDescription>范围: 0 - 5000 mV</FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </CardContent>
              </Card>

              {/* 时间和终止条件 */}
              <Card>
                <CardHeader>
                  <CardTitle>时间和终止条件</CardTitle>
                  <CardDescription>设置工步的时间和终止条件</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <FormField
                      control={form.control}
                      name="duration"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>持续时间 *</FormLabel>
                          <FormControl>
                            <Input 
                              placeholder="00:01:00.000"
                              {...field}
                            />
                          </FormControl>
                          <FormDescription>格式: hh:mm:ss.ms</FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="capacity"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>容量 (mAh)</FormLabel>
                          <FormControl>
                            <Input 
                              type="number" 
                              step="1"
                              placeholder="5000"
                              {...field}
                              onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                            />
                          </FormControl>
                          <FormDescription>范围: 0 - 50000 mAh</FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="upperTemperature"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>温度上限 (℃)</FormLabel>
                          <FormControl>
                            <Input 
                              type="number" 
                              step="0.1"
                              placeholder="60"
                              {...field}
                              onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                            />
                          </FormControl>
                          <FormDescription>范围: -40 - 100 ℃</FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="samplingInterval"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>采样间隔 (s)</FormLabel>
                          <FormControl>
                            <Input 
                              type="number" 
                              step="1"
                              placeholder="2"
                              {...field}
                              onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                            />
                          </FormControl>
                          <FormDescription>范围: 1 - 3600 s</FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </CardContent>
              </Card>

              {/* 备注 */}
              <Card>
                <CardHeader>
                  <CardTitle>备注信息</CardTitle>
                </CardHeader>
                <CardContent>
                  <FormField
                    control={form.control}
                    name="notes"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>备注</FormLabel>
                        <FormControl>
                          <Textarea 
                            placeholder="请输入备注信息..."
                            className="min-h-[100px]"
                            {...field}
                          />
                        </FormControl>
                        <FormDescription>最多500字符</FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="advanced">
              <Card>
                <CardHeader>
                  <CardTitle>高级设置</CardTitle>
                  <CardDescription>辅助参数组配置将在后续版本中实现</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="text-center py-8 text-muted-foreground">
                    <p>辅助参数组配置功能正在开发中...</p>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>

          {/* 操作按钮 */}
          <div className="flex items-center justify-end space-x-4">
            <Button type="button" variant="outline" onClick={onCancel}>
              取消
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? '保存中...' : '保存'}
            </Button>
          </div>
        </form>
      </Form>

      {/* 验证错误显示 */}
      {validationErrors.length > 0 && (
        <Card className="border-destructive">
          <CardHeader>
            <CardTitle className="text-destructive">验证错误</CardTitle>
          </CardHeader>
          <CardContent>
            <ul className="space-y-1">
              {validationErrors.map((error, index) => (
                <li key={index} className="text-sm text-destructive">
                  {error.field}: {error.message}
                </li>
              ))}
            </ul>
          </CardContent>
        </Card>
      )}
    </div>
  );
}

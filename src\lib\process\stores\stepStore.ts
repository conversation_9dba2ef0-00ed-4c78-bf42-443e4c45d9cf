/**
 * 工步状态管理Store
 * 使用Zustand进行状态管理
 */

import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { 
  Step, 
  StepFormData, 
  StepListQuery, 
  StepListResponse, 
  StepBatchOperation,
  StepOperationResult,
  StepValidationError
} from '../types/step';
import { DEFAULT_STEP_PARAMETERS, DEFAULT_AUXILIARY_PARAMETERS } from '../constants/stepConstants';
import { ProcessService } from '../services/processService';

interface StepStore {
  // 状态
  steps: Step[];
  currentStep: Step | null;
  selectedStepIds: string[];
  isLoading: boolean;
  error: string | null;
  
  // 列表查询状态
  listQuery: StepListQuery;
  listResponse: StepListResponse | null;
  
  // 编辑状态
  isEditing: boolean;
  editingStep: StepFormData | null;
  validationErrors: StepValidationError[];
  
  // 批量操作状态
  isBatchMode: boolean;
  batchOperation: StepBatchOperation | null;

  // Actions
  // 基础操作
  setSteps: (steps: Step[]) => void;
  setCurrentStep: (step: Step | null) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  
  // 列表操作
  setListQuery: (query: Partial<StepListQuery>) => void;
  setListResponse: (response: StepListResponse) => void;
  loadSteps: (query?: StepListQuery) => Promise<void>;
  loadSampleSteps: () => Promise<void>;
  refreshSteps: () => Promise<void>;
  
  // CRUD操作
  createStep: (stepData: StepFormData) => Promise<StepOperationResult>;
  updateStep: (id: string, stepData: StepFormData) => Promise<StepOperationResult>;
  deleteStep: (id: string) => Promise<StepOperationResult>;
  duplicateStep: (id: string) => Promise<StepOperationResult>;

  // 工艺流程相关操作
  createStepInProcess: (processId: string, stepData: StepFormData) => Promise<StepOperationResult>;
  updateStepInProcess: (processId: string, stepId: string, stepData: StepFormData) => Promise<StepOperationResult>;
  deleteStepFromProcess: (processId: string, stepId: string) => Promise<StepOperationResult>;
  
  // 编辑操作
  startEditing: (step?: Step) => void;
  stopEditing: () => void;
  updateEditingStep: (stepData: Partial<StepFormData>) => void;
  validateEditingStep: () => StepValidationError[];
  saveEditingStep: () => Promise<StepOperationResult>;
  saveEditingStepToProcess: (processId: string) => Promise<StepOperationResult>;
  
  // 选择操作
  selectStep: (id: string) => void;
  selectMultipleSteps: (ids: string[]) => void;
  toggleStepSelection: (id: string) => void;
  selectAllSteps: () => void;
  clearSelection: () => void;
  
  // 批量操作
  enterBatchMode: () => void;
  exitBatchMode: () => void;
  executeBatchOperation: (operation: StepBatchOperation) => Promise<StepOperationResult>;
  
  // 排序和移动
  moveStep: (fromIndex: number, toIndex: number) => Promise<StepOperationResult>;
  reorderSteps: (stepIds: string[]) => Promise<StepOperationResult>;
  
  // 搜索和筛选
  searchSteps: (searchTerm: string) => void;
  filterSteps: (filters: Partial<StepListQuery>) => void;
  
  // 重置
  reset: () => void;
}

export const useStepStore = create<StepStore>()(
  devtools(
    (set, get) => ({
      // 初始状态
      steps: [],
      currentStep: null,
      selectedStepIds: [],
      isLoading: false,
      error: null,
      
      listQuery: {
        page: 1,
        pageSize: 20,
        sortBy: 'number',
        sortOrder: 'asc'
      },
      listResponse: null,
      
      isEditing: false,
      editingStep: null,
      validationErrors: [],
      
      isBatchMode: false,
      batchOperation: null,

      // 基础操作
      setSteps: (steps) => set({ steps }),
      setCurrentStep: (step) => set({ currentStep: step }),
      setLoading: (loading) => set({ isLoading: loading }),
      setError: (error) => set({ error }),

      // 列表操作
      setListQuery: (query) => set((state) => ({
        listQuery: { ...state.listQuery, ...query }
      })),
      
      setListResponse: (response) => set({ listResponse: response }),
      
      loadSteps: async (query) => {
        const state = get();
        const finalQuery = query || state.listQuery;

        set({ isLoading: true, error: null });

        try {
          // 这里应该调用实际的API
          // const response = await stepService.getSteps(finalQuery);

          // 不自动加载示例数据，返回空数组
          // 示例数据只有在明确需要时才加载
          const mockResponse: StepListResponse = {
            steps: [],
            total: 0,
            page: finalQuery.page || 1,
            pageSize: finalQuery.pageSize || 20,
            totalPages: 0
          };

          set({
            listResponse: mockResponse,
            steps: mockResponse.steps,
            listQuery: finalQuery,
            isLoading: false
          });
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : '加载工步列表失败',
            isLoading: false
          });
        }
      },

      // 新增：加载示例数据的专用方法
      loadSampleSteps: async () => {
        const state = get();

        set({ isLoading: true, error: null });

        try {
          const mockSteps: Step[] = [
            {
              id: 'step_1',
              number: 1,
              name: '恒流充电',
              status: 'charge',
              mode: 'cc',
              parameters: {
                electrical: {
                  current: { value: 1000, unit: 'mA', range: [0.1, 10000] },
                  upperVoltage: { value: 4200, unit: 'mV', range: [0, 5000] },
                  lowerVoltage: { value: 3000, unit: 'mV', range: [0, 5000] }
                },
                time: {
                  duration: { value: '01:00:00.000', format: 'hh:mm:ss.ms' }
                },
                termination: {
                  capacity: { value: 5000, unit: 'mAh', range: [0, 50000] },
                  power: { value: 1000, unit: 'mW', range: [0, 10000] },
                  upperTemperature: { value: 60, unit: '℃', range: [-40, 100] },
                  lowerTemperature: { value: 0, unit: '℃', range: [-40, 100] }
                },
                sampling: {
                  samplingInterval: { value: 2, unit: 's', range: [1, 3600] }
                }
              },
              auxiliaryParams: {
                voltage: {
                  enabled: true,
                  checkTime: 10,
                  upperLimit: 4300,
                  lowerLimit: 2800,
                  upperRange: 4250,
                  lowerRange: 2900,
                  deviation: 50,
                  absoluteValue: 100,
                  checkInterval: 5,
                  fluctuationValue: 10,
                  continuousCount: 3,
                  totalCount: 10,
                  rateValue: 0.1,
                  comparator: '>=' as const,
                  timeInterval: 1
                },
                current: {
                  enabled: true,
                  startTime: 0,
                  upperCurrent: 1100,
                  lowerCurrent: 900,
                  currentValue: 1000,
                  checkInterval: 5,
                  dropValue: 50
                },
                capacity: {
                  enabled: true,
                  ccCapacityLimit: { value: 5000, enabled: true },
                  cvCapacityLimit: { value: 1000, enabled: true }
                },
                samplingInterval: {
                  enabled: true,
                  voltageDiff: 10,
                  currentDiff: 50,
                  temperatureDiff: 2,
                  capacityDiff: 100,
                  powerDiff: 100
                },
                stepProtection: {
                  enabled: true,
                  impedanceUpperLimit: 1000,
                  impedanceLowerLimit: 10
                }
              },
              createdAt: new Date('2024-01-01'),
              updatedAt: new Date('2024-01-01'),
              notes: '标准恒流充电工步'
            },
            {
              id: 'step_2',
              number: 2,
              name: '恒压充电',
              status: 'charge',
              mode: 'cv',
              parameters: {
                electrical: {
                  current: { value: 500, unit: 'mA', range: [0.1, 10000] },
                  upperVoltage: { value: 4200, unit: 'mV', range: [0, 5000] },
                  lowerVoltage: { value: 4200, unit: 'mV', range: [0, 5000] }
                },
                time: {
                  duration: { value: '00:30:00.000', format: 'hh:mm:ss.ms' }
                },
                termination: {
                  capacity: { value: 2000, unit: 'mAh', range: [0, 50000] },
                  power: { value: 500, unit: 'mW', range: [0, 10000] },
                  upperTemperature: { value: 60, unit: '℃', range: [-40, 100] },
                  lowerTemperature: { value: 0, unit: '℃', range: [-40, 100] }
                },
                sampling: {
                  samplingInterval: { value: 2, unit: 's', range: [1, 3600] }
                }
              },
              auxiliaryParams: {
                voltage: {
                  enabled: true,
                  checkTime: 10,
                  upperLimit: 4250,
                  lowerLimit: 4150,
                  upperRange: 4220,
                  lowerRange: 4180,
                  deviation: 20,
                  absoluteValue: 50,
                  checkInterval: 5,
                  fluctuationValue: 5,
                  continuousCount: 5,
                  totalCount: 15,
                  rateValue: 0.05,
                  comparator: '<=' as const,
                  timeInterval: 1
                },
                current: {
                  enabled: true,
                  startTime: 0,
                  upperCurrent: 550,
                  lowerCurrent: 450,
                  currentValue: 500,
                  checkInterval: 5,
                  dropValue: 25
                },
                capacity: {
                  enabled: true,
                  ccCapacityLimit: { value: 2000, enabled: true },
                  cvCapacityLimit: { value: 500, enabled: true }
                },
                samplingInterval: {
                  enabled: true,
                  voltageDiff: 5,
                  currentDiff: 25,
                  temperatureDiff: 1,
                  capacityDiff: 50,
                  powerDiff: 50
                },
                stepProtection: {
                  enabled: true,
                  impedanceUpperLimit: 1000,
                  impedanceLowerLimit: 10
                }
              },
              createdAt: new Date('2024-01-01'),
              updatedAt: new Date('2024-01-01'),
              notes: '恒压充电至截止电流'
            },
            {
              id: 'step_3',
              number: 3,
              name: '恒流放电',
              status: 'discharge',
              mode: 'cc',
              parameters: {
                electrical: {
                  current: { value: 1000, unit: 'mA', range: [0.1, 10000] },
                  upperVoltage: { value: 4200, unit: 'mV', range: [0, 5000] },
                  lowerVoltage: { value: 3000, unit: 'mV', range: [0, 5000] }
                },
                time: {
                  duration: { value: '02:00:00.000', format: 'hh:mm:ss.ms' }
                },
                termination: {
                  capacity: { value: 8000, unit: 'mAh', range: [0, 50000] },
                  power: { value: 1000, unit: 'mW', range: [0, 10000] },
                  upperTemperature: { value: 60, unit: '℃', range: [-40, 100] },
                  lowerTemperature: { value: 0, unit: '℃', range: [-40, 100] }
                },
                sampling: {
                  samplingInterval: { value: 2, unit: 's', range: [1, 3600] }
                }
              },
              auxiliaryParams: {
                voltage: {
                  enabled: true,
                  checkTime: 10,
                  upperLimit: 4300,
                  lowerLimit: 2800,
                  upperRange: 4250,
                  lowerRange: 2900,
                  deviation: 50,
                  absoluteValue: 100,
                  checkInterval: 5,
                  fluctuationValue: 10,
                  continuousCount: 3,
                  totalCount: 10,
                  rateValue: 0.1,
                  comparator: '<=' as const,
                  timeInterval: 1
                },
                current: {
                  enabled: true,
                  startTime: 0,
                  upperCurrent: 1100,
                  lowerCurrent: 900,
                  currentValue: 1000,
                  checkInterval: 5,
                  dropValue: 50
                },
                capacity: {
                  enabled: true,
                  ccCapacityLimit: { value: 8000, enabled: true },
                  cvCapacityLimit: { value: 1000, enabled: true }
                },
                samplingInterval: {
                  enabled: true,
                  voltageDiff: 10,
                  currentDiff: 50,
                  temperatureDiff: 2,
                  capacityDiff: 100,
                  powerDiff: 100
                },
                stepProtection: {
                  enabled: true,
                  impedanceUpperLimit: 1000,
                  impedanceLowerLimit: 10
                }
              },
              createdAt: new Date('2024-01-01'),
              updatedAt: new Date('2024-01-01'),
              notes: '恒流放电至截止电压'
            }
          ];

          const mockResponse: StepListResponse = {
            steps: mockSteps,
            total: mockSteps.length,
            page: 1,
            pageSize: 20,
            totalPages: Math.ceil(mockSteps.length / 20)
          };

          set({
            listResponse: mockResponse,
            steps: mockResponse.steps,
            listQuery: { page: 1, pageSize: 20 },
            isLoading: false
          });
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : '加载示例工步失败',
            isLoading: false
          });
        }
      },
      
      refreshSteps: async () => {
        const { listQuery, loadSteps } = get();
        await loadSteps(listQuery);
      },

      // CRUD操作
      createStep: async (stepData) => {
        set({ isLoading: true, error: null });
        
        try {
          // 这里应该调用实际的API
          // const result = await stepService.createStep(stepData);
          
          // 模拟创建
          const newStep: Step = {
            id: `step_${Date.now()}`,
            number: get().steps.length + 1,
            name: stepData.name || `工步${get().steps.length + 1}`,
            status: stepData.status,
            mode: stepData.mode,
            parameters: stepData.parameters,
            auxiliaryParams: stepData.auxiliaryParams,
            createdAt: new Date(),
            updatedAt: new Date(),
            notes: stepData.notes
          };
          
          set((state) => ({
            steps: [...state.steps, newStep],
            isLoading: false
          }));
          
          return { success: true, data: newStep };
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : '创建工步失败';
          set({ error: errorMessage, isLoading: false });
          return { success: false, message: errorMessage };
        }
      },
      
      updateStep: async (id, stepData) => {
        set({ isLoading: true, error: null });
        
        try {
          // 这里应该调用实际的API
          // const result = await stepService.updateStep(id, stepData);
          
          // 模拟更新
          set((state) => ({
            steps: state.steps.map(step => 
              step.id === id 
                ? { 
                    ...step, 
                    ...stepData, 
                    updatedAt: new Date() 
                  }
                : step
            ),
            isLoading: false
          }));
          
          return { success: true };
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : '更新工步失败';
          set({ error: errorMessage, isLoading: false });
          return { success: false, message: errorMessage };
        }
      },
      
      deleteStep: async (id) => {
        set({ isLoading: true, error: null });
        
        try {
          // 这里应该调用实际的API
          // await stepService.deleteStep(id);
          
          set((state) => ({
            steps: state.steps.filter(step => step.id !== id),
            selectedStepIds: state.selectedStepIds.filter(stepId => stepId !== id),
            currentStep: state.currentStep?.id === id ? null : state.currentStep,
            isLoading: false
          }));
          
          return { success: true };
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : '删除工步失败';
          set({ error: errorMessage, isLoading: false });
          return { success: false, message: errorMessage };
        }
      },
      
      duplicateStep: async (id) => {
        const state = get();
        const originalStep = state.steps.find(step => step.id === id);
        
        if (!originalStep) {
          return { success: false, message: '未找到要复制的工步' };
        }
        
        const duplicatedStepData: StepFormData = {
          name: `${originalStep.name} (副本)`,
          status: originalStep.status,
          mode: originalStep.mode,
          parameters: originalStep.parameters,
          auxiliaryParams: originalStep.auxiliaryParams,
          notes: originalStep.notes
        };
        
        return await get().createStep(duplicatedStepData);
      },

      // 工艺流程相关操作
      createStepInProcess: async (processId, stepData) => {
        set({ isLoading: true, error: null });

        try {
          const processService = new ProcessService();

          // 创建工步对象
          const newStep: Step = {
            id: `step_${Date.now()}`,
            number: get().steps.length + 1,
            name: stepData.name || `工步${get().steps.length + 1}`,
            status: stepData.status,
            mode: stepData.mode,
            parameters: stepData.parameters,
            auxiliaryParams: stepData.auxiliaryParams,
            createdAt: new Date(),
            updatedAt: new Date(),
            notes: stepData.notes
          };

          // 保存到数据库
          const processStep = await processService.addStepToProcess(processId, newStep);

          // 更新本地状态
          set((state) => ({
            steps: [...state.steps, newStep],
            isLoading: false
          }));

          return { success: true, data: newStep };
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : '创建工步失败';
          set({ error: errorMessage, isLoading: false });
          return { success: false, message: errorMessage };
        }
      },

      updateStepInProcess: async (processId, stepId, stepData) => {
        set({ isLoading: true, error: null });

        try {
          const processService = new ProcessService();

          // 找到要更新的工步
          const existingStep = get().steps.find(s => s.id === stepId);
          if (!existingStep) {
            throw new Error('工步不存在');
          }

          // 创建更新后的工步对象
          const updatedStep: Step = {
            ...existingStep,
            name: stepData.name,
            status: stepData.status,
            mode: stepData.mode,
            parameters: stepData.parameters,
            auxiliaryParams: stepData.auxiliaryParams,
            notes: stepData.notes,
            updatedAt: new Date()
          };

          // 保存到数据库
          const success = await processService.updateStepInProcess(processId, stepId, updatedStep);

          if (success) {
            // 更新本地状态
            set((state) => ({
              steps: state.steps.map(step =>
                step.id === stepId ? updatedStep : step
              ),
              isLoading: false
            }));

            return { success: true, data: updatedStep };
          } else {
            throw new Error('更新工步失败');
          }
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : '更新工步失败';
          set({ error: errorMessage, isLoading: false });
          return { success: false, message: errorMessage };
        }
      },

      deleteStepFromProcess: async (processId, stepId) => {
        set({ isLoading: true, error: null });

        try {
          const processService = new ProcessService();

          // 从数据库删除
          const success = await processService.removeStepFromProcess(processId, stepId);

          if (success) {
            // 更新本地状态
            set((state) => ({
              steps: state.steps.filter(step => step.id !== stepId),
              isLoading: false
            }));

            return { success: true };
          } else {
            throw new Error('删除工步失败');
          }
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : '删除工步失败';
          set({ error: errorMessage, isLoading: false });
          return { success: false, message: errorMessage };
        }
      },

      // 编辑操作
      startEditing: (step) => {
        if (step) {
          const editingStep: StepFormData = {
            name: step.name,
            status: step.status,
            mode: step.mode,
            parameters: step.parameters,
            auxiliaryParams: step.auxiliaryParams,
            notes: step.notes
          };
          set({ isEditing: true, editingStep, validationErrors: [] });
        } else {
          // 创建新工步
          const newStepData: StepFormData = {
            name: '',
            status: 'discharge' as any,
            mode: 'cc' as any,
            parameters: DEFAULT_STEP_PARAMETERS,
            auxiliaryParams: DEFAULT_AUXILIARY_PARAMETERS
          };
          set({ isEditing: true, editingStep: newStepData, validationErrors: [] });
        }
      },
      
      stopEditing: () => set({ 
        isEditing: false, 
        editingStep: null, 
        validationErrors: [] 
      }),
      
      updateEditingStep: (stepData) => set((state) => ({
        editingStep: state.editingStep ? { ...state.editingStep, ...stepData } : null
      })),
      
      validateEditingStep: () => {
        const { editingStep } = get();
        if (!editingStep) return [];
        
        // 这里应该调用验证函数
        // const errors = validateStepFormData(editingStep);
        const errors: StepValidationError[] = [];
        
        set({ validationErrors: errors });
        return errors;
      },
      
      saveEditingStep: async () => {
        const { editingStep, currentStep } = get();
        if (!editingStep) {
          return { success: false, message: '没有可保存的工步数据' };
        }
        
        const errors = get().validateEditingStep();
        if (errors.length > 0) {
          return { success: false, errors, message: '请修正验证错误后再保存' };
        }
        
        let result: StepOperationResult;
        if (currentStep) {
          result = await get().updateStep(currentStep.id, editingStep);
        } else {
          result = await get().createStep(editingStep);
        }
        
        if (result.success) {
          get().stopEditing();
        }
        
        return result;
      },

      saveEditingStepToProcess: async (processId) => {
        const { editingStep, currentStep } = get();
        if (!editingStep) {
          return { success: false, message: '没有可保存的工步数据' };
        }

        const errors = get().validateEditingStep();
        if (errors.length > 0) {
          return { success: false, errors, message: '请修正验证错误后再保存' };
        }

        let result: StepOperationResult;
        if (currentStep) {
          result = await get().updateStepInProcess(processId, currentStep.id, editingStep);
        } else {
          result = await get().createStepInProcess(processId, editingStep);
        }

        if (result.success) {
          get().stopEditing();
        }

        return result;
      },

      // 选择操作
      selectStep: (id) => set({ selectedStepIds: [id] }),
      selectMultipleSteps: (ids) => set({ selectedStepIds: ids }),
      toggleStepSelection: (id) => set((state) => ({
        selectedStepIds: state.selectedStepIds.includes(id)
          ? state.selectedStepIds.filter(stepId => stepId !== id)
          : [...state.selectedStepIds, id]
      })),
      selectAllSteps: () => set((state) => ({
        selectedStepIds: state.steps.map(step => step.id)
      })),
      clearSelection: () => set({ selectedStepIds: [] }),

      // 批量操作
      enterBatchMode: () => set({ isBatchMode: true }),
      exitBatchMode: () => set({ 
        isBatchMode: false, 
        selectedStepIds: [], 
        batchOperation: null 
      }),
      
      executeBatchOperation: async (operation) => {
        set({ isLoading: true, error: null, batchOperation: operation });
        
        try {
          // 这里应该调用实际的API
          // const result = await stepService.batchOperation(operation);
          
          // 模拟批量操作
          switch (operation.action) {
            case 'delete':
              set((state) => ({
                steps: state.steps.filter(step => !operation.stepIds.includes(step.id)),
                selectedStepIds: []
              }));
              break;
            // 其他操作...
          }
          
          set({ isLoading: false, batchOperation: null });
          return { success: true };
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : '批量操作失败';
          set({ error: errorMessage, isLoading: false, batchOperation: null });
          return { success: false, message: errorMessage };
        }
      },

      // 移动和排序
      moveStep: async (fromIndex, toIndex) => {
        // 实现工步移动逻辑
        return { success: true };
      },
      
      reorderSteps: async (stepIds) => {
        // 实现工步重排序逻辑
        return { success: true };
      },

      // 搜索和筛选
      searchSteps: (searchTerm) => {
        get().setListQuery({ search: searchTerm, page: 1 });
        get().loadSteps();
      },
      
      filterSteps: (filters) => {
        get().setListQuery({ ...filters, page: 1 });
        get().loadSteps();
      },

      // 重置
      reset: () => set({
        steps: [],
        currentStep: null,
        selectedStepIds: [],
        isLoading: false,
        error: null,
        listQuery: {
          page: 1,
          pageSize: 20,
          sortBy: 'number',
          sortOrder: 'asc'
        },
        listResponse: null,
        isEditing: false,
        editingStep: null,
        validationErrors: [],
        isBatchMode: false,
        batchOperation: null
      })
    }),
    {
      name: 'step-store'
    }
  )
);

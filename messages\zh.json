{"common": {"welcome": "欢迎使用朗天PDCS", "dashboard": "仪表板", "devices": "设备", "alarms": "报警", "reports": "报表", "settings": "设置", "logout": "退出登录", "login": "登录", "loading": "加载中...", "save": "保存", "cancel": "取消", "delete": "删除", "edit": "编辑", "add": "添加", "search": "搜索", "filter": "筛选", "export": "导出", "import": "导入"}, "navigation": {"home": "首页", "overview": "概览", "monitoring": "监控", "control": "控制", "maintenance": "维护", "analytics": "分析"}, "dashboard": {"title": "系统仪表板", "systemStatus": "系统状态", "activeDevices": "活跃设备", "totalAlarms": "总报警数", "energyConsumption": "能耗", "productionRate": "生产率"}, "devices": {"title": "设备管理", "deviceList": "设备列表", "deviceStatus": "设备状态", "online": "在线", "offline": "离线", "maintenance": "维护中", "error": "故障"}, "alarms": {"title": "报警管理", "activeAlarms": "活跃报警", "alarmHistory": "报警历史", "critical": "严重", "warning": "警告", "info": "信息", "acknowledged": "已确认"}, "events": {"title": "事件查看", "description": "查看和管理系统事件，包括设备状态、通道操作、用户行为等", "totalEvents": "总事件数", "criticalEvents": "严重事件", "deviceEvents": "设备事件", "channelEvents": "通道事件", "userEvents": "用户事件", "systemEvents": "系统事件", "recentEvents": "最近24小时", "needAttention": "需要关注的事件", "deviceStatusChange": "设备状态变更", "channelOperations": "通道操作记录", "realTimeUpdate": "实时更新", "export": "导出", "settings": "设置", "filter": "筛选", "eventList": "事件列表", "eventListDescription": "按时间倒序显示的系统事件", "loading": "加载中...", "noEvents": "暂无事件数据", "showingEvents": "显示 {filtered} / {total} 个事件", "previousPage": "上一页", "nextPage": "下一页", "pageInfo": "第 {current} 页，共 {total} 页", "quickTime": {"lastHour": "最近1小时", "today": "今天", "yesterday": "昨天", "last7Days": "最近7天", "last30Days": "最近30天", "thisMonth": "本月", "lastMonth": "上月"}, "levels": {"info": "信息", "warning": "警告", "error": "错误", "critical": "严重"}, "categories": {"device": "设备", "channel": "通道", "user": "用户", "system": "系统"}, "types": {"device_online": "设备上线", "device_offline": "设备离线", "device_maintenance": "设备维护", "device_error": "设备故障", "device_recovered": "设备恢复", "channel_enabled": "通道启用", "channel_disabled": "通道禁用", "channel_step_changed": "工步变更", "channel_parameter_exceeded": "参数超限", "channel_process_started": "流程开始", "channel_process_completed": "流程完成", "channel_process_stopped": "流程停止", "user_login": "用户登录", "user_logout": "用户登出", "user_device_control": "设备控制", "user_channel_operation": "通道操作", "user_config_changed": "配置变更", "user_settings_modified": "设置修改", "system_startup": "系统启动", "system_shutdown": "系统关闭", "system_error": "系统错误", "system_warning": "系统警告", "system_backup_created": "备份创建", "system_update_installed": "更新安装"}}, "batteryAnalysis": {"title": "电池数据分析", "description": "电池测试数据的可视化分析和统计报告", "dataPoints": "数据点", "exportCSV": "导出CSV", "exportJSON": "导出JSON", "loadNewFile": "加载新文件", "recordCount": "记录数量", "cycleRange": "循环范围", "fileSize": "文件大小", "uploadTime": "上传时间", "charts": {"voltage": "电压分析", "capacity": {"title": "容量分析", "noData": "暂无数据", "totalCycles": "总循环数", "currentRetention": "当前保持率", "initialCapacity": "初始容量", "currentCapacity": "当前容量", "fadeRate": "衰减率", "avgEfficiency": "平均效率", "chargeCapacity": "充电容量", "dischargeCapacity": "放电容量", "efficiency": "库仑效率", "retention": "容量保持率", "energyEfficiency": "能量效率", "cycleAxis": "循环次数", "capacityAxis": "容量 (Ah)", "efficiencyAxis": "效率 (%)", "retentionAxis": "保持率 (%)", "coulombicEfficiency": "库仑效率", "capacityRetention": "容量保持率", "tabs": {"capacity": "容量对比", "efficiency": "效率分析", "retention": "保持率"}, "tooltip": {"title": "循环"}}, "temperature": {"title": "温度监控", "noData": "暂无数据", "dataPoints": "数据点数", "criticalAlerts": "严重警告", "warnings": "温度警告", "maxTemp": "最高温度", "minTemp": "最低温度", "avgTemp": "平均温度", "tempRange": "温度范围", "timeAxis": "时间", "temperatureAxis": "温度 (°C)", "warningThreshold": "警告阈值", "criticalThreshold": "严重阈值", "avgLine": "平均值", "temperature": "温度", "voltage": "电压", "current": "电流", "stepType": "工步类型", "cycle": "循环", "time": "时间", "criticalWarning": "检测到 {count} 个严重温度警告点 (>{threshold}°C)", "temperatureWarning": "检测到 {count} 个温度警告点 (>{threshold}°C)", "tooltip": {"title": "温度数据"}}, "differential": {"title": "微分分析 (dQ/dV)", "noData": "暂无数据", "dataPoints": "数据点数", "cycles": "循环数", "maxDQDV": "最大dQ/dV", "avgDQDV": "平均dQ/dV", "voltageRange": "电压范围", "totalPoints": "总数据点", "voltageAxis": "电压 (V)", "voltage": "电压", "cycle": "循环", "stepType": "工步类型", "current": "电流", "charge": "充电", "discharge": "放电", "avgLine": "平均值", "tabs": {"byCycle": "按循环", "byStepType": "按工步类型", "combined": "综合视图"}, "tooltip": {"title": "微分数据"}}, "voltageTime": {"title": "电压-时间曲线", "noData": "暂无数据", "dataPoints": "数据点数", "cycles": "循环数", "maxVoltage": "最高电压", "minVoltage": "最低电压", "avgVoltage": "平均电压", "timeAxis": "时间", "voltageAxis": "电压 (V)", "avgLine": "平均值", "voltage": "电压", "current": "电流", "stepType": "工步类型", "cycle": "循环", "temperature": "温度", "time": "时间", "tooltip": {"title": "电压数据"}}}, "fileUpload": {"title": "文件上传", "dragDrop": "拖拽文件到此处或点击选择文件", "dropHere": "释放文件到此处", "supportedFormats": "支持格式", "maxSize": "最大文件大小", "selectFile": "选择文件", "uploading": "上传中", "success": "文件上传成功", "recordCount": "记录数量", "fileSize": "文件大小", "cycleRange": "循环范围", "uploadTime": "上传时间"}, "stats": {"title": "统计分析", "noData": "暂无数据", "batteryHealth": {"title": "电池健康状态", "capacityRetention": "容量保持率", "initialCapacity": "初始容量", "currentCapacity": "当前容量", "status": {"excellent": "优秀", "good": "良好", "fair": "一般", "poor": "较差"}}, "capacity": {"title": "容量统计", "maxCapacity": "最大容量", "avgCapacity": "平均容量", "minCapacity": "最小容量", "fadeRate": "衰减率"}, "energy": {"title": "能量效率", "chargeEfficiency": "充电效率", "dischargeEfficiency": "放电效率", "roundTripEfficiency": "往返效率"}, "cycle": {"title": "循环统计", "totalCycles": "总循环数", "completedCycles": "完成循环", "avgCycleTime": "平均循环时间", "maxCycleTime": "最大循环时间"}, "temperature": {"title": "温度统计", "maxTemp": "最高温度", "avgTemp": "平均温度", "minTemp": "最低温度", "variation": "温度变化"}, "lifetime": {"title": "寿命预测", "predictedCycles": "预测循环数", "currentRetention": "当前保持率", "fadeRate": "衰减率", "confidence": {"high": "高置信度", "medium": "中等置信度", "low": "低置信度"}, "disclaimer": "* 预测结果基于线性衰减模型，仅供参考"}}}, "batterySorting": {"title": "电池分选", "description": "电池测试数据的智能分选和质量分级", "currentRule": "当前规则", "runSorting": "执行分选", "variables": {"title": "变量定义区", "addVariable": "添加变量", "variableName": "变量名称", "dataType": "数据类型", "stepNumber": "工步号", "cycleNumber": "循环次数", "actions": "操作", "noVariables": "暂无变量定义", "addNewVariable": "添加新变量", "editVariable": "编辑变量", "variableNamePlaceholder": "请输入变量名称", "stepNumberPlaceholder": "可选，指定工步号", "cycleNumberPlaceholder": "可选，指定循环次数", "description": "描述", "descriptionPlaceholder": "可选，变量描述", "cancel": "取消", "add": "添加", "save": "保存"}, "grades": {"title": "等级设定区", "addGrade": "添加等级", "priority": "优先级", "gradeName": "等级名称", "color": "颜色", "conditions": "条件表达式", "actions": "操作", "noGrades": "暂无等级定义", "noConditions": "未设置条件", "addNewGrade": "添加新等级", "editGrade": "编辑等级", "gradeNamePlaceholder": "请输入等级名称", "conditionsPlaceholder": "请输入条件表达式，如：结束容量 > 3000", "conditionsHelp": "使用变量名、比较运算符(>, <, >=, <=, ==, !=)和逻辑运算符(AND, OR, NOT)组合条件", "descriptionPlaceholder": "可选，等级描述", "cancel": "取消", "add": "添加", "save": "保存"}, "deviceList": {"title": "设备列表", "searchPlaceholder": "搜索设备或位置", "noSearchResults": "未找到匹配的设备", "noDevices": "暂无设备", "channels": "通道", "active": "运行中", "error": "错误", "lastUpdate": "最后更新", "channel": "通道", "currentStep": "当前工步", "currentCycle": "当前循环", "channelStatus": {"idle": "空闲", "testing": "测试中", "completed": "已完成", "error": "错误", "unknown": "未知"}}, "filter": {"title": "筛选器", "active": "已激活", "reset": "重置", "apply": "应用", "dateRange": "日期范围", "selectDateRange": "选择日期范围", "startDate": "开始日期", "endDate": "结束日期", "clear": "清除", "confirm": "确认", "devices": "设备", "stepNumbers": "工步号", "step": "工步", "cycleNumbers": "循环号", "cycle": "循环", "more": "更多", "grades": "等级", "channelStatus": "通道状态", "status": {"idle": "空闲", "testing": "测试中", "completed": "已完成", "error": "错误"}}, "results": {"title": "分选结果", "refresh": "刷新", "export": "导出", "loading": "加载中", "noResults": "暂无分选结果", "noResultsHint": "请先设置分选规则并执行分选", "device": "设备", "channel": "通道", "channelNumber": "通道", "grade": "等级", "errorMessage": "错误信息", "sortingTime": "分选时间", "error": "错误", "pending": "待分选", "stats": {"total": "总数", "graded": "已分级", "ungraded": "未分级", "errors": "错误"}, "gradeDistribution": "等级分布"}, "conditionEditor": {"title": "条件编辑区", "selectVariable": "选择变量", "selectVariablePlaceholder": "请选择变量", "selectOperator": "选择运算符", "comparisonValue": "比较值", "comparisonValuePlaceholder": "请输入数值", "actions": "操作", "addCondition": "添加条件", "logicalOperators": "逻辑运算符", "addParentheses": "添加括号", "conditionExpression": "条件表达式", "invalid": "无效", "valid": "有效", "conditionPlaceholder": "请输入条件表达式或使用上方工具构建", "usageInstructions": "使用说明", "instruction1": "使用上方工具快速构建条件", "instruction2": "支持比较运算符：>, <, >=, <=, ==, !=", "instruction3": "支持逻辑运算符：AND, OR, NOT", "instruction4": "使用括号控制运算优先级", "exampleConditions": "示例条件", "clearCondition": "清空条件"}, "ruleEditor": {"basicInfo": {"title": "规则基本信息", "ruleName": "规则名称", "ruleNamePlaceholder": "请输入规则名称", "isActive": "启用规则", "description": "规则描述", "descriptionPlaceholder": "请输入规则描述", "status": "状态", "valid": "有效", "invalid": "无效", "variables": "变量", "grades": "等级"}, "validation": {"errors": "验证错误", "nameRequired": "规则名称不能为空", "variablesRequired": "至少需要定义一个变量", "gradesRequired": "至少需要定义一个等级", "gradeConditionsRequired": "所有等级都必须设置条件", "undefinedVariable": "等级 {grade} 中使用了未定义的变量: {variable}"}, "actions": {"test": "测试规则", "save": "保存规则"}}, "dataTypes": {"voltage": "电压", "current": "电流", "power": "功率", "contactResistance": "接触电阻", "capacity": "容量", "specificCapacity": "比容量", "chargeCapacity": "充电容量", "chargeSpecificCapacity": "充电比容量", "dischargeCapacity": "放电容量", "dischargeSpecificCapacity": "放电比容量", "energy": "能量", "specificEnergy": "比能量", "chargeEnergy": "充电能量", "chargeSpecificEnergy": "充电比能量", "dischargeEnergy": "放电能量", "dischargeSpecificEnergy": "放电比能量", "dQdV": "dQ/dV", "dQmdV": "dQm/dV", "v1": "V1电压", "temperature": "温度", "auxVoltageDiff": "辅助电压差", "auxTempDiff": "辅助温度差"}}, "settings": {"title": "系统设置", "description": "配置系统显示、单位、通知和监控设置", "tabs": {"display": "显示", "units": "单位", "notifications": "通知", "monitoring": "监控"}, "actions": {"save": "保存设置", "reset": "重置", "restoreDefaults": "恢复默认"}, "display": {"theme": {"title": "主题设置", "description": "选择应用程序的外观主题", "label": "主题", "light": "浅色模式", "dark": "深色模式", "system": "跟随系统"}, "language": {"title": "语言设置", "description": "选择界面显示语言", "label": "语言", "zh": "中文", "en": "English"}, "interface": {"title": "界面设置", "description": "自定义界面显示选项"}, "fontSize": {"label": "字体大小", "small": "小", "medium": "中", "large": "大"}, "compactMode": {"label": "紧凑模式", "description": "减少界面元素间距，显示更多内容"}, "animations": {"label": "动画效果", "description": "启用界面过渡动画和视觉效果"}}, "units": {"precision": {"title": "数值精度", "description": "设置数值显示的小数位数", "decimalPlaces": "小数位数", "example": "示例"}, "electrical": {"title": "电学参数", "description": "设置电压、电流、功率等参数的显示单位", "voltage": "电压", "current": "电流", "power": "功率", "resistance": "电阻"}, "energy": {"title": "能量参数", "description": "设置容量、能量等参数的显示单位", "capacity": "容量", "energy": "能量"}, "environmental": {"title": "环境参数", "description": "设置温度、时间等参数的显示单位", "temperature": "温度", "time": "时间"}, "preview": {"title": "单位预览", "description": "查看当前单位设置的显示效果"}}, "notifications": {"title": "通知设置", "description": "配置系统通知的接收方式", "enabled": {"title": "启用通知", "description": "接收系统事件和警报通知"}, "sound": {"title": "声音提醒", "description": "通知时播放提示音"}, "desktop": {"title": "桌面通知", "description": "在桌面显示通知弹窗"}, "email": {"title": "邮件通知", "description": "发送重要通知到邮箱"}, "types": {"title": "通知类型", "description": "系统会在以下情况发送通知", "system": {"title": "系统事件", "startup": "系统启动", "shutdown": "系统关闭", "error": "系统错误"}, "device": {"title": "设备事件", "online": "设备上线", "offline": "设备离线", "alarm": "设备报警"}, "test": {"title": "测试事件", "completed": "测试完成", "failed": "测试失败", "threshold": "阈值超限"}, "sorting": {"title": "分选事件", "completed": "分选完成", "error": "分选错误", "grade": "等级变更"}}}, "monitoring": {"refresh": {"title": "刷新设置", "description": "配置数据刷新频率", "interval": "刷新间隔", "options": {"1s": "1秒", "2s": "2秒", "5s": "5秒", "10s": "10秒", "30s": "30秒", "1m": "1分钟"}}, "retention": {"title": "数据保留", "description": "设置历史数据保留时间", "period": "保留期限", "options": {"7d": "7天", "14d": "14天", "30d": "30天", "60d": "60天", "90d": "90天", "1y": "1年"}}, "thresholds": {"title": "报警阈值", "description": "设置参数超限报警的阈值范围", "voltage": "电压阈值 (V)", "current": "电流阈值 (A)", "temperature": "温度阈值 (°C)", "min": "最小值", "max": "最大值"}}}, "process": {"title": "工步流程管理", "description": "配置和管理电池测试工步流程", "tabs": {"editor": "工步编辑", "table": "工步视图", "send": "工步发送", "overview": "流程概览"}, "stepEditor": {"title": "工步编辑器", "description": "创建和编辑电池测试工步流程", "createStep": "新建工步", "editStep": "编辑工步", "noSteps": "暂无工步", "noStepsDescription": "开始创建您的第一个工步流程", "createFirstStep": "创建工步"}, "stepForm": {"basicInfo": "基本信息", "basicInfoDescription": "设置工步的基本属性", "electricalParams": "电气参数", "electricalParamsDescription": "设置电流和电压相关参数", "timeAndTermination": "时间和终止条件", "timeAndTerminationDescription": "设置工步的时间和终止条件", "notes": "备注信息", "advancedSettings": "高级设置", "fields": {"name": "工步名称", "status": "工步状态", "mode": "工步模式", "current": "电流 (mA)", "upperVoltage": "上限电压 (mV)", "lowerVoltage": "下限电压 (mV)", "duration": "持续时间", "capacity": "容量 (mAh)", "power": "功率 (mW)", "upperTemperature": "温度上限 (℃)", "lowerTemperature": "温度下限 (℃)", "samplingInterval": "采样间隔 (s)", "notes": "备注"}, "placeholders": {"name": "请输入工步名称", "status": "选择工步状态", "mode": "选择工步模式", "duration": "00:01:00.000", "notes": "请输入备注信息..."}, "ranges": {"current": "范围: 0.1 - 10000 mA", "voltage": "范围: 0 - 5000 mV", "capacity": "范围: 0 - 50000 mAh", "power": "范围: 0 - 10000 mW", "temperature": "范围: -40 - 100 ℃", "samplingInterval": "范围: 1 - 3600 s"}, "timeFormat": "格式: hh:mm:ss.ms", "maxCharacters": "最多500字符", "required": "必填项"}, "stepStatus": {"discharge": "放电", "charge": "充电", "rest": "搁置", "suspend": "挂起", "cycle": "循环"}, "stepMode": {"cc": "恒流", "cccv": "恒流恒压", "cp": "恒功率", "cr": "恒阻", "dcir": "直流内阻", "pulse": "脉冲", "cv": "恒压", "rate_cc": "倍率恒流", "rate_cv": "倍率恒压"}, "statistics": {"totalSteps": "总工步数", "chargeSteps": "充电工步", "dischargeSteps": "放电工步", "restSteps": "搁置工步"}}}
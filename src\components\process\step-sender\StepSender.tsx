'use client';

import React, { useState, useMemo } from 'react';
import { useTranslations } from 'next-intl';
import { Send, AlertTriangle, CheckCircle, Clock, Zap, Settings } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Progress } from '@/components/ui/progress';
import { Tabs, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';

import { ChannelSelector } from '../channel-selector/ChannelSelector';
import { Step } from '@/lib/process/types/step';
import { Channel, ChannelSelectionResult } from '@/lib/process/types/channel';
import { 
  StepSendConfig, 
  StepSendRequest, 
  StepSendResult,
  DEFAULT_SEND_CONFIG,
  validateSteps,
  checkChannelCompatibility,
  estimateStepDuration,
  sendStepsToChannels
} from '@/lib/process/services/stepSendService';

interface StepSenderProps {
  steps: Step[];
  channels: Channel[];
  disabled?: boolean;
  className?: string;
}

export function StepSender({
  steps,
  channels,
  disabled = false,
  className
}: StepSenderProps) {
  
  const t = useTranslations('process.stepSender');
  
  const [isOpen, setIsOpen] = useState(false);
  const [selectedChannels, setSelectedChannels] = useState<Channel[]>([]);
  const [sendConfig, setSendConfig] = useState<StepSendConfig>(DEFAULT_SEND_CONFIG);
  const [description, setDescription] = useState('');
  const [isSending, setIsSending] = useState(false);
  const [sendResult, setSendResult] = useState<StepSendResult | null>(null);
  const [currentTab, setCurrentTab] = useState('channels');

  // 验证工步
  const stepValidation = useMemo(() => {
    return validateSteps(steps);
  }, [steps]);

  // 检查通道兼容性
  const channelCompatibility = useMemo(() => {
    if (selectedChannels.length === 0) return [];
    return checkChannelCompatibility(steps, selectedChannels);
  }, [steps, selectedChannels]);

  // 估算总执行时间
  const estimatedDuration = useMemo(() => {
    return estimateStepDuration(steps);
  }, [steps]);

  // 兼容的通道
  const compatibleChannels = useMemo(() => {
    return channelCompatibility.filter(c => c.compatible);
  }, [channelCompatibility]);

  // 不兼容的通道
  const incompatibleChannels = useMemo(() => {
    return channelCompatibility.filter(c => !c.compatible);
  }, [channelCompatibility]);

  const handleChannelSelection = (result: ChannelSelectionResult) => {
    setSelectedChannels(result.selectedChannels);
  };

  const handleSend = async () => {
    if (selectedChannels.length === 0 || !stepValidation.isValid) {
      return;
    }

    setIsSending(true);
    setSendResult(null);

    try {
      const request: StepSendRequest = {
        steps,
        channelIds: selectedChannels.map(c => c.id || `${c.deviceId}_${c.channelNumber}`),
        config: sendConfig,
        metadata: {
          description: description || undefined,
          priority: 'normal'
        }
      };

      const result = await sendStepsToChannels(request);
      setSendResult(result);

      // 如果发送成功，切换到结果标签页
      if (result.success) {
        setCurrentTab('result');
      }
    } catch (error) {
      setSendResult({
        success: false,
        requestId: '',
        totalChannels: selectedChannels.length,
        successChannels: 0,
        failedChannels: selectedChannels.length,
        results: [],
        message: error instanceof Error ? error.message : '发送失败'
      });
    } finally {
      setIsSending(false);
    }
  };

  const handleClose = () => {
    setIsOpen(false);
    setSelectedChannels([]);
    setSendResult(null);
    setCurrentTab('channels');
    setDescription('');
  };

  const formatDuration = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);
    return `${hours}h ${minutes}m ${secs}s`;
  };

  const canSend = stepValidation.isValid && 
                 selectedChannels.length > 0 && 
                 compatibleChannels.length > 0 && 
                 !isSending;

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button 
          disabled={disabled || steps.length === 0}
          className={className}
        >
          <Send className="h-4 w-4 mr-2" />
          发送工步
        </Button>
      </DialogTrigger>
      
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden flex flex-col">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <Send className="h-5 w-5" />
            <span>发送工步到通道</span>
          </DialogTitle>
          <DialogDescription>
            选择目标通道并配置发送参数
          </DialogDescription>
        </DialogHeader>

        <Tabs value={currentTab} onValueChange={setCurrentTab} className="flex-1 flex flex-col">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="channels">选择通道</TabsTrigger>
            <TabsTrigger value="validation">验证检查</TabsTrigger>
            <TabsTrigger value="config">发送配置</TabsTrigger>
            <TabsTrigger value="result" disabled={!sendResult}>发送结果</TabsTrigger>
          </TabsList>

          {/* 通道选择 */}
          <TabsContent value="channels" className="flex-1 space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">工步概览</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600">{steps.length}</div>
                    <div className="text-muted-foreground">工步数量</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600">
                      {formatDuration(estimatedDuration)}
                    </div>
                    <div className="text-muted-foreground">预计时长</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-purple-600">{channels.length}</div>
                    <div className="text-muted-foreground">可用通道</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-orange-600">{selectedChannels.length}</div>
                    <div className="text-muted-foreground">已选通道</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <ChannelSelector
              selectedChannelIds={selectedChannels.map(c => `${c.deviceId}_${c.channelNumber}`)}
              onSelectionChange={handleChannelSelection}
              config={{
                multiSelect: true,
                showBatteryInfo: false,
                showRealTimeData: false,
                groupByDevice: true,
                compactMode: true
              }}
              trigger={
                <Button variant="outline" className="w-full">
                  <Zap className="h-4 w-4 mr-2" />
                  选择目标通道
                  {selectedChannels.length > 0 && (
                    <Badge variant="secondary" className="ml-2">
                      {selectedChannels.length}
                    </Badge>
                  )}
                </Button>
              }
              title="选择目标通道"
              description="选择要发送工步的设备通道"
            />
          </TabsContent>

          {/* 验证检查 */}
          <TabsContent value="validation" className="flex-1 space-y-4">
            {/* 工步验证 */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  {stepValidation.isValid ? (
                    <CheckCircle className="h-5 w-5 text-green-500" />
                  ) : (
                    <AlertTriangle className="h-5 w-5 text-red-500" />
                  )}
                  <span>工步验证</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {stepValidation.isValid ? (
                  <Alert>
                    <CheckCircle className="h-4 w-4" />
                    <AlertDescription>
                      所有工步验证通过，可以安全发送。
                    </AlertDescription>
                  </Alert>
                ) : (
                  <Alert variant="destructive">
                    <AlertTriangle className="h-4 w-4" />
                    <AlertDescription>
                      <div className="font-medium mb-2">发现以下错误：</div>
                      <ul className="list-disc list-inside space-y-1">
                        {stepValidation.errors.map((error, index) => (
                          <li key={index} className="text-sm">
                            工步{error.stepNumber}: {error.message}
                          </li>
                        ))}
                      </ul>
                    </AlertDescription>
                  </Alert>
                )}

                {stepValidation.warnings.length > 0 && (
                  <Alert>
                    <AlertTriangle className="h-4 w-4" />
                    <AlertDescription>
                      <div className="font-medium mb-2">注意事项：</div>
                      <ul className="list-disc list-inside space-y-1">
                        {stepValidation.warnings.map((warning, index) => (
                          <li key={index} className="text-sm">
                            工步{warning.stepNumber}: {warning.message}
                          </li>
                        ))}
                      </ul>
                    </AlertDescription>
                  </Alert>
                )}
              </CardContent>
            </Card>

            {/* 通道兼容性 */}
            {selectedChannels.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    {incompatibleChannels.length === 0 ? (
                      <CheckCircle className="h-5 w-5 text-green-500" />
                    ) : (
                      <AlertTriangle className="h-5 w-5 text-yellow-500" />
                    )}
                    <span>通道兼容性</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div className="text-center p-3 bg-green-50 rounded-md">
                      <div className="text-2xl font-bold text-green-600">
                        {compatibleChannels.length}
                      </div>
                      <div className="text-green-600">兼容通道</div>
                    </div>
                    <div className="text-center p-3 bg-red-50 rounded-md">
                      <div className="text-2xl font-bold text-red-600">
                        {incompatibleChannels.length}
                      </div>
                      <div className="text-red-600">不兼容通道</div>
                    </div>
                  </div>

                  {incompatibleChannels.length > 0 && (
                    <div className="space-y-2">
                      <h4 className="font-medium">兼容性问题：</h4>
                      {incompatibleChannels.map((channel) => (
                        <div key={channel.channelId} className="border rounded-md p-3">
                          <div className="font-medium mb-2">通道 {channel.channelId}</div>
                          <ul className="list-disc list-inside space-y-1 text-sm">
                            {channel.issues.map((issue, index) => (
                              <li key={index} className={
                                issue.severity === 'error' ? 'text-red-600' : 'text-yellow-600'
                              }>
                                {issue.message}
                              </li>
                            ))}
                          </ul>
                        </div>
                      ))}
                    </div>
                  )}
                </CardContent>
              </Card>
            )}
          </TabsContent>

          {/* 发送配置 */}
          <TabsContent value="config" className="flex-1 space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Settings className="h-5 w-5" />
                  <span>发送配置</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="autoStart">发送后自动启动</Label>
                    <Switch
                      id="autoStart"
                      checked={sendConfig.autoStart}
                      onCheckedChange={(checked) => 
                        setSendConfig(prev => ({ ...prev, autoStart: checked }))
                      }
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <Label htmlFor="overwriteExisting">覆盖现有工步</Label>
                    <Switch
                      id="overwriteExisting"
                      checked={sendConfig.overwriteExisting}
                      onCheckedChange={(checked) => 
                        setSendConfig(prev => ({ ...prev, overwriteExisting: checked }))
                      }
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="sendTimeout">发送超时 (秒)</Label>
                    <Input
                      id="sendTimeout"
                      type="number"
                      min="5"
                      max="300"
                      value={sendConfig.sendTimeout}
                      onChange={(e) => 
                        setSendConfig(prev => ({ 
                          ...prev, 
                          sendTimeout: parseInt(e.target.value) || 30 
                        }))
                      }
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="retryCount">重试次数</Label>
                    <Input
                      id="retryCount"
                      type="number"
                      min="0"
                      max="10"
                      value={sendConfig.retryCount}
                      onChange={(e) => 
                        setSendConfig(prev => ({ 
                          ...prev, 
                          retryCount: parseInt(e.target.value) || 3 
                        }))
                      }
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description">描述信息</Label>
                  <Textarea
                    id="description"
                    placeholder="请输入发送描述信息（可选）"
                    value={description}
                    onChange={(e) => setDescription(e.target.value)}
                    className="min-h-[80px]"
                  />
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* 发送结果 */}
          <TabsContent value="result" className="flex-1 space-y-4">
            {sendResult && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    {sendResult.success ? (
                      <CheckCircle className="h-5 w-5 text-green-500" />
                    ) : (
                      <AlertTriangle className="h-5 w-5 text-red-500" />
                    )}
                    <span>发送结果</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-3 gap-4 text-center">
                    <div className="p-3 bg-blue-50 rounded-md">
                      <div className="text-2xl font-bold text-blue-600">
                        {sendResult.totalChannels}
                      </div>
                      <div className="text-blue-600">总通道数</div>
                    </div>
                    <div className="p-3 bg-green-50 rounded-md">
                      <div className="text-2xl font-bold text-green-600">
                        {sendResult.successChannels}
                      </div>
                      <div className="text-green-600">成功</div>
                    </div>
                    <div className="p-3 bg-red-50 rounded-md">
                      <div className="text-2xl font-bold text-red-600">
                        {sendResult.failedChannels}
                      </div>
                      <div className="text-red-600">失败</div>
                    </div>
                  </div>

                  {sendResult.message && (
                    <Alert variant={sendResult.success ? "default" : "destructive"}>
                      <AlertDescription>{sendResult.message}</AlertDescription>
                    </Alert>
                  )}

                  {/* 详细结果 */}
                  <div className="space-y-2">
                    <h4 className="font-medium">详细结果：</h4>
                    <div className="max-h-40 overflow-y-auto space-y-2">
                      {sendResult.results.map((result, index) => (
                        <div 
                          key={index} 
                          className={`p-3 rounded-md border ${
                            result.success ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'
                          }`}
                        >
                          <div className="flex items-center justify-between">
                            <span className="font-medium">通道 {result.channelId}</span>
                            <Badge variant={result.success ? "default" : "destructive"}>
                              {result.success ? '成功' : '失败'}
                            </Badge>
                          </div>
                          {result.success ? (
                            <div className="text-sm text-muted-foreground">
                              发送 {result.stepCount} 个工步
                              {result.estimatedDuration && (
                                <span className="ml-2">
                                  预计时长: {formatDuration(result.estimatedDuration)}
                                </span>
                              )}
                            </div>
                          ) : (
                            <div className="text-sm text-red-600">
                              {result.error}
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}
          </TabsContent>
        </Tabs>

        {/* 操作按钮 */}
        <div className="flex items-center justify-between pt-4 border-t">
          <div className="text-sm text-muted-foreground">
            {selectedChannels.length > 0 && (
              <span>将发送到 {selectedChannels.length} 个通道</span>
            )}
          </div>

          <div className="flex items-center space-x-2">
            <Button variant="outline" onClick={handleClose}>
              {sendResult ? '关闭' : '取消'}
            </Button>
            
            {!sendResult && (
              <Button 
                onClick={handleSend}
                disabled={!canSend}
              >
                {isSending ? (
                  <>
                    <Clock className="h-4 w-4 mr-2 animate-spin" />
                    发送中...
                  </>
                ) : (
                  <>
                    <Send className="h-4 w-4 mr-2" />
                    开始发送
                  </>
                )}
              </Button>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}

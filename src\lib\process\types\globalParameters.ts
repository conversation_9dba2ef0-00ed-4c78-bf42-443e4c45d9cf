/**
 * 全局参数类型定义
 * 基于 step-specification.md 第5章节定义
 */

// 保护参数组 (protectionParams)
export interface ProtectionParams {
  // 报警设置
  alarmSettings: {
    alarmCapacity: {
      value: number;
      unit: 'mAh';
      range: [number, number];
      default: 5100;
    };
    ccVoltageFluctuation: {
      value: number;
      unit: 'mV';
      range: [number, number];
      default: 100;
    };
  };
  // 异常检测
  abnormalDetection: {
    openCircuitDetection: {
      value: boolean;
      default: true;
    };
  };
  // 温度监控
  temperatureMonitoring: {
    alarmTemperatureLimit: {
      value: number;
      unit: '℃';
      range: [number, number];
      default: 60;
    };
  };
}

// 显示设置组 (displayParams)
export interface DisplayParams {
  // 结束条件
  endConditions: {
    timeCondition: {
      value: boolean;
      default: true;
    };
  };
  // 记录条件
  recordConditions: {
    voltageDiffRecord: {
      value: boolean;
      default: true;
    };
  };
}

// 温度容量补偿组 (temperatureCompensation)
export interface TemperatureCompensation {
  compensationFormula: {
    rangeMinValue: {
      value: number;
      default: -1;
    };
    dynamicFormula: {
      value: string;
      default: '(c*(26.8-t)/26.8+100)*0.9';
    };
  };
}

// 工步温度补偿组 (stepTemperatureCompensation)
export interface StepTemperatureCompensation {
  dataSourceSelection: {
    temperatureSource: {
      value: 'start' | 'end' | 'max' | 'min';
      options: ['start', 'end', 'max', 'min'];
      labels: ['开始温度', '结束温度', '最高温度', '最低温度'];
      default: 'end';
    };
  };
  stepCompensation: {
    compensationFormula: {
      value: string;
      default: '(c/1000-0.6735*(t-29.5))*1000';
    };
  };
}

// 流程匹配规则组 (processMatchingRules)
export interface ProcessMatchingRules {
  barcodeMatching: {
    matchingPattern: {
      value: string;
      default: '*';
    };
    conditionPriority: {
      value: number;
      default: 1;
    };
  };
}

// 分选条件组 (sortingConditions)
export interface SortingConditions {
  gradeSettings: {
    sortingColor: {
      value: string;
      default: '#4CAF50';
    };
  };
  conditionEditing: {
    logicExpression: {
      value: string;
      default: 'EndC>=100';
    };
  };
}

// 全局参数主接口
export interface GlobalParameters {
  protectionParams: ProtectionParams;
  displayParams: DisplayParams;
  temperatureCompensation: TemperatureCompensation;
  stepTemperatureCompensation: StepTemperatureCompensation;
  processMatchingRules: ProcessMatchingRules;
  sortingConditions: SortingConditions;
}

// 全局参数默认值
export const DEFAULT_GLOBAL_PARAMETERS: GlobalParameters = {
  protectionParams: {
    alarmSettings: {
      alarmCapacity: {
        value: 5100,
        unit: 'mAh',
        range: [0, 50000],
        default: 5100
      },
      ccVoltageFluctuation: {
        value: 100,
        unit: 'mV',
        range: [0, 1000],
        default: 100
      }
    },
    abnormalDetection: {
      openCircuitDetection: {
        value: true,
        default: true
      }
    },
    temperatureMonitoring: {
      alarmTemperatureLimit: {
        value: 60,
        unit: '℃',
        range: [-40, 100],
        default: 60
      }
    }
  },
  displayParams: {
    endConditions: {
      timeCondition: {
        value: true,
        default: true
      }
    },
    recordConditions: {
      voltageDiffRecord: {
        value: true,
        default: true
      }
    }
  },
  temperatureCompensation: {
    compensationFormula: {
      rangeMinValue: {
        value: -1,
        default: -1
      },
      dynamicFormula: {
        value: '(c*(26.8-t)/26.8+100)*0.9',
        default: '(c*(26.8-t)/26.8+100)*0.9'
      }
    }
  },
  stepTemperatureCompensation: {
    dataSourceSelection: {
      temperatureSource: {
        value: 'end',
        options: ['start', 'end', 'max', 'min'],
        labels: ['开始温度', '结束温度', '最高温度', '最低温度'],
        default: 'end'
      }
    },
    stepCompensation: {
      compensationFormula: {
        value: '(c/1000-0.6735*(t-29.5))*1000',
        default: '(c/1000-0.6735*(t-29.5))*1000'
      }
    }
  },
  processMatchingRules: {
    barcodeMatching: {
      matchingPattern: {
        value: '*',
        default: '*'
      },
      conditionPriority: {
        value: 1,
        default: 1
      }
    }
  },
  sortingConditions: {
    gradeSettings: {
      sortingColor: {
        value: '#4CAF50',
        default: '#4CAF50'
      }
    },
    conditionEditing: {
      logicExpression: {
        value: 'EndC>=100',
        default: 'EndC>=100'
      }
    }
  }
};

// 全局参数组名称映射
export const GLOBAL_PARAM_GROUP_LABELS = {
  protectionParams: '保护参数组',
  displayParams: '显示设置组',
  temperatureCompensation: '温度容量补偿组',
  stepTemperatureCompensation: '工步温度补偿组',
  processMatchingRules: '流程匹配规则组',
  sortingConditions: '分选条件组'
} as const;

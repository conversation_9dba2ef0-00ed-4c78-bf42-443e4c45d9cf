import { create } from 'zustand';

// 导航方向枚举
export enum NavigationDirection {
  FORWARD = 'forward',
  BACKWARD = 'backward',
  NONE = 'none'
}

// 页面路由层级定义
const PAGE_HIERARCHY: Record<string, number> = {
  '/': 0,
  '/monitoring': 1,
  '/channels': 2,
  '/events': 1,
  '/settings': 1,
  '/reports': 1,
  '/help': 1
};

// 导航状态管理接口
interface NavigationStore {
  currentPath: string;
  previousPath: string;
  direction: NavigationDirection;
  isAnimating: boolean;

  // 隐式参数传递
  navigationParams: Record<string, any>;

  // Actions
  setCurrentPath: (path: string) => void;
  setAnimating: (isAnimating: boolean) => void;
  getDirection: (fromPath: string, toPath: string) => NavigationDirection;
  updateNavigation: (newPath: string) => void;
  setNavigationParams: (params: Record<string, any>) => void;
  getNavigationParam: (key: string) => any;
  clearNavigationParams: () => void;
}

// 获取路径的层级
const getPathLevel = (path: string): number => {
  // 移除locale前缀 (如 /zh, /en)
  const cleanPath = path.replace(/^\/[a-z]{2}(?=\/|$)/, '') || '/';
  return PAGE_HIERARCHY[cleanPath] ?? 1;
};

// 创建导航状态管理
export const useNavigationStore = create<NavigationStore>((set, get) => ({
  currentPath: '',
  previousPath: '',
  direction: NavigationDirection.NONE,
  isAnimating: false,
  navigationParams: {},

  setCurrentPath: (path: string) => {
    set({ currentPath: path });
  },

  setAnimating: (isAnimating: boolean) => {
    set({ isAnimating });
  },

  getDirection: (fromPath: string, toPath: string): NavigationDirection => {
    if (!fromPath || !toPath || fromPath === toPath) {
      return NavigationDirection.NONE;
    }

    const fromLevel = getPathLevel(fromPath);
    const toLevel = getPathLevel(toPath);

    // 特殊处理：从设备监控到通道监控始终是前进
    if (fromPath.includes('/monitoring') && toPath.includes('/channels')) {
      return NavigationDirection.FORWARD;
    }

    // 特殊处理：从通道监控到设备监控始终是后退
    if (fromPath.includes('/channels') && toPath.includes('/monitoring')) {
      return NavigationDirection.BACKWARD;
    }

    // 根据层级判断方向
    if (toLevel > fromLevel) {
      return NavigationDirection.FORWARD;
    } else if (toLevel < fromLevel) {
      return NavigationDirection.BACKWARD;
    } else {
      return NavigationDirection.NONE;
    }
  },

  updateNavigation: (newPath: string) => {
    const { currentPath } = get();
    const direction = get().getDirection(currentPath, newPath);

    set({
      previousPath: currentPath,
      currentPath: newPath,
      direction,
      isAnimating: true
    });

    // 动画完成后重置状态
    setTimeout(() => {
      set({ isAnimating: false });
    }, 300);
  },

  setNavigationParams: (params: Record<string, any>) => {
    set({ navigationParams: { ...get().navigationParams, ...params } });
  },

  getNavigationParam: (key: string) => {
    return get().navigationParams[key];
  },

  clearNavigationParams: () => {
    set({ navigationParams: {} });
  }
}));

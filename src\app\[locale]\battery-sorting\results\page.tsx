'use client';

import React, { useState, useEffect } from 'react';
import { useTranslations } from 'next-intl';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { RefreshCw, Download, Settings, BarChart3 } from 'lucide-react';

import { TestSessionList } from '@/components/battery-sorting/TestSessionList';
import { BatteryResultsTable } from '@/components/battery-sorting/BatteryResultsTable';
import { TestSession, BatteryResult } from '@/lib/types/battery-sorting';
import { getTestSessions, getBatteryResultsBySession } from '@/lib/services/batterySortingService';

export default function BatterySortingResultsPage() {
  const t = useTranslations('batterySorting');

  const [testSessions, setTestSessions] = useState<TestSession[]>([]);
  const [selectedSession, setSelectedSession] = useState<TestSession | null>(null);
  const [batteryResults, setBatteryResults] = useState<BatteryResult[]>([]);
  const [isLoadingSessions, setIsLoadingSessions] = useState(true);
  const [isLoadingResults, setIsLoadingResults] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);

  // 加载测试会话
  useEffect(() => {
    loadTestSessions();
  }, []);

  const loadTestSessions = async () => {
    try {
      setIsLoadingSessions(true);
      const sessions = await getTestSessions();
      setTestSessions(sessions);

      // 自动选择第一个会话
      if (sessions.length > 0) {
        handleSessionSelect(sessions[0]);
      }
    } catch (error) {
      console.error('加载测试会话失败:', error);
    } finally {
      setIsLoadingSessions(false);
    }
  };

  // 选择测试会话
  const handleSessionSelect = async (session: TestSession) => {
    if (selectedSession?.id === session.id) return;

    try {
      setSelectedSession(session);
      setIsLoadingResults(true);
      const results = await getBatteryResultsBySession(session.id);
      setBatteryResults(results);
    } catch (error) {
      console.error('加载电池结果失败:', error);
      setBatteryResults([]);
    } finally {
      setIsLoadingResults(false);
    }
  };

  const handleRefresh = async () => {
    try {
      setIsRefreshing(true);
      await loadTestSessions();
    } catch (error) {
      console.error('刷新数据失败:', error);
    } finally {
      setIsRefreshing(false);
    }
  };

  const handleViewDetail = (result: BatteryResult) => {
    console.log('查看详情:', result);
    // TODO: 实现详情查看功能
  };

  const handleExport = (format?: string) => {
    console.log('导出当前会话数据:', selectedSession?.id, '格式:', format);
    // TODO: 实现数据导出功能
  };

  if (isLoadingSessions) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="text-center">
          <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-2" />
          <p className="text-muted-foreground">加载测试数据中...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">分选结果查看</h1>
          <p className="text-muted-foreground">
            电池分选测试结果和质量分析
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button 
            variant="outline" 
            onClick={handleRefresh}
            disabled={isRefreshing}
          >
            <RefreshCw className={`mr-2 h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} />
            刷新
          </Button>
          <Button variant="outline">
            <Settings className="mr-2 h-4 w-4" />
            分选设置
          </Button>
          <Button onClick={() => handleExport('excel')}>
            <Download className="mr-2 h-4 w-4" />
            导出报表
          </Button>
        </div>
      </div>

      {/* 主要内容区域 - 左右布局 */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* 左侧：测试会话列表 */}
        <div className="lg:col-span-1">
          <TestSessionList
            sessions={testSessions}
            selectedSessionId={selectedSession?.id}
            onSessionSelect={handleSessionSelect}
            isLoading={isLoadingSessions}
          />
        </div>

        {/* 右侧：电池结果表格 */}
        <div className="lg:col-span-2">
          <BatteryResultsTable
            results={batteryResults}
            sessionName={selectedSession?.processName}
            isLoading={isLoadingResults}
            onViewDetail={handleViewDetail}
            onExport={handleExport}
          />
        </div>
      </div>
    </div>
  );
}

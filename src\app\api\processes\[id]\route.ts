import { NextRequest, NextResponse } from 'next/server';
import { ProcessService } from '@/lib/process/services/processService';
import { UpdateProcessData } from '@/lib/process/types/process';

// GET /api/processes/[id] - 获取单个工艺流程
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const resolvedParams = await params;
  try {
    const processService = new ProcessService();
    const process = await processService.getProcess(resolvedParams.id);

    if (!process) {
      return NextResponse.json(
        {
          success: false,
          error: '工艺流程不存在'
        },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: process
    });
  } catch (error) {
    console.error('获取工艺流程失败:', error);
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : '获取工艺流程失败'
      },
      { status: 500 }
    );
  }
}

// PUT /api/processes/[id] - 更新工艺流程
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const resolvedParams = await params;
  try {
    const body = await request.json();
    const updateData: UpdateProcessData = body;

    const processService = new ProcessService();
    const process = await processService.updateProcess(resolvedParams.id, updateData);

    return NextResponse.json({
      success: true,
      data: process
    });
  } catch (error) {
    console.error('更新工艺流程失败:', error);
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : '更新工艺流程失败'
      },
      { status: 500 }
    );
  }
}

// DELETE /api/processes/[id] - 删除工艺流程
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const resolvedParams = await params;
  try {
    const processService = new ProcessService();
    await processService.deleteProcess(resolvedParams.id);

    return NextResponse.json({
      success: true,
      message: '工艺流程删除成功'
    });
  } catch (error) {
    console.error('删除工艺流程失败:', error);
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : '删除工艺流程失败'
      },
      { status: 500 }
    );
  }
}

'use client';

import React from 'react';
import { useTranslations } from 'next-intl';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useStepStore } from '@/lib/process/stores/stepStore';

interface StepTableProps {
  className?: string;
}

export function StepTable({ className }: StepTableProps) {
  const t = useTranslations('process.stepTable');
  
  const {
    steps,
    isLoading,
    error
  } = useStepStore();

  if (error) {
    return (
      <Card className={className}>
        <CardContent className="flex items-center justify-center py-12">
          <div className="text-center">
            <div className="text-red-500 mb-2">加载失败</div>
            <div className="text-sm text-muted-foreground">{error}</div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={`h-full flex flex-col overflow-hidden ${className}`}>
      {/* 表格头部 */}
      <div className="flex-shrink-0">
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle>工步列表</CardTitle>
                <CardDescription>
                  管理和查看所有工步配置
                </CardDescription>
              </div>
            </div>
          </CardHeader>
        </Card>
      </div>

      {/* 表格主体 */}
      <div className="flex-1 overflow-hidden">
        <Card className="h-full">
          <CardContent className="p-6">
            {isLoading ? (
              <div className="flex items-center justify-center py-12">
                <div className="text-center">
                  <div className="text-muted-foreground">加载中...</div>
                </div>
              </div>
            ) : steps.length === 0 ? (
              <div className="flex items-center justify-center py-12">
                <div className="text-center">
                  <div className="text-muted-foreground mb-2">暂无工步数据</div>
                  <div className="text-sm text-muted-foreground">请先创建工步</div>
                </div>
              </div>
            ) : (
              <div className="space-y-4">
                {steps.map((step, index) => (
                  <div key={step.id} className="border rounded-lg p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="font-medium">{step.name}</h3>
                        <p className="text-sm text-muted-foreground">
                          工步 {step.number} - {step.status} - {step.mode}
                        </p>
                      </div>
                      <div className="text-sm text-muted-foreground">
                        电流: {step.parameters.electrical.current.value}mA
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

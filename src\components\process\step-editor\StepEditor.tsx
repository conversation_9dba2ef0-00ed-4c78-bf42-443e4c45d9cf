'use client';

import React, { useEffect } from 'react';
import { Plus, Trash2, ChevronUp, ChevronDown, Edit, Settings } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';

import { StepForm } from './StepForm';
import { StepWizard } from '../step-wizard/StepWizard';
import { JsonImporter } from '../json-manager/JsonImporter';
import { JsonExporter } from '../json-manager/JsonExporter';
import { JsonValidator } from '../json-manager/JsonValidator';
import { StepStatsCard } from './StepStatsCard';
import { StepSender } from '../step-sender/StepSender';
import { GlobalParametersDialog } from '../global-parameters/GlobalParametersDialog';
import { ProcessManager } from '../process-manager/ProcessManager';

import { useStepStore } from '@/lib/process/stores/stepStore';
import { useProcessStore, useCurrentProcess } from '@/lib/process/stores/processStore';
import { StepFormData, Step } from '@/lib/process/types/step';
import { STEP_STATUS_OPTIONS, STEP_MODE_OPTIONS } from '@/lib/process/constants/stepConstants';
import { useDeviceStore, useChannels } from '@/lib/device/stores/deviceStore';

interface StepEditorProps {
  className?: string;
  useWizard?: boolean; // 是否使用向导模式
}

export function StepEditor({ className, useWizard = false }: StepEditorProps) {
  const {
    steps,
    currentStep,
    isEditing,
    editingStep,
    isLoading,
    error,
    startEditing,
    stopEditing,
    saveEditingStep,
    loadSteps,
    createStep,
    saveEditingStepToProcess,
    deleteStep,
    deleteStepFromProcess
  } = useStepStore();

  // 工艺流程状态管理
  const currentProcess = useCurrentProcess();
  const { initialize } = useProcessStore();

  // 设备和通道状态管理
  const { initialize: initializeDevices } = useDeviceStore();
  const channels = useChannels();


  // 获取状态显示文本
  const getStatusLabel = (status: string) => {
    const option = STEP_STATUS_OPTIONS.find(opt => opt.value === status);
    return option?.label || status;
  };

  // 获取模式显示文本
  const getModeLabel = (mode: string) => {
    const option = STEP_MODE_OPTIONS.find(opt => opt.value === mode);
    return option?.label || mode;
  };

  // 组件挂载时初始化工艺流程和设备，但不自动加载工步数据
  useEffect(() => {
    initialize();
    initializeDevices();
  }, [initialize, initializeDevices]);

  const handleCreateStep = () => {
    if (!currentProcess) {
      alert('请先选择或创建一个工艺流程');
      return;
    }
    startEditing(); // 不传参数表示创建新工步
  };



  const handleSaveStep = async () => {
    // 如果有当前工艺流程，保存到工艺流程中；否则使用普通保存
    if (currentProcess) {
      const result = await saveEditingStepToProcess(currentProcess.id);
      if (result.success) {
        // 保存成功，对话框会自动关闭
      }
    } else {
      const result = await saveEditingStep();
      if (result.success) {
        // 保存成功，对话框会自动关闭
      }
    }
  };

  const handleCancelEdit = () => {
    stopEditing();
  };

  const handleDeleteStep = async (step: Step) => {
    if (!confirm(`确定要删除工步"${step.name || '未命名工步'}"吗？`)) {
      return;
    }

    if (currentProcess) {
      await deleteStepFromProcess(currentProcess.id, step.id);
    } else {
      await deleteStep(step.id);
    }
  };

  const handleMoveStepUp = (stepIndex: number) => {
    if (stepIndex === 0) return; // 已经是第一个
    // TODO: 实现工步上移逻辑
    console.log('上移工步:', stepIndex);
  };

  const handleMoveStepDown = (stepIndex: number) => {
    if (stepIndex === steps.length - 1) return; // 已经是最后一个
    // TODO: 实现工步下移逻辑
    console.log('下移工步:', stepIndex);
  };

  const handleImportSteps = async (importedSteps: Step[]) => {
    // 批量创建导入的工步
    for (const step of importedSteps) {
      const stepFormData: StepFormData = {
        name: step.name,
        status: step.status,
        mode: step.mode,
        parameters: step.parameters,
        auxiliaryParams: step.auxiliaryParams,
        notes: step.notes
      };
      await createStep(stepFormData);
    }
    // 刷新工步列表
    await loadSteps();
  };

  // 工艺管理器状态
  const [showProcessManager, setShowProcessManager] = React.useState(false);





  return (
    <div className={`h-full flex flex-col overflow-hidden ${className}`}>
      {/* 头部操作区 */}
      <div className="flex-shrink-0 p-4 border-b">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold tracking-tight">工步编辑器</h2>
            <p className="text-muted-foreground">
              {currentProcess
                ? `当前工艺流程: ${currentProcess.name}`
                : '请先选择或创建工艺流程'
              }
            </p>
          </div>
        
          <div className="flex items-center space-x-2">
            <Button onClick={handleCreateStep}>
              <Plus className="mr-2 h-4 w-4" />
              新建工步
            </Button>

            <JsonImporter
              onImport={handleImportSteps}
              isLoading={isLoading}
            />

            <Button
              variant="outline"
              onClick={() => setShowProcessManager(true)}
            >
              <Settings className="mr-2 h-4 w-4" />
              工艺管理器
            </Button>

            <JsonExporter
              steps={steps}
              selectedStepIds={[]}
              disabled={steps.length === 0}
            />

            <JsonValidator />

            {/* 全局参数设置按钮 */}
            <GlobalParametersDialog />

            {/* 工艺发送按钮 */}
            <StepSender
              steps={steps}
              channels={channels}
            />
          </div>
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="flex-1 overflow-hidden p-4">
        {/* 工步表格显示 */}
        {isLoading ? (
          <div className="flex items-center justify-center h-full">
            <div className="text-center">
              <div className="text-muted-foreground">加载中...</div>
            </div>
          </div>
        ) : steps.length === 0 ? (
          <div className="flex items-center justify-center h-full">
            <div className="text-center">
              <div className="text-muted-foreground mb-2">暂无工步数据</div>
              <div className="text-sm text-muted-foreground">点击"新建工步"开始创建</div>
            </div>
          </div>
        ) : (
          <Card className="h-full">
            <CardContent className="p-0 h-full">
              <div className="overflow-auto h-full">
                <div className="min-w-[1200px]">
                  <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-16">序号</TableHead>
                      <TableHead className="min-w-[120px]">工步名称</TableHead>
                      <TableHead className="min-w-[80px]">状态</TableHead>
                      <TableHead className="min-w-[100px]">模式</TableHead>
                      <TableHead className="min-w-[80px]">电流 (mA)</TableHead>
                      <TableHead className="min-w-[120px]">电压范围 (mV)</TableHead>
                      <TableHead className="min-w-[100px]">时长</TableHead>
                      <TableHead className="min-w-[80px]">容量 (mAh)</TableHead>
                      <TableHead className="min-w-[80px]">功率 (mW)</TableHead>
                      <TableHead className="min-w-[80px]">温度上限 (℃)</TableHead>
                      <TableHead className="min-w-[80px]">温度下限 (℃)</TableHead>
                      <TableHead className="min-w-[80px]">采集间隔 (s)</TableHead>
                      <TableHead className="w-32 sticky right-0 bg-background">操作</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {steps.map((step, index) => (
                      <TableRow key={step.id} className="hover:bg-muted/50">
                        <TableCell className="font-medium">{step.number}</TableCell>
                        <TableCell className="min-w-[120px]">{step.name || '未命名工步'}</TableCell>
                        <TableCell className="min-w-[80px]">
                          <Badge variant="outline">{getStatusLabel(step.status)}</Badge>
                        </TableCell>
                        <TableCell className="min-w-[100px]">
                          <Badge variant="secondary">{getModeLabel(step.mode)}</Badge>
                        </TableCell>
                        <TableCell className="min-w-[80px]">{step.parameters.electrical.current.value}</TableCell>
                        <TableCell className="min-w-[120px]">
                          {step.parameters.electrical.lowerVoltage.value} - {step.parameters.electrical.upperVoltage.value}
                        </TableCell>
                        <TableCell className="min-w-[100px]">{step.parameters.time?.duration?.value || '00:00:00.000'}</TableCell>
                        <TableCell className="min-w-[80px]">{step.parameters.termination?.capacity?.value || 0}</TableCell>
                        <TableCell className="min-w-[80px]">{step.parameters.termination?.power?.value || 0}</TableCell>
                        <TableCell className="min-w-[80px]">{step.parameters.termination?.upperTemperature?.value || 0}</TableCell>
                        <TableCell className="min-w-[80px]">{step.parameters.termination?.lowerTemperature?.value || 0}</TableCell>
                        <TableCell className="min-w-[80px]">{step.parameters.sampling?.samplingInterval?.value || 0}</TableCell>
                        <TableCell className="sticky right-0 bg-background">
                          <div className="flex items-center space-x-1">
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => startEditing(step)}
                              title="编辑工步"
                            >
                              <Edit className="h-3 w-3" />
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handleMoveStepUp(index)}
                              disabled={index === 0}
                              title="上移工步"
                            >
                              <ChevronUp className="h-3 w-3" />
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handleMoveStepDown(index)}
                              disabled={index === steps.length - 1}
                              title="下移工步"
                            >
                              <ChevronDown className="h-3 w-3" />
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handleDeleteStep(step)}
                              title="删除工步"
                              className="text-red-600 hover:text-red-700"
                            >
                              <Trash2 className="h-3 w-3" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                  </Table>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* 工步编辑对话框 */}
        {useWizard ? (
          <StepWizard
            open={isEditing}
            onOpenChange={(open) => !open && stopEditing()}
            initialData={editingStep || undefined}
            onSubmit={handleSaveStep}
            isLoading={isLoading}
          />
        ) : (
          <Dialog open={isEditing} onOpenChange={(open) => !open && stopEditing()}>
            <DialogContent className="max-w-[95vw] w-[95vw] max-h-[95vh] h-[95vh] overflow-hidden flex flex-col">
              <DialogHeader className="flex-shrink-0">
                <DialogTitle>
                  {currentStep ? '编辑工步' : '新建工步'}
                </DialogTitle>
                <DialogDescription>
                  配置工步的基本参数和测试条件
                </DialogDescription>
              </DialogHeader>

              <div className="flex-1 overflow-hidden">
                {editingStep && (
                  <StepForm
                    initialData={editingStep}
                    onSubmit={handleSaveStep}
                    onCancel={handleCancelEdit}
                    isLoading={isLoading}
                  />
                )}
              </div>
            </DialogContent>
          </Dialog>
        )}
      </div>

      {/* 错误提示 */}
      {error && (
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}





      {/* 悬浮统计卡片 */}
      <StepStatsCard steps={steps} />

      {/* 工艺管理器对话框 */}
      <ProcessManager
        open={showProcessManager}
        onOpenChange={setShowProcessManager}
        onProcessSelected={(processId) => {
          console.log('选择了工艺流程:', processId);
          setShowProcessManager(false);
        }}
      />
    </div>
  );
}

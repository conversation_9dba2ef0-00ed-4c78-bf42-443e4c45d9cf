import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import {
  Device,
  Channel,
  Alarm,
  DeviceStatus,
  AlarmLevel,
  DeviceStatistics
} from '@/lib/types';
import {
  logDeviceOnline,
  logDeviceOffline,
  logChannelEnabled,
  logChannelDisabled,
  logChannelParameterExceeded,
  logChannelStepChanged
} from '@/utils/eventLogger';

// 重新导出类型以便其他文件使用
export { DeviceStatus, AlarmLevel } from '@/lib/types';
export type { Device, Channel, Alarm, DeviceStatistics } from '@/lib/types';

// 类型定义已移至 @/lib/types，这里不再重复定义

// 设备状态管理接口
interface DeviceStore {
  // 核心数据
  devices: Device[];
  channels: Channel[];
  alarms: Alarm[];

  // 选择状态
  selectedDevice: string | null;
  selectedChannel: number | null;

  // 模拟设置
  simulationSettings: {
    deviceCount: number[];
    tempVariation: number[];
    updateInterval: number[];
    errorRate: number[];
  };
  isSimulationRunning: boolean;
  simulationTimer: NodeJS.Timeout | null;

  // 网络设置
  localIpAddress: string;
  deviceConnectivityStatus: Record<string, 'online' | 'offline' | 'testing'>;

  // 统计数据
  statistics: DeviceStatistics;

  // 设备和通道操作
  setDevices: (devices: Device[]) => void;
  setChannels: (channels: Channel[]) => void;
  setSelectedDevice: (deviceName: string | null) => void;
  setSelectedChannel: (channelNumber: number | null) => void;
  getChannelsByDevice: (deviceName: string) => Channel[];
  getDeviceByName: (deviceName: string) => Device | undefined;
  getChannelByNumber: (deviceName: string, channelNumber: number) => Channel | undefined;
  updateChannelStatus: (deviceName: string, channelNumber: number, updates: Partial<Channel>) => void;
  updateDeviceStatus: (deviceName: string, status: DeviceStatus) => void;
  updateDevice: (device: Device) => void;
  addDevice: (device: Device) => void;
  removeDevice: (deviceName: string) => void;

  // 网络管理
  setLocalIpAddress: (ip: string) => void;
  testDeviceConnectivity: (deviceName?: string) => Promise<void>;
  updateDeviceConnectivityStatus: (deviceName: string, status: 'online' | 'offline' | 'testing') => void;

  // 报警管理
  addAlarm: (alarm: Omit<Alarm, 'id' | 'timestamp'>) => void;
  acknowledgeAlarm: (alarmId: string, acknowledgedBy: string) => void;
  clearAlarm: (alarmId: string) => void;
  getAlarmsByDevice: (deviceName: string) => Alarm[];
  getActiveAlarms: () => Alarm[];

  // 数据生成和更新
  generateDeviceData: (deviceName: string, index: number) => Device;
  generateChannelsForDevice: (deviceName: string) => Channel[];
  updateSimulationSettings: (settings: Partial<DeviceStore['simulationSettings']>) => void;
  initializeData: () => void;
  startSimulation: () => void;
  stopSimulation: () => void;
  updateDeviceData: () => void;
  updateStatistics: () => void;
}

// 生成设备数据的辅助函数
const generateDeviceData = (deviceName: string, index: number, tempVariation: number = 10, errorRate: number = 2): Device => {
  const statuses = [DeviceStatus.WORKING, DeviceStatus.OFFLINE, DeviceStatus.ERROR, DeviceStatus.INITIALIZING, DeviceStatus.CALIBRATING];
  const locations = ['车间A', '车间B', '车间C', '测试区', '质检区'];

  const generateTemps = (count: number, baseTemp: number, variation: number) =>
    Array.from({ length: count }, () =>
      Math.random() > 0.1 ? Number((baseTemp + (Math.random() - 0.5) * variation).toFixed(1)) : null
    );

  // 根据错误率决定设备状态
  const isError = Math.random() * 100 < errorRate;
  const deviceStatus = isError
    ? statuses[Math.floor(Math.random() * statuses.length)]
    : DeviceStatus.WORKING;

  return {
    deviceName,
    ipAddress: [192, 168, 1, 100 + index],
    status: deviceStatus,
    ambientTemp: generateTemps(4, 25, tempVariation),
    probeTemp: generateTemps(8, 30, tempVariation * 1.5),
    boardTemp: generateTemps(2, 45, tempVariation * 0.8),
    channelTemp: generateTemps(64, 28, tempVariation * 1.2),
    version: `v${Math.floor(Math.random() * 3) + 1}.${Math.floor(Math.random() * 10)}.${Math.floor(Math.random() * 10)}`,
    otherInfo: {
      location: locations[Math.floor(Math.random() * locations.length)],
      installDate: new Date(2023, Math.floor(Math.random() * 12), Math.floor(Math.random() * 28) + 1).toLocaleDateString(),
      lastMaintenance: new Date(2024, Math.floor(Math.random() * 12), Math.floor(Math.random() * 28) + 1).toLocaleDateString()
    }
  };
};

// 生成通道数据的辅助函数
const generateChannelsForDevice = (deviceName: string): Channel[] => {
  const channels = [];
  const statuses = ['充电', '放电', '搁置', '暂停', '完成', '复位'];
  const modes = ['恒流充电', '恒压充电', '恒流放电', '恒压放电', '搁置'];
  
  for (let i = 1; i <= 64; i++) {
    const status = statuses[Math.floor(Math.random() * statuses.length)];
    
    channels.push({
      channelNumber: i,
      stepStatus: status,
      stepMode: modes[Math.floor(Math.random() * modes.length)],
      current: Number((Math.random() * 10 - 5).toFixed(2)),
      voltage: Number((Math.random() * 5 + 3).toFixed(2)),
      capacity: Number((Math.random() * 5000).toFixed(1)),
      energy: Number((Math.random() * 20000).toFixed(1)),
      temperature: Number((Math.random() * 40 + 20).toFixed(1)),
      auxVoltage: Number((Math.random() * 15 + 10).toFixed(1)),
      isEnabled: Math.random() > 0.2,
      totalStepNumber: Math.floor(Math.random() * 100) + 1,
      cycleCount: Math.floor(Math.random() * 10),
      stepNumber: Math.floor(Math.random() * 20) + 1,
      stepTime: Math.floor(Math.random() * 3600),
      processTime: Math.floor(Math.random() * 86400),
      alarmStatus: Math.random() > 0.9 ? 0x0001 : 0x0000,
      processName: '锂电池标准测试',
      trayCode: `TP-20240721-${String(i).padStart(3, '0')}`,
      colorStatus: status,
      deviceName
    });
  }
  return channels;
};

// 创建Zustand store with persistence
export const useDeviceStore = create<DeviceStore>()(
  persist(
    (set, get) => ({
      // 核心数据
      devices: [],
      channels: [],
      alarms: [],

      // 选择状态
      selectedDevice: null,
      selectedChannel: null,

      // 模拟设置
      simulationSettings: {
        deviceCount: [6],
        tempVariation: [5],
        updateInterval: [2],
        errorRate: [2]
      },
      isSimulationRunning: false,
      simulationTimer: null,

      // 网络设置
      localIpAddress: '*************',
      deviceConnectivityStatus: {},

      // 统计数据
      statistics: {
        totalDevices: 0,
        onlineDevices: 0,
        totalChannels: 0,
        activeChannels: 0,
        totalAlarms: 0,
        criticalAlarms: 0,
        lastUpdateTime: null,
      },

      // 基础操作
      setDevices: (devices) => {
        set({ devices });
        get().updateStatistics();
      },

      setChannels: (channels) => {
        set({ channels });
        get().updateStatistics();
      },

      setSelectedDevice: (deviceName) => set({ selectedDevice: deviceName }),

      setSelectedChannel: (channelNumber) => set({ selectedChannel: channelNumber }),

  updateSimulationSettings: (settings) => {
    const currentState = get();
    const newSettings = { ...currentState.simulationSettings, ...settings };
    set({ simulationSettings: newSettings });

    // 如果设备数量发生变化，重新生成设备数据
    if (settings.deviceCount) {
      get().initializeData();
    }

    // 如果更新间隔发生变化，重启模拟
    if (settings.updateInterval && currentState.isSimulationRunning) {
      get().stopSimulation();
      get().startSimulation();
    }
  },

  // 数据查询方法
  getChannelsByDevice: (deviceName) => {
    const { channels } = get();
    return channels.filter(channel => channel.deviceName === deviceName);
  },

  getDeviceByName: (deviceName) => {
    const { devices } = get();
    return devices.find(device => device.deviceName === deviceName);
  },

  getChannelByNumber: (deviceName, channelNumber) => {
    const { channels } = get();
    return channels.find(channel =>
      channel.deviceName === deviceName && channel.channelNumber === channelNumber
    );
  },

  updateChannelStatus: (deviceName, channelNumber, updates) => {
    const { channels } = get();
    const oldChannel = channels.find(channel =>
      channel.deviceName === deviceName && channel.channelNumber === channelNumber
    );

    const updatedChannels = channels.map(channel => {
      if (channel.deviceName === deviceName && channel.channelNumber === channelNumber) {
        return { ...channel, ...updates };
      }
      return channel;
    });
    set({ channels: updatedChannels });
    get().updateStatistics();

    // 记录事件
    if (oldChannel && updates.isEnabled !== undefined && updates.isEnabled !== oldChannel.isEnabled) {
      if (updates.isEnabled) {
        logChannelEnabled(deviceName, channelNumber);
      } else {
        logChannelDisabled(deviceName, channelNumber);
      }
    }

    // 记录工步变更事件
    if (oldChannel && updates.stepStatus && updates.stepStatus !== oldChannel.stepStatus) {
      logChannelStepChanged(deviceName, channelNumber, oldChannel.stepStatus, updates.stepStatus);
    }

    // 检查参数超限
    if (updates.voltage !== undefined && updates.voltage > 4.5) {
      logChannelParameterExceeded(deviceName, channelNumber, '电压', updates.voltage, 4.5);
    }
    if (updates.current !== undefined && updates.current > 3.0) {
      logChannelParameterExceeded(deviceName, channelNumber, '电流', updates.current, 3.0);
    }
  },

  updateDeviceStatus: (deviceName, status) => {
    const { devices } = get();
    const oldDevice = devices.find(device => device.deviceName === deviceName);

    const updatedDevices = devices.map(device => {
      if (device.deviceName === deviceName) {
        return { ...device, status };
      }
      return device;
    });
    set({ devices: updatedDevices });
    get().updateStatistics();

    // 记录设备状态变更事件
    if (oldDevice && oldDevice.status !== status) {
      if (status === DeviceStatus.WORKING) {
        logDeviceOnline(deviceName);
      } else if (status === DeviceStatus.OFFLINE) {
        logDeviceOffline(deviceName);
      }
    }
  },

  // 报警管理方法
  addAlarm: (alarmData) => {
    const alarm: Alarm = {
      ...alarmData,
      id: `alarm_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
      timestamp: new Date(),
    };

    set(state => ({
      alarms: [...state.alarms, alarm]
    }));
    get().updateStatistics();
  },

  acknowledgeAlarm: (alarmId, acknowledgedBy) => {
    set(state => ({
      alarms: state.alarms.map(alarm =>
        alarm.id === alarmId
          ? {
              ...alarm,
              acknowledged: true,
              acknowledgedBy,
              acknowledgedAt: new Date()
            }
          : alarm
      )
    }));
  },

  clearAlarm: (alarmId) => {
    set(state => ({
      alarms: state.alarms.filter(alarm => alarm.id !== alarmId)
    }));
    get().updateStatistics();
  },

  getAlarmsByDevice: (deviceName) => {
    const { alarms } = get();
    return alarms.filter(alarm => alarm.deviceName === deviceName);
  },

  getActiveAlarms: () => {
    const { alarms } = get();
    return alarms.filter(alarm => !alarm.acknowledged);
  },

  generateDeviceData: (deviceName: string, index: number) => {
    const { simulationSettings } = get();
    return generateDeviceData(
      deviceName,
      index,
      simulationSettings.tempVariation[0],
      simulationSettings.errorRate[0]
    );
  },

  generateChannelsForDevice,

  initializeData: () => {
    const { simulationSettings } = get();
    const deviceCount = simulationSettings.deviceCount[0];

    // 生成设备数据
    const devices = Array.from({ length: deviceCount }, (_, i) =>
      generateDeviceData(`Device${i + 1}`, i, simulationSettings.tempVariation[0], simulationSettings.errorRate[0])
    );

    // 生成所有设备的通道数据
    const allChannels: Channel[] = [];
    devices.forEach(device => {
      const deviceChannels = generateChannelsForDevice(device.deviceName);
      allChannels.push(...deviceChannels);
    });

    set({
      devices,
      channels: allChannels,
      selectedDevice: devices.length > 0 ? devices[0].deviceName : null
    });
  },

  startSimulation: () => {
    const { simulationSettings, simulationTimer } = get();

    // 如果已经在运行，先停止
    if (simulationTimer) {
      clearInterval(simulationTimer);
    }

    const interval = setInterval(() => {
      get().updateDeviceData();
    }, simulationSettings.updateInterval[0] * 1000);

    set({
      isSimulationRunning: true,
      simulationTimer: interval
    });
  },

  stopSimulation: () => {
    const { simulationTimer } = get();
    if (simulationTimer) {
      clearInterval(simulationTimer);
    }
    set({
      isSimulationRunning: false,
      simulationTimer: null
    });
  },

  updateDeviceData: () => {
    const { devices, simulationSettings } = get();
    const tempVariation = simulationSettings.tempVariation[0];
    const errorRate = simulationSettings.errorRate[0];

    // 更新设备数据
    const updatedDevices = devices.map((device, index) =>
      generateDeviceData(device.deviceName, index, tempVariation, errorRate)
    );

    // 更新通道数据
    const allChannels: Channel[] = [];
    updatedDevices.forEach(device => {
      const deviceChannels = generateChannelsForDevice(device.deviceName);
      allChannels.push(...deviceChannels);
    });

    set({
      devices: updatedDevices,
      channels: allChannels
    });

    get().updateStatistics();
  },

  // 统计数据更新
  updateStatistics: () => {
    const { devices, channels, alarms } = get();

    const onlineDevices = devices.filter(device => device.status === DeviceStatus.WORKING).length;
    const activeChannels = channels.filter(channel => channel.isEnabled).length;
    const activeAlarms = alarms.filter(alarm => !alarm.acknowledged);
    const criticalAlarms = activeAlarms.filter(alarm => alarm.level === AlarmLevel.CRITICAL).length;

    set({
      statistics: {
        totalDevices: devices.length,
        onlineDevices,
        totalChannels: channels.length,
        activeChannels,
        totalAlarms: activeAlarms.length,
        criticalAlarms,
        lastUpdateTime: new Date(),
      }
    });
  },

  // 设备更新
  updateDevice: (device: Device) => {
    set((state) => ({
      devices: state.devices.map(d =>
        d.deviceName === device.deviceName ? device : d
      )
    }));
    get().updateStatistics();
  },

  // 添加设备
  addDevice: (device: Device) => {
    set((state) => ({
      devices: [...state.devices, device]
    }));

    // 为新设备生成通道数据
    const deviceChannels = generateChannelsForDevice(device.deviceName);
    set((state) => ({
      channels: [...state.channels, ...deviceChannels]
    }));

    get().updateStatistics();
  },

  // 删除设备
  removeDevice: (deviceName: string) => {
    set((state) => ({
      devices: state.devices.filter(d => d.deviceName !== deviceName),
      channels: state.channels.filter(c => c.deviceName !== deviceName),
      alarms: state.alarms.filter(a => a.deviceName !== deviceName)
    }));

    // 如果删除的是当前选中的设备，清除选择
    const { selectedDevice } = get();
    if (selectedDevice === deviceName) {
      set({ selectedDevice: null, selectedChannel: null });
    }

    get().updateStatistics();
  },

  // 网络管理方法
  setLocalIpAddress: (ip: string) => {
    set({ localIpAddress: ip });
  },

  testDeviceConnectivity: async (deviceName?: string) => {
    const state = get();
    const devicesToTest = deviceName
      ? state.devices.filter(d => d.deviceName === deviceName)
      : state.devices;

    // 设置所有要测试的设备为测试中状态
    devicesToTest.forEach(device => {
      state.updateDeviceConnectivityStatus(device.deviceName, 'testing');
    });

    // 模拟连通性测试
    for (const device of devicesToTest) {
      try {
        // 模拟网络延迟
        await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));

        // 模拟连通性结果（70%成功率）
        const isConnected = Math.random() > 0.3;
        state.updateDeviceConnectivityStatus(
          device.deviceName,
          isConnected ? 'online' : 'offline'
        );

        // 同时更新设备状态
        state.updateDeviceStatus(
          device.deviceName,
          isConnected ? DeviceStatus.WORKING : DeviceStatus.ERROR
        );
      } catch (error) {
        state.updateDeviceConnectivityStatus(device.deviceName, 'offline');
        state.updateDeviceStatus(device.deviceName, DeviceStatus.ERROR);
      }
    }
  },

  updateDeviceConnectivityStatus: (deviceName: string, status: 'online' | 'offline' | 'testing') => {
    set((state) => ({
      deviceConnectivityStatus: {
        ...state.deviceConnectivityStatus,
        [deviceName]: status
      }
    }));
  }
}),
    {
      name: 'device-store',
      partialize: (state) => ({
        devices: state.devices,
        channels: state.channels,
        alarms: state.alarms,
        selectedDevice: state.selectedDevice,
        simulationSettings: state.simulationSettings,
        statistics: state.statistics,
      }),
    }
  )
);

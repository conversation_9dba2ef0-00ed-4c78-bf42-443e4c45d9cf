{"name": "langtian-pdcs", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "build:analyze": "ANALYZE=true next build", "build:production": "NODE_ENV=production next build", "build:no-typecheck": "NODE_ENV=production SKIP_TYPE_CHECK=true next build", "start:production": "NODE_ENV=production next start", "export": "next build && next export", "clean": "rm -rf .next out", "type-check": "tsc --noEmit", "docker:build": "docker build -t tianlang-pdcs .", "docker:run": "docker run -p 3000:3000 tianlang-pdcs", "deploy": "npm run build:production && npm run start:production", "deploy:fast": "npm run build:no-typecheck && npm run start:production"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-tooltip": "^1.2.7", "@tanstack/react-query": "^5.83.0", "@tanstack/react-table": "^8.21.3", "@types/better-sqlite3": "^7.6.13", "@types/uuid": "^10.0.0", "better-sqlite3": "^12.2.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "framer-motion": "^12.23.6", "lucide-react": "^0.525.0", "next": "15.4.2", "next-intl": "^4.3.4", "react": "19.1.0", "react-day-picker": "^9.8.0", "react-dom": "19.1.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.60.0", "react-resizable-panels": "^3.0.3", "recharts": "^3.1.0", "tailwind-merge": "^3.3.1", "uuid": "^11.1.0", "vaul": "^1.1.2", "zod": "^4.0.5", "zustand": "^5.0.6"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.11", "@tanstack/react-query-devtools": "^5.83.0", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.21", "postcss": "^8.5.6", "tailwindcss": "^4.0.0", "tw-animate-css": "^1.3.5", "typescript": "^5"}}
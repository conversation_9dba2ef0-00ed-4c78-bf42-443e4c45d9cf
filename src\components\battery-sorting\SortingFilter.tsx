'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Calendar } from '@/components/ui/calendar';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { 
  Filter, 
  Calendar as CalendarIcon, 
  X, 
  RotateCcw,
  Search
} from 'lucide-react';
import { format } from 'date-fns';
import { zhCN } from 'date-fns/locale';
import { SortingFilter, BatteryDevice } from '@/lib/types';
import { useTranslations } from 'next-intl';

interface SortingFilterProps {
  devices: BatteryDevice[];
  filter: SortingFilter;
  onFilterChange: (filter: SortingFilter) => void;
  onApplyFilter: () => void;
  onResetFilter: () => void;
}

export function SortingFilterComponent({
  devices,
  filter,
  onFilterChange,
  onApplyFilter,
  onResetFilter
}: SortingFilterProps) {
  const t = useTranslations('batterySorting.filter');
  const [isDateRangeOpen, setIsDateRangeOpen] = useState(false);

  // 获取所有可用的工步号
  const getAllStepNumbers = (): number[] => {
    const stepNumbers = new Set<number>();
    devices.forEach(device => {
      device.channels.forEach(channel => {
        if (channel.testData) {
          channel.testData.forEach(record => {
            stepNumbers.add(record.stepNumber);
          });
        }
      });
    });
    return Array.from(stepNumbers).sort((a, b) => a - b);
  };

  // 获取所有可用的循环号
  const getAllCycleNumbers = (): number[] => {
    const cycleNumbers = new Set<number>();
    devices.forEach(device => {
      device.channels.forEach(channel => {
        if (channel.testData) {
          channel.testData.forEach(record => {
            cycleNumbers.add(record.cycleNumber);
          });
        }
      });
    });
    return Array.from(cycleNumbers).sort((a, b) => a - b);
  };

  // 更新日期范围
  const updateDateRange = (field: 'start' | 'end', date: Date | undefined) => {
    if (!date) return;
    
    const newDateRange = {
      start: filter.dateRange?.start || new Date(),
      end: filter.dateRange?.end || new Date(),
      [field]: date
    };
    
    onFilterChange({
      ...filter,
      dateRange: newDateRange
    });
  };

  // 切换设备选择
  const toggleDevice = (deviceId: string) => {
    const currentDevices = filter.deviceIds || [];
    const newDevices = currentDevices.includes(deviceId)
      ? currentDevices.filter(id => id !== deviceId)
      : [...currentDevices, deviceId];
    
    onFilterChange({
      ...filter,
      deviceIds: newDevices.length > 0 ? newDevices : undefined
    });
  };

  // 切换工步号选择
  const toggleStepNumber = (stepNumber: number) => {
    const currentSteps = filter.stepNumbers || [];
    const newSteps = currentSteps.includes(stepNumber)
      ? currentSteps.filter(num => num !== stepNumber)
      : [...currentSteps, stepNumber];
    
    onFilterChange({
      ...filter,
      stepNumbers: newSteps.length > 0 ? newSteps : undefined
    });
  };

  // 切换循环号选择
  const toggleCycleNumber = (cycleNumber: number) => {
    const currentCycles = filter.cycleNumbers || [];
    const newCycles = currentCycles.includes(cycleNumber)
      ? currentCycles.filter(num => num !== cycleNumber)
      : [...currentCycles, cycleNumber];
    
    onFilterChange({
      ...filter,
      cycleNumbers: newCycles.length > 0 ? newCycles : undefined
    });
  };

  // 切换等级选择
  const toggleGrade = (grade: string) => {
    const currentGrades = filter.grades || [];
    const newGrades = currentGrades.includes(grade)
      ? currentGrades.filter(g => g !== grade)
      : [...currentGrades, grade];
    
    onFilterChange({
      ...filter,
      grades: newGrades.length > 0 ? newGrades : undefined
    });
  };

  // 切换状态选择
  const toggleStatus = (status: 'idle' | 'testing' | 'completed' | 'error') => {
    const currentStatuses = filter.status || [];
    const newStatuses = currentStatuses.includes(status)
      ? currentStatuses.filter(s => s !== status)
      : [...currentStatuses, status];
    
    onFilterChange({
      ...filter,
      status: newStatuses.length > 0 ? newStatuses : undefined
    });
  };

  // 检查是否有活动筛选
  const hasActiveFilters = () => {
    return !!(
      filter.dateRange ||
      filter.deviceIds?.length ||
      filter.stepNumbers?.length ||
      filter.cycleNumbers?.length ||
      filter.grades?.length ||
      filter.status?.length
    );
  };

  const stepNumbers = getAllStepNumbers();
  const cycleNumbers = getAllCycleNumbers();
  const availableGrades = ['A级', 'B级', 'C级', 'D级', 'E级']; // 示例等级
  const availableStatuses = [
    { value: 'idle', label: t('status.idle') },
    { value: 'testing', label: t('status.testing') },
    { value: 'completed', label: t('status.completed') },
    { value: 'error', label: t('status.error') }
  ] as const;

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            {t('title')}
            {hasActiveFilters() && (
              <Badge variant="default" className="ml-2">
                {t('active')}
              </Badge>
            )}
          </CardTitle>
          
          <div className="flex gap-2">
            <Button variant="outline" size="sm" onClick={onResetFilter}>
              <RotateCcw className="h-4 w-4 mr-2" />
              {t('reset')}
            </Button>
            <Button size="sm" onClick={onApplyFilter}>
              <Search className="h-4 w-4 mr-2" />
              {t('apply')}
            </Button>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* 日期范围筛选 */}
        <div className="space-y-2">
          <Label>{t('dateRange')}</Label>
          <div className="flex gap-2">
            <Popover open={isDateRangeOpen} onOpenChange={setIsDateRangeOpen}>
              <PopoverTrigger asChild>
                <Button variant="outline" className="justify-start text-left font-normal">
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {filter.dateRange ? (
                    `${format(filter.dateRange.start, 'yyyy-MM-dd', { locale: zhCN })} - ${format(filter.dateRange.end, 'yyyy-MM-dd', { locale: zhCN })}`
                  ) : (
                    t('selectDateRange')
                  )}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                <div className="p-4 space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label className="text-sm">{t('startDate')}</Label>
                      <Calendar
                        mode="single"
                        selected={filter.dateRange?.start}
                        onSelect={(date) => updateDateRange('start', date)}
                        locale={zhCN}
                      />
                    </div>
                    <div>
                      <Label className="text-sm">{t('endDate')}</Label>
                      <Calendar
                        mode="single"
                        selected={filter.dateRange?.end}
                        onSelect={(date) => updateDateRange('end', date)}
                        locale={zhCN}
                      />
                    </div>
                  </div>
                  <div className="flex justify-end gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        onFilterChange({ ...filter, dateRange: undefined });
                        setIsDateRangeOpen(false);
                      }}
                    >
                      {t('clear')}
                    </Button>
                    <Button size="sm" onClick={() => setIsDateRangeOpen(false)}>
                      {t('confirm')}
                    </Button>
                  </div>
                </div>
              </PopoverContent>
            </Popover>
          </div>
        </div>

        {/* 设备筛选 */}
        <div className="space-y-2">
          <Label>{t('devices')}</Label>
          <div className="flex flex-wrap gap-2">
            {devices.map((device) => (
              <Badge
                key={device.id}
                variant={filter.deviceIds?.includes(device.id) ? 'default' : 'outline'}
                className="cursor-pointer"
                onClick={() => toggleDevice(device.id)}
              >
                {device.name}
                {filter.deviceIds?.includes(device.id) && (
                  <X className="ml-1 h-3 w-3" />
                )}
              </Badge>
            ))}
          </div>
        </div>

        {/* 工步号筛选 */}
        {stepNumbers.length > 0 && (
          <div className="space-y-2">
            <Label>{t('stepNumbers')}</Label>
            <div className="flex flex-wrap gap-2">
              {stepNumbers.map((stepNumber) => (
                <Badge
                  key={stepNumber}
                  variant={filter.stepNumbers?.includes(stepNumber) ? 'default' : 'outline'}
                  className="cursor-pointer"
                  onClick={() => toggleStepNumber(stepNumber)}
                >
                  {t('step')} {stepNumber}
                  {filter.stepNumbers?.includes(stepNumber) && (
                    <X className="ml-1 h-3 w-3" />
                  )}
                </Badge>
              ))}
            </div>
          </div>
        )}

        {/* 循环号筛选 */}
        {cycleNumbers.length > 0 && (
          <div className="space-y-2">
            <Label>{t('cycleNumbers')}</Label>
            <div className="flex flex-wrap gap-2">
              {cycleNumbers.slice(0, 10).map((cycleNumber) => (
                <Badge
                  key={cycleNumber}
                  variant={filter.cycleNumbers?.includes(cycleNumber) ? 'default' : 'outline'}
                  className="cursor-pointer"
                  onClick={() => toggleCycleNumber(cycleNumber)}
                >
                  {t('cycle')} {cycleNumber}
                  {filter.cycleNumbers?.includes(cycleNumber) && (
                    <X className="ml-1 h-3 w-3" />
                  )}
                </Badge>
              ))}
              {cycleNumbers.length > 10 && (
                <Badge variant="outline">
                  +{cycleNumbers.length - 10} {t('more')}
                </Badge>
              )}
            </div>
          </div>
        )}

        {/* 等级筛选 */}
        <div className="space-y-2">
          <Label>{t('grades')}</Label>
          <div className="flex flex-wrap gap-2">
            {availableGrades.map((grade) => (
              <Badge
                key={grade}
                variant={filter.grades?.includes(grade) ? 'default' : 'outline'}
                className="cursor-pointer"
                onClick={() => toggleGrade(grade)}
              >
                {grade}
                {filter.grades?.includes(grade) && (
                  <X className="ml-1 h-3 w-3" />
                )}
              </Badge>
            ))}
          </div>
        </div>

        {/* 状态筛选 */}
        <div className="space-y-2">
          <Label>{t('channelStatus')}</Label>
          <div className="flex flex-wrap gap-2">
            {availableStatuses.map((status) => (
              <Badge
                key={status.value}
                variant={filter.status?.includes(status.value) ? 'default' : 'outline'}
                className="cursor-pointer"
                onClick={() => toggleStatus(status.value)}
              >
                {status.label}
                {filter.status?.includes(status.value) && (
                  <X className="ml-1 h-3 w-3" />
                )}
              </Badge>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

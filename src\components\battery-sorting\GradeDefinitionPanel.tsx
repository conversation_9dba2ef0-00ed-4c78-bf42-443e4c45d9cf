'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Plus, Trash2, Edit, Palette } from 'lucide-react';
import { SortingGrade } from '@/lib/types';
import { useTranslations } from 'next-intl';

interface GradeDefinitionPanelProps {
  grades: SortingGrade[];
  onGradesChange: (grades: SortingGrade[]) => void;
}

// 预定义的颜色选项
const PRESET_COLORS = [
  { name: 'A级绿', value: '#22c55e' },
  { name: 'B级蓝', value: '#3b82f6' },
  { name: 'C级黄', value: '#f59e0b' },
  { name: 'D级橙', value: '#f97316' },
  { name: 'E级红', value: '#ef4444' },
  { name: '紫色', value: '#8b5cf6' },
  { name: '青色', value: '#06b6d4' },
  { name: '粉色', value: '#ec4899' },
];

export function GradeDefinitionPanel({
  grades,
  onGradesChange
}: GradeDefinitionPanelProps) {
  const t = useTranslations('batterySorting.grades');
  const [editingGrade, setEditingGrade] = useState<SortingGrade | null>(null);
  const [isAddingNew, setIsAddingNew] = useState(false);

  // 创建新等级的默认值
  const createNewGrade = (): SortingGrade => ({
    id: generateId(),
    name: '',
    color: PRESET_COLORS[grades.length % PRESET_COLORS.length].value,
    priority: grades.length + 1,
    conditions: '',
    description: ''
  });

  const handleAddGrade = () => {
    setEditingGrade(createNewGrade());
    setIsAddingNew(true);
  };

  const handleEditGrade = (grade: SortingGrade) => {
    setEditingGrade({ ...grade });
    setIsAddingNew(false);
  };

  const handleSaveGrade = () => {
    if (!editingGrade || !editingGrade.name.trim()) return;

    if (isAddingNew) {
      onGradesChange([...grades, editingGrade]);
    } else {
      onGradesChange(
        grades.map(g => g.id === editingGrade.id ? editingGrade : g)
      );
    }

    setEditingGrade(null);
    setIsAddingNew(false);
  };

  const handleDeleteGrade = (gradeId: string) => {
    const updatedGrades = grades.filter(g => g.id !== gradeId);
    // 重新调整优先级
    const reorderedGrades = updatedGrades.map((grade, index) => ({
      ...grade,
      priority: index + 1
    }));
    onGradesChange(reorderedGrades);
  };

  const handleCancelEdit = () => {
    setEditingGrade(null);
    setIsAddingNew(false);
  };

  const movePriority = (gradeId: string, direction: 'up' | 'down') => {
    const currentIndex = grades.findIndex(g => g.id === gradeId);
    if (currentIndex === -1) return;

    const newIndex = direction === 'up' ? currentIndex - 1 : currentIndex + 1;
    if (newIndex < 0 || newIndex >= grades.length) return;

    const newGrades = [...grades];
    [newGrades[currentIndex], newGrades[newIndex]] = [newGrades[newIndex], newGrades[currentIndex]];
    
    // 更新优先级
    const reorderedGrades = newGrades.map((grade, index) => ({
      ...grade,
      priority: index + 1
    }));
    
    onGradesChange(reorderedGrades);
  };

  const generateId = () => Date.now().toString(36) + Math.random().toString(36).substr(2);

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            {t('title')}
            <Badge variant="secondary">{grades.length}</Badge>
          </CardTitle>
          <Button onClick={handleAddGrade} size="sm">
            <Plus className="h-4 w-4 mr-2" />
            {t('addGrade')}
          </Button>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* 等级列表 */}
        <div className="border rounded-lg">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-[60px]">{t('priority')}</TableHead>
                <TableHead>{t('gradeName')}</TableHead>
                <TableHead>{t('color')}</TableHead>
                <TableHead>{t('conditions')}</TableHead>
                <TableHead className="w-[120px]">{t('actions')}</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {grades.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={5} className="text-center text-muted-foreground py-8">
                    {t('noGrades')}
                  </TableCell>
                </TableRow>
              ) : (
                grades
                  .sort((a, b) => a.priority - b.priority)
                  .map((grade, index) => (
                    <TableRow key={grade.id}>
                      <TableCell>
                        <div className="flex flex-col gap-1">
                          <Badge variant="outline" className="w-fit">
                            {grade.priority}
                          </Badge>
                          <div className="flex gap-1">
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-6 w-6 p-0"
                              onClick={() => movePriority(grade.id, 'up')}
                              disabled={index === 0}
                            >
                              ↑
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-6 w-6 p-0"
                              onClick={() => movePriority(grade.id, 'down')}
                              disabled={index === grades.length - 1}
                            >
                              ↓
                            </Button>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell className="font-medium">{grade.name}</TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <div
                            className="w-6 h-6 rounded border"
                            style={{ backgroundColor: grade.color }}
                          />
                          <span className="text-sm text-muted-foreground">
                            {grade.color}
                          </span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="max-w-xs">
                          <code className="text-xs bg-muted px-2 py-1 rounded">
                            {grade.conditions || t('noConditions')}
                          </code>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex gap-1">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleEditGrade(grade)}
                          >
                            <Edit className="h-3 w-3" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDeleteGrade(grade.id)}
                          >
                            <Trash2 className="h-3 w-3" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
              )}
            </TableBody>
          </Table>
        </div>

        {/* 编辑表单 */}
        {editingGrade && (
          <Card className="border-2 border-primary">
            <CardHeader>
              <CardTitle className="text-lg">
                {isAddingNew ? t('addNewGrade') : t('editGrade')}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="gradeName">{t('gradeName')}</Label>
                  <Input
                    id="gradeName"
                    value={editingGrade.name}
                    onChange={(e) => setEditingGrade({
                      ...editingGrade,
                      name: e.target.value
                    })}
                    placeholder={t('gradeNamePlaceholder')}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="priority">{t('priority')}</Label>
                  <Input
                    id="priority"
                    type="number"
                    min="1"
                    value={editingGrade.priority}
                    onChange={(e) => setEditingGrade({
                      ...editingGrade,
                      priority: parseInt(e.target.value) || 1
                    })}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label>{t('color')}</Label>
                <div className="flex flex-wrap gap-2">
                  {PRESET_COLORS.map((color) => (
                    <button
                      key={color.value}
                      className={`w-8 h-8 rounded border-2 ${
                        editingGrade.color === color.value 
                          ? 'border-primary' 
                          : 'border-muted'
                      }`}
                      style={{ backgroundColor: color.value }}
                      onClick={() => setEditingGrade({
                        ...editingGrade,
                        color: color.value
                      })}
                      title={color.name}
                    />
                  ))}
                  <div className="flex items-center gap-2">
                    <Palette className="h-4 w-4 text-muted-foreground" />
                    <Input
                      type="color"
                      value={editingGrade.color}
                      onChange={(e) => setEditingGrade({
                        ...editingGrade,
                        color: e.target.value
                      })}
                      className="w-12 h-8 p-0 border-0"
                    />
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="conditions">{t('conditions')}</Label>
                <Textarea
                  id="conditions"
                  value={editingGrade.conditions}
                  onChange={(e) => setEditingGrade({
                    ...editingGrade,
                    conditions: e.target.value
                  })}
                  placeholder={t('conditionsPlaceholder')}
                  rows={3}
                />
                <p className="text-xs text-muted-foreground">
                  {t('conditionsHelp')}
                </p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">{t('description')}</Label>
                <Input
                  id="description"
                  value={editingGrade.description || ''}
                  onChange={(e) => setEditingGrade({
                    ...editingGrade,
                    description: e.target.value
                  })}
                  placeholder={t('descriptionPlaceholder')}
                />
              </div>

              <div className="flex gap-2 justify-end">
                <Button variant="outline" onClick={handleCancelEdit}>
                  {t('cancel')}
                </Button>
                <Button 
                  onClick={handleSaveGrade}
                  disabled={!editingGrade.name.trim()}
                >
                  {isAddingNew ? t('add') : t('save')}
                </Button>
              </div>
            </CardContent>
          </Card>
        )}
      </CardContent>
    </Card>
  );
}

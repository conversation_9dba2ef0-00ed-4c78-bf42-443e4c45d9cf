'use client';

import React, { useState, useMemo } from 'react';
import {
  useReactTable,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  ColumnFiltersState,
  SortingState,
  VisibilityState,
  RowSelectionState,
  flexRender,
} from '@tanstack/react-table';

import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight } from 'lucide-react';

import { StepTableFilters } from './StepTableFilters';
import { StepTableActions } from './StepTableActions';
import { createStepTableColumns } from './StepTableColumns';

import { Step, StepStatus, StepMode, StepBatchOperation } from '@/lib/process/types/step';
import { useStepStore } from '@/lib/process/stores/stepStore';

interface StepTableProps {
  className?: string;
}

export function StepTable({ className }: StepTableProps) {
  const {
    steps,
    isLoading,
    error,
    startEditing,
    duplicateStep,
    deleteStep,
    executeBatchOperation
  } = useStepStore();

  // 表格状态
  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});
  const [rowSelection, setRowSelection] = useState<RowSelectionState>({});
  const [globalFilter, setGlobalFilter] = useState('');
  
  // 自定义筛选状态
  const [statusFilter, setStatusFilter] = useState<StepStatus[]>([]);
  const [modeFilter, setModeFilter] = useState<StepMode[]>([]);

  // 表格列定义
  const columns = useMemo(
    () => createStepTableColumns(
      (step) => startEditing(step),
      (step) => duplicateStep(step.id),
      (step) => deleteStep(step.id)
    ),
    [startEditing, duplicateStep, deleteStep]
  );

  // 筛选后的数据
  const filteredData = useMemo(() => {
    let filtered = steps;

    // 状态筛选
    if (statusFilter.length > 0) {
      filtered = filtered.filter(step => statusFilter.includes(step.status as StepStatus));
    }

    // 模式筛选
    if (modeFilter.length > 0) {
      filtered = filtered.filter(step => modeFilter.includes(step.mode as StepMode));
    }

    return filtered;
  }, [steps, statusFilter, modeFilter]);

  // 表格实例
  const table = useReactTable({
    data: filteredData,
    columns,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    onGlobalFilterChange: setGlobalFilter,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
      globalFilter,
    },
    initialState: {
      pagination: {
        pageSize: 20,
      },
    },
  });

  // 获取选中的工步
  const selectedSteps = table.getFilteredSelectedRowModel().rows.map(row => row.original);
  const selectedStepIds = selectedSteps.map(step => step.id);

  // 处理批量操作
  const handleBatchOperation = async (operation: StepBatchOperation) => {
    await executeBatchOperation(operation);
    // 清除选择
    setRowSelection({});
  };

  // 清除所有筛选器
  const handleClearFilters = () => {
    setGlobalFilter('');
    setStatusFilter([]);
    setModeFilter([]);
    setColumnFilters([]);
    table.resetColumnFilters();
  };

  if (error) {
    return (
      <Card className={className}>
        <CardContent className="flex items-center justify-center py-12">
          <div className="text-center">
            <div className="text-red-500 mb-2">加载失败</div>
            <div className="text-sm text-muted-foreground">{error}</div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={`h-full flex flex-col overflow-hidden ${className}`}>
      {/* 表格头部 */}
      <div className="flex-shrink-0">
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle>工步列表</CardTitle>
                <CardDescription>
                  管理和查看所有工步配置
                </CardDescription>
              </div>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* 筛选器 */}
            <StepTableFilters
              table={table}
              globalFilter={globalFilter}
              onGlobalFilterChange={setGlobalFilter}
              statusFilter={statusFilter}
              onStatusFilterChange={setStatusFilter}
              modeFilter={modeFilter}
              onModeFilterChange={setModeFilter}
              onClearFilters={handleClearFilters}
            />

            {/* 批量操作 */}
            <StepTableActions
              table={table}
              selectedSteps={selectedSteps}
              onBatchOperation={handleBatchOperation}
              isLoading={isLoading}
            />
          </CardContent>
        </Card>
      </div>

      {/* 表格主体 */}
      <div className="flex-1 overflow-hidden">
        <Card className="h-full">
          <CardContent className="p-0 h-full flex flex-col">
            <div className="flex-1 overflow-auto">
              <Table>
              <TableHeader>
                {table.getHeaderGroups().map((headerGroup) => (
                  <TableRow key={headerGroup.id}>
                    {headerGroup.headers.map((header) => (
                      <TableHead 
                        key={header.id}
                        style={{ width: header.getSize() }}
                      >
                        {header.isPlaceholder
                          ? null
                          : flexRender(
                              header.column.columnDef.header,
                              header.getContext()
                            )}
                      </TableHead>
                    ))}
                  </TableRow>
                ))}
              </TableHeader>
              <TableBody>
                {isLoading ? (
                  <TableRow>
                    <TableCell 
                      colSpan={columns.length} 
                      className="h-24 text-center"
                    >
                      加载中...
                    </TableCell>
                  </TableRow>
                ) : table.getRowModel().rows?.length ? (
                  table.getRowModel().rows.map((row) => (
                    <TableRow
                      key={row.id}
                      data-state={row.getIsSelected() && "selected"}
                      className="hover:bg-muted/50"
                    >
                      {row.getVisibleCells().map((cell) => (
                        <TableCell 
                          key={cell.id}
                          style={{ width: cell.column.getSize() }}
                        >
                          {flexRender(
                            cell.column.columnDef.cell,
                            cell.getContext()
                          )}
                        </TableCell>
                      ))}
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell 
                      colSpan={columns.length} 
                      className="h-24 text-center"
                    >
                      暂无数据
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* 分页控制 */}
      <Card>
        <CardContent className="flex items-center justify-between py-4">
          <div className="flex items-center space-x-2">
            <p className="text-sm text-muted-foreground">
              显示第 {table.getState().pagination.pageIndex + 1} 页，
              共 {table.getPageCount()} 页，
              总计 {table.getFilteredRowModel().rows.length} 条记录
            </p>
          </div>

          <div className="flex items-center space-x-6">
            {/* 每页显示数量 */}
            <div className="flex items-center space-x-2">
              <p className="text-sm font-medium">每页显示</p>
              <Select
                value={`${table.getState().pagination.pageSize}`}
                onValueChange={(value) => {
                  table.setPageSize(Number(value));
                }}
              >
                <SelectTrigger className="h-8 w-[70px]">
                  <SelectValue placeholder={table.getState().pagination.pageSize} />
                </SelectTrigger>
                <SelectContent side="top">
                  {[10, 20, 30, 40, 50].map((pageSize) => (
                    <SelectItem key={pageSize} value={`${pageSize}`}>
                      {pageSize}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* 分页按钮 */}
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                className="h-8 w-8 p-0"
                onClick={() => table.setPageIndex(0)}
                disabled={!table.getCanPreviousPage()}
              >
                <span className="sr-only">跳转到第一页</span>
                <ChevronsLeft className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                className="h-8 w-8 p-0"
                onClick={() => table.previousPage()}
                disabled={!table.getCanPreviousPage()}
              >
                <span className="sr-only">上一页</span>
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                className="h-8 w-8 p-0"
                onClick={() => table.nextPage()}
                disabled={!table.getCanNextPage()}
              >
                <span className="sr-only">下一页</span>
                <ChevronRight className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                className="h-8 w-8 p-0"
                onClick={() => table.setPageIndex(table.getPageCount() - 1)}
                disabled={!table.getCanNextPage()}
              >
                <span className="sr-only">跳转到最后一页</span>
                <ChevronsRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
      </div>
    </div>
  );
}

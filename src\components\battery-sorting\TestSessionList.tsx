'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Calendar } from '@/components/ui/calendar';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import {
  Calendar as CalendarIcon,
  Clock,
  User,
  CheckCircle,
  XCircle,
  Loader,
  Play,
  AlertTriangle
} from 'lucide-react';
import { TestSession } from '@/lib/types/battery-sorting';
import { format } from 'date-fns';
import { zhCN } from 'date-fns/locale';
import { cn } from '@/lib/utils';

interface TestSessionListProps {
  sessions: TestSession[];
  selectedSessionId?: string;
  onSessionSelect: (session: TestSession) => void;
  isLoading?: boolean;
  className?: string;
}

const STATUS_CONFIG = {
  running: {
    label: '运行中',
    icon: Loader,
    color: 'text-blue-600',
    bgColor: 'bg-blue-50 border-blue-200'
  },
  completed: {
    label: '已完成',
    icon: CheckCircle,
    color: 'text-green-600',
    bgColor: 'bg-green-50 border-green-200'
  },
  failed: {
    label: '失败',
    icon: XCircle,
    color: 'text-red-600',
    bgColor: 'bg-red-50 border-red-200'
  },
  cancelled: {
    label: '已取消',
    icon: AlertTriangle,
    color: 'text-orange-600',
    bgColor: 'bg-orange-50 border-orange-200'
  }
};

export function TestSessionList({
  sessions,
  selectedSessionId,
  onSessionSelect,
  isLoading = false,
  className
}: TestSessionListProps) {
  const [dateRange, setDateRange] = useState<{
    start?: Date;
    end?: Date;
  }>({});
  const [isDateRangeOpen, setIsDateRangeOpen] = useState(false);

  // 更新日期范围
  const updateDateRange = (type: 'start' | 'end', date?: Date) => {
    setDateRange(prev => ({
      ...prev,
      [type]: date
    }));
  };

  // 过滤会话
  const filteredSessions = sessions.filter(session => {
    if (dateRange.start && session.startTime < dateRange.start) {
      return false;
    }
    if (dateRange.end) {
      const endOfDay = new Date(dateRange.end);
      endOfDay.setHours(23, 59, 59, 999);
      if (session.startTime > endOfDay) {
        return false;
      }
    }
    return true;
  });

  const formatDuration = (startTime: Date, endTime?: Date) => {
    if (!endTime) return '进行中...';
    const duration = endTime.getTime() - startTime.getTime();
    const hours = Math.floor(duration / (1000 * 60 * 60));
    const minutes = Math.floor((duration % (1000 * 60 * 60)) / (1000 * 60));
    return `${hours}h ${minutes}m`;
  };

  return (
    <Card className={`${className} flex flex-col h-full`}>
      <CardHeader className="flex-shrink-0">
        <CardTitle className="flex items-center justify-between">
          测试列表
          <Badge variant="outline">{filteredSessions.length} 个测试</Badge>
        </CardTitle>
        <CardDescription>
          工艺流程测试会话列表
        </CardDescription>
      </CardHeader>
      <CardContent className="flex flex-col flex-1 space-y-4 overflow-hidden">
        {/* 时间筛选器 */}
        <div className="space-y-3 flex-shrink-0">
          <Label className="text-sm font-medium">时间筛选</Label>
          <Popover open={isDateRangeOpen} onOpenChange={setIsDateRangeOpen}>
            <PopoverTrigger asChild>
              <Button variant="outline" className="w-full justify-start text-left font-normal text-xs">
                <CalendarIcon className="mr-2 h-3 w-3" />
                {dateRange.start || dateRange.end ? (
                  `${dateRange.start ? format(dateRange.start, 'MM-dd', { locale: zhCN }) : '开始'} - ${dateRange.end ? format(dateRange.end, 'MM-dd', { locale: zhCN }) : '结束'}`
                ) : (
                  '选择日期范围'
                )}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="start">
              <div className="p-4 space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label className="text-sm">开始日期</Label>
                    <Calendar
                      mode="single"
                      selected={dateRange.start}
                      onSelect={(date) => updateDateRange('start', date)}
                      locale={zhCN}
                    />
                  </div>
                  <div>
                    <Label className="text-sm">结束日期</Label>
                    <Calendar
                      mode="single"
                      selected={dateRange.end}
                      onSelect={(date) => updateDateRange('end', date)}
                      locale={zhCN}
                    />
                  </div>
                </div>
                <div className="flex justify-end gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      setDateRange({});
                      setIsDateRangeOpen(false);
                    }}
                  >
                    清除
                  </Button>
                  <Button size="sm" onClick={() => setIsDateRangeOpen(false)}>
                    确认
                  </Button>
                </div>
              </div>
            </PopoverContent>
          </Popover>
        </div>

        {/* 测试会话列表 */}
        <div className="flex-1 overflow-hidden">
          <ScrollArea className="h-full">
          <div className="space-y-3">
            {isLoading ? (
              <div className="flex items-center justify-center py-8">
                <Loader className="h-6 w-6 animate-spin mr-2" />
                <span className="text-sm text-muted-foreground">加载中...</span>
              </div>
            ) : filteredSessions.length === 0 ? (
              <div className="text-center py-8">
                <CalendarIcon className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                <p className="text-sm text-muted-foreground">暂无测试记录</p>
              </div>
            ) : (
              filteredSessions.map((session) => {
                const statusConfig = STATUS_CONFIG[session.status];
                const StatusIcon = statusConfig.icon;
                const isSelected = session.id === selectedSessionId;
                
                return (
                  <div
                    key={session.id}
                    className={cn(
                      'p-3 rounded-lg border cursor-pointer transition-all hover:shadow-sm',
                      isSelected 
                        ? 'border-primary bg-primary/5 shadow-sm' 
                        : 'border-border hover:border-primary/50',
                      statusConfig.bgColor
                    )}
                    onClick={() => onSessionSelect(session)}
                  >
                    <div className="space-y-2">
                      {/* 标题和状态 */}
                      <div className="flex items-center justify-between">
                        <h4 className="font-medium text-sm truncate">
                          {session.processName}
                        </h4>
                        <div className="flex items-center space-x-1">
                          <StatusIcon className={cn('h-3 w-3', statusConfig.color)} />
                          <span className={cn('text-xs', statusConfig.color)}>
                            {statusConfig.label}
                          </span>
                        </div>
                      </div>
                      
                      {/* 时间信息 */}
                      <div className="flex items-center space-x-2 text-xs text-muted-foreground">
                        <Clock className="h-3 w-3" />
                        <span>
                          {format(session.startTime, 'MM-dd HH:mm', { locale: zhCN })}
                        </span>
                        <span>•</span>
                        <span>{formatDuration(session.startTime, session.endTime)}</span>
                      </div>
                      
                      {/* 操作员 */}
                      <div className="flex items-center space-x-2 text-xs text-muted-foreground">
                        <User className="h-3 w-3" />
                        <span>{session.operator}</span>
                      </div>
                      
                      {/* 统计信息 */}
                      <div className="grid grid-cols-3 gap-2 text-xs">
                        <div className="text-center">
                          <div className="font-medium">{session.totalBatteries}</div>
                          <div className="text-muted-foreground">总数</div>
                        </div>
                        <div className="text-center">
                          <div className="font-medium text-green-600">{session.passedBatteries}</div>
                          <div className="text-muted-foreground">合格</div>
                        </div>
                        <div className="text-center">
                          <div className="font-medium">
                            {session.statistics.passRate.toFixed(1)}%
                          </div>
                          <div className="text-muted-foreground">通过率</div>
                        </div>
                      </div>
                      
                      {/* 描述 */}
                      {session.description && (
                        <p className="text-xs text-muted-foreground truncate">
                          {session.description}
                        </p>
                      )}
                    </div>
                  </div>
                );
              })
            )}
          </div>
          </ScrollArea>
        </div>
      </CardContent>
    </Card>
  );
}

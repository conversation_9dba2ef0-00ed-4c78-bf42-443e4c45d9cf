'use client';

import React from 'react';
import { useTranslations } from 'next-intl';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';

import { StepFormData, StepStatus, StepMode } from '@/lib/process/types/step';
import { STEP_STATUS_OPTIONS, STEP_MODE_OPTIONS } from '@/lib/process/constants/stepConstants';

interface BasicParametersStepProps {
  data: StepFormData;
  onChange: (data: Partial<StepFormData>) => void;
  errors: string[];
}

export function BasicParametersStep({ data, onChange, errors }: BasicParametersStepProps) {
  const t = useTranslations('process.stepWizard.basicParameters');

  const handleBasicInfoChange = (field: string, value: any) => {
    onChange({ [field]: value });
  };

  const handleParameterChange = (category: string, field: string, value: number) => {
    const currentCategory = data.parameters[category as keyof typeof data.parameters] as any;
    onChange({
      parameters: {
        ...data.parameters,
        [category]: {
          ...currentCategory,
          [field]: {
            ...currentCategory[field],
            value
          }
        }
      }
    });
  };

  const getStatusBadge = (status: string) => {
    const statusOption = STEP_STATUS_OPTIONS.find(opt => opt.value === status);
    if (!statusOption) return null;

    return (
      <div className="flex items-center space-x-2">
        <div 
          className="w-3 h-3 rounded-full" 
          style={{ backgroundColor: statusOption.color }}
        />
        <span>{statusOption.label}</span>
      </div>
    );
  };

  const getModeDescription = (mode: string) => {
    const modeOption = STEP_MODE_OPTIONS.find(opt => opt.value === mode);
    return modeOption?.description || '';
  };

  return (
    <div className="space-y-6">
      {/* 基本信息 */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">基本信息</CardTitle>
          <CardDescription>设置工步的基本属性和标识</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="stepName">工步名称 *</Label>
              <Input
                id="stepName"
                placeholder="请输入工步名称"
                value={data.name}
                onChange={(e) => handleBasicInfoChange('name', e.target.value)}
                className={errors.some(e => e.includes('名称')) ? 'border-red-500' : ''}
              />
              <p className="text-xs text-muted-foreground">
                用于标识和区分不同的工步
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="stepStatus">工步状态 *</Label>
              <Select 
                value={data.status} 
                onValueChange={(value) => handleBasicInfoChange('status', value as StepStatus)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="选择工步状态" />
                </SelectTrigger>
                <SelectContent>
                  {STEP_STATUS_OPTIONS.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {getStatusBadge(option.value)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <p className="text-xs text-muted-foreground">
                定义工步的运行状态类型
              </p>
            </div>

            <div className="space-y-2 md:col-span-2">
              <Label htmlFor="stepMode">工步模式 *</Label>
              <Select 
                value={data.mode} 
                onValueChange={(value) => handleBasicInfoChange('mode', value as StepMode)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="选择工步模式" />
                </SelectTrigger>
                <SelectContent>
                  {STEP_MODE_OPTIONS.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      <div>
                        <div className="font-medium">{option.label}</div>
                        <div className="text-sm text-muted-foreground">{option.description}</div>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {data.mode && (
                <div className="p-3 bg-blue-50 border border-blue-200 rounded-md">
                  <p className="text-sm text-blue-800">
                    <strong>当前模式：</strong> {getModeDescription(data.mode)}
                  </p>
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 电气参数 */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">电气参数</CardTitle>
          <CardDescription>配置电流和电压相关的核心参数</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="current">电流 (mA) *</Label>
              <Input
                id="current"
                type="number"
                step="0.1"
                min="0.1"
                max="10000"
                value={data.parameters.electrical.current.value}
                onChange={(e) => handleParameterChange('electrical', 'current', parseFloat(e.target.value) || 0)}
              />
              <p className="text-xs text-muted-foreground">
                范围: 0.1 - 10000 mA
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="upperVoltage">上限电压 (mV) *</Label>
              <Input
                id="upperVoltage"
                type="number"
                step="1"
                min="0"
                max="5000"
                value={data.parameters.electrical.upperVoltage.value}
                onChange={(e) => handleParameterChange('electrical', 'upperVoltage', parseFloat(e.target.value) || 0)}
                className={errors.some(e => e.includes('电压')) ? 'border-red-500' : ''}
              />
              <p className="text-xs text-muted-foreground">
                范围: 0 - 5000 mV
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="lowerVoltage">下限电压 (mV) *</Label>
              <Input
                id="lowerVoltage"
                type="number"
                step="1"
                min="0"
                max="5000"
                value={data.parameters.electrical.lowerVoltage.value}
                onChange={(e) => handleParameterChange('electrical', 'lowerVoltage', parseFloat(e.target.value) || 0)}
                className={errors.some(e => e.includes('电压')) ? 'border-red-500' : ''}
              />
              <p className="text-xs text-muted-foreground">
                范围: 0 - 5000 mV
              </p>
            </div>
          </div>

          {/* 电压范围提示 */}
          {data.parameters.electrical.upperVoltage.value > 0 && data.parameters.electrical.lowerVoltage.value > 0 && (
            <div className="p-3 bg-gray-50 border rounded-md">
              <p className="text-sm">
                <strong>电压范围：</strong> 
                {data.parameters.electrical.lowerVoltage.value} - {data.parameters.electrical.upperVoltage.value} mV
                <span className="ml-2">
                  (差值: {data.parameters.electrical.upperVoltage.value - data.parameters.electrical.lowerVoltage.value} mV)
                </span>
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* 时间和终止条件 */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">时间和终止条件</CardTitle>
          <CardDescription>设置工步的运行时间和终止条件</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="space-y-2">
              <Label htmlFor="duration">持续时间 *</Label>
              <Input
                id="duration"
                placeholder="00:01:00.000"
                value={data.parameters.time.duration.value}
                onChange={(e) => onChange({
                  parameters: {
                    ...data.parameters,
                    time: {
                      duration: { ...data.parameters.time.duration, value: e.target.value }
                    }
                  }
                })}
              />
              <p className="text-xs text-muted-foreground">
                格式: hh:mm:ss.ms
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="capacity">容量 (mAh)</Label>
              <Input
                id="capacity"
                type="number"
                step="1"
                min="0"
                max="50000"
                value={data.parameters.termination.capacity.value}
                onChange={(e) => handleParameterChange('termination', 'capacity', parseFloat(e.target.value) || 0)}
              />
              <p className="text-xs text-muted-foreground">
                范围: 0 - 50000 mAh
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="upperTemperature">温度上限 (℃)</Label>
              <Input
                id="upperTemperature"
                type="number"
                step="0.1"
                min="-40"
                max="100"
                value={data.parameters.termination.upperTemperature.value}
                onChange={(e) => handleParameterChange('termination', 'upperTemperature', parseFloat(e.target.value) || 0)}
                className={errors.some(e => e.includes('温度')) ? 'border-red-500' : ''}
              />
              <p className="text-xs text-muted-foreground">
                范围: -40 - 100 ℃
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="lowerTemperature">温度下限 (℃)</Label>
              <Input
                id="lowerTemperature"
                type="number"
                step="0.1"
                min="-40"
                max="100"
                value={data.parameters.termination.lowerTemperature.value}
                onChange={(e) => handleParameterChange('termination', 'lowerTemperature', parseFloat(e.target.value) || 0)}
                className={errors.some(e => e.includes('温度')) ? 'border-red-500' : ''}
              />
              <p className="text-xs text-muted-foreground">
                范围: -40 - 100 ℃
              </p>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="power">功率 (mW)</Label>
              <Input
                id="power"
                type="number"
                step="1"
                min="0"
                max="10000"
                value={data.parameters.termination.power.value}
                onChange={(e) => handleParameterChange('termination', 'power', parseFloat(e.target.value) || 0)}
              />
              <p className="text-xs text-muted-foreground">
                范围: 0 - 10000 mW
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="samplingInterval">采样间隔 (s)</Label>
              <Input
                id="samplingInterval"
                type="number"
                step="1"
                min="1"
                max="3600"
                value={data.parameters.sampling.samplingInterval.value}
                onChange={(e) => handleParameterChange('sampling', 'samplingInterval', parseFloat(e.target.value) || 0)}
              />
              <p className="text-xs text-muted-foreground">
                范围: 1 - 3600 s
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 备注 */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">备注信息</CardTitle>
          <CardDescription>添加工步的说明和备注</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <Label htmlFor="notes">备注</Label>
            <Textarea
              id="notes"
              placeholder="请输入备注信息..."
              className="min-h-[80px]"
              value={data.notes || ''}
              onChange={(e) => handleBasicInfoChange('notes', e.target.value)}
            />
            <p className="text-xs text-muted-foreground">
              最多500字符，当前: {(data.notes || '').length}/500
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

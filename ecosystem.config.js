module.exports = {
  apps: [
    {
      name: 'tianlang-pdcs',
      script: 'npm',
      args: 'start',
      cwd: './',
      instances: 1,
      autorestart: true,
      watch: false,
      max_memory_restart: '1G',
      env: {
        NODE_ENV: 'production',
        PORT: 3000
      },
      env_production: {
        NODE_ENV: 'production',
        PORT: 3000
      },
      log_date_format: 'YYYY-MM-DD HH:mm Z',
      error_file: './logs/err.log',
      out_file: './logs/out.log',
      log_file: './logs/combined.log',
      time: true,
      // 健康检查
      health_check_url: 'http://localhost:3000',
      health_check_grace_period: 3000,
      // 重启策略
      min_uptime: '10s',
      max_restarts: 10,
      // 集群模式（可选）
      // instances: 'max',
      // exec_mode: 'cluster'
    }
  ],

  deploy: {
    production: {
      user: 'deploy',
      host: 'your-server.com',
      ref: 'origin/main',
      repo: '**************:your-username/tianlang-pdcs.git',
      path: '/var/www/tianlang-pdcs',
      'pre-deploy-local': '',
      'post-deploy': 'npm install && npm run build && pm2 reload ecosystem.config.js --env production',
      'pre-setup': ''
    }
  }
};

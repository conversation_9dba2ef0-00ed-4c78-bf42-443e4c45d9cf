# 朗天PDCS - 大型工业软件前端系统

一个基于现代化技术栈构建的工业过程数据采集与监视控制系统（PDCS）前端应用。

## 🚀 技术栈

### 核心框架
- **Next.js 15** - 使用 App Router 的 React 全栈框架
- **TypeScript** - 类型安全的 JavaScript 超集
- **Tailwind CSS v4** - 基于 Rust 引擎的现代化 CSS 框架

### UI 组件库
- **Shadcn UI** - 高质量的 React 组件库
- **Lucide React** - 美观的图标库
- **Recharts** - 数据可视化图表库

### 国际化
- **next-intl** - Next.js App Router 官方推荐的国际化解决方案
- 支持中文和英文双语切换

### 状态管理与数据获取
- **Zustand** - 轻量级状态管理库
- **TanStack Query (React Query)** - 强大的数据获取和缓存库
- **React Hook Form + Zod** - 表单管理和验证

## 🛠️ 开发环境设置

### 前置要求
- Node.js 18+
- pnpm (推荐) 或 npm/yarn

### 安装依赖
```bash
pnpm install
```

### 启动开发服务器
```bash
pnpm dev
```

访问 [http://localhost:3000](http://localhost:3000) 查看应用。

### 构建生产版本
```bash
pnpm build
```

### 启动生产服务器
```bash
pnpm start
```

## 🌟 主要功能

### 已实现功能
- ✅ 响应式布局设计
- ✅ 深色/浅色主题切换
- ✅ 中英文国际化支持
- ✅ 侧边栏导航
- ✅ 设备管理界面
- ✅ 报警管理界面
- ✅ 实时数据展示卡片
- ✅ 数据表格组件
- ✅ 状态徽章和图标

### 计划功能
- 🔄 实时数据监控
- 🔄 数据图表可视化
- 🔄 用户认证和权限管理
- 🔄 设备控制操作
- 🔄 报表生成和导出
- 🔄 系统设置和配置
- 🔄 WebSocket 实时通信
- 🔄 MQTT 设备通信

## 🎨 UI 组件

项目使用 Shadcn UI 组件库，已集成的组件包括：
- Button, Card, Input, Label
- Select, Textarea, Badge, Table
- Dropdown Menu
- 主题切换组件
- 语言切换组件

添加新组件：
```bash
pnpm dlx shadcn@latest add [component-name]
```

## 🌍 国际化

支持的语言：
- 🇨🇳 中文 (zh)
- 🇺🇸 English (en)

添加新的翻译：
1. 在 `messages/` 目录下编辑对应的 JSON 文件
2. 在组件中使用 `useTranslations` Hook

```tsx
import { useTranslations } from 'next-intl';

function MyComponent() {
  const t = useTranslations('common');
  return <h1>{t('welcome')}</h1>;
}
```

## 📊 状态管理

使用 Zustand 管理全局 UI 状态：
- 主题设置
- 侧边栏状态
- 语言设置
- 全局通知
- 加载状态

```tsx
import { useUIStore } from '@/lib/stores/ui-store';

function MyComponent() {
  const { theme, setTheme } = useUIStore();
  // ...
}
```

---

**朗天PDCS** - 现代化工业控制系统前端解决方案

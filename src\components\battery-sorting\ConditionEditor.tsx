'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { 
  Code, 
  Plus, 
  Check, 
  AlertCircle,
  Info,
  Parentheses
} from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { SortingVariable, ComparisonOperator, LogicalOperator } from '@/lib/types';
import { useTranslations } from 'next-intl';

interface ConditionEditorProps {
  variables: SortingVariable[];
  condition: string;
  onConditionChange: (condition: string) => void;
}

export function ConditionEditor({
  variables,
  condition,
  onConditionChange
}: ConditionEditorProps) {
  const t = useTranslations('batterySorting.conditionEditor');
  const [selectedVariable, setSelectedVariable] = useState<string>('');
  const [selectedOperator, setSelectedOperator] = useState<ComparisonOperator>(ComparisonOperator.GREATER_THAN);
  const [comparisonValue, setComparisonValue] = useState<string>('');
  const [validationError, setValidationError] = useState<string>('');

  // 比较运算符选项
  const comparisonOperators = [
    { value: ComparisonOperator.GREATER_THAN, label: '大于 (>)' },
    { value: ComparisonOperator.LESS_THAN, label: '小于 (<)' },
    { value: ComparisonOperator.GREATER_EQUAL, label: '大于等于 (>=)' },
    { value: ComparisonOperator.LESS_EQUAL, label: '小于等于 (<=)' },
    { value: ComparisonOperator.EQUAL, label: '等于 (==)' },
    { value: ComparisonOperator.NOT_EQUAL, label: '不等于 (!=)' }
  ];

  // 逻辑运算符选项
  const logicalOperators = [
    { value: LogicalOperator.AND, label: 'AND (且)' },
    { value: LogicalOperator.OR, label: 'OR (或)' },
    { value: LogicalOperator.NOT, label: 'NOT (非)' }
  ];

  // 添加比较条件
  const addComparison = () => {
    if (!selectedVariable || !comparisonValue.trim()) return;

    const variable = variables.find(v => v.id === selectedVariable);
    if (!variable) return;

    const comparison = `${variable.name} ${selectedOperator} ${comparisonValue}`;
    
    if (condition.trim()) {
      onConditionChange(`${condition} AND ${comparison}`);
    } else {
      onConditionChange(comparison);
    }

    // 重置选择
    setSelectedVariable('');
    setComparisonValue('');
  };

  // 添加逻辑运算符
  const addLogicalOperator = (operator: LogicalOperator) => {
    if (!condition.trim()) return;
    
    if (operator === LogicalOperator.NOT) {
      onConditionChange(`NOT (${condition})`);
    } else {
      onConditionChange(`${condition} ${operator} `);
    }
  };

  // 添加括号
  const addParentheses = () => {
    if (!condition.trim()) {
      onConditionChange('()');
    } else {
      onConditionChange(`(${condition})`);
    }
  };

  // 验证条件表达式
  const validateCondition = (expr: string): string => {
    if (!expr.trim()) return '';

    try {
      // 基本语法检查
      const brackets = expr.match(/[()]/g) || [];
      let openCount = 0;
      for (const bracket of brackets) {
        if (bracket === '(') openCount++;
        else openCount--;
        if (openCount < 0) return '括号不匹配';
      }
      if (openCount !== 0) return '括号不匹配';

      // 检查变量引用
      const variableNames = variables.map(v => v.name);
      const usedVariables = expr.match(/[a-zA-Z\u4e00-\u9fa5][a-zA-Z0-9\u4e00-\u9fa5]*/g) || [];
      const logicalKeywords = ['AND', 'OR', 'NOT'];
      
      for (const usedVar of usedVariables) {
        if (!logicalKeywords.includes(usedVar) && !variableNames.includes(usedVar)) {
          return `未定义的变量: ${usedVar}`;
        }
      }

      return '';
    } catch (error) {
      return '表达式语法错误';
    }
  };

  // 实时验证
  React.useEffect(() => {
    const error = validateCondition(condition);
    setValidationError(error);
  }, [condition, variables]);

  // 示例条件
  const exampleConditions = [
    '结束容量 > 3000',
    '结束容量 > 3000 AND 中值电压 > 3.7',
    '(结束容量 > 3000 OR 充电容量 > 2800) AND 效率 > 95',
    'NOT (温度 > 45)',
    '循环次数 >= 100 AND 容量保持率 > 80'
  ];

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Code className="h-5 w-5" />
          {t('title')}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* 快速构建工具 */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 p-4 bg-muted/50 rounded-lg">
          <div className="space-y-2">
            <label className="text-sm font-medium">{t('selectVariable')}</label>
            <Select value={selectedVariable} onValueChange={setSelectedVariable}>
              <SelectTrigger>
                <SelectValue placeholder={t('selectVariablePlaceholder')} />
              </SelectTrigger>
              <SelectContent>
                {variables.map((variable) => (
                  <SelectItem key={variable.id} value={variable.id}>
                    {variable.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">{t('selectOperator')}</label>
            <Select value={selectedOperator} onValueChange={(value: ComparisonOperator) => setSelectedOperator(value)}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {comparisonOperators.map((op) => (
                  <SelectItem key={op.value} value={op.value}>
                    {op.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">{t('comparisonValue')}</label>
            <Input
              type="number"
              value={comparisonValue}
              onChange={(e) => setComparisonValue(e.target.value)}
              placeholder={t('comparisonValuePlaceholder')}
            />
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">{t('actions')}</label>
            <Button 
              onClick={addComparison}
              disabled={!selectedVariable || !comparisonValue.trim()}
              className="w-full"
            >
              <Plus className="h-4 w-4 mr-2" />
              {t('addCondition')}
            </Button>
          </div>
        </div>

        {/* 逻辑运算符和括号 */}
        <div className="flex flex-wrap gap-2">
          <span className="text-sm font-medium self-center">{t('logicalOperators')}:</span>
          {logicalOperators.map((op) => (
            <Button
              key={op.value}
              variant="outline"
              size="sm"
              onClick={() => addLogicalOperator(op.value)}
              disabled={!condition.trim() && op.value !== LogicalOperator.NOT}
            >
              {op.label}
            </Button>
          ))}
          <Button
            variant="outline"
            size="sm"
            onClick={addParentheses}
          >
            <Parentheses className="h-4 w-4 mr-1" />
            {t('addParentheses')}
          </Button>
        </div>

        {/* 条件表达式编辑器 */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <label className="text-sm font-medium">{t('conditionExpression')}</label>
            <div className="flex items-center gap-2">
              {validationError ? (
                <Badge variant="destructive" className="text-xs">
                  <AlertCircle className="h-3 w-3 mr-1" />
                  {t('invalid')}
                </Badge>
              ) : condition.trim() ? (
                <Badge variant="default" className="text-xs">
                  <Check className="h-3 w-3 mr-1" />
                  {t('valid')}
                </Badge>
              ) : null}
            </div>
          </div>
          
          <Textarea
            value={condition}
            onChange={(e) => onConditionChange(e.target.value)}
            placeholder={t('conditionPlaceholder')}
            rows={4}
            className={validationError ? 'border-destructive' : ''}
          />
          
          {validationError && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{validationError}</AlertDescription>
            </Alert>
          )}
        </div>

        {/* 使用说明 */}
        <Alert>
          <Info className="h-4 w-4" />
          <AlertDescription>
            <div className="space-y-2">
              <p className="font-medium">{t('usageInstructions')}</p>
              <ul className="text-sm space-y-1 ml-4">
                <li>• {t('instruction1')}</li>
                <li>• {t('instruction2')}</li>
                <li>• {t('instruction3')}</li>
                <li>• {t('instruction4')}</li>
              </ul>
            </div>
          </AlertDescription>
        </Alert>

        {/* 示例条件 */}
        <div className="space-y-2">
          <label className="text-sm font-medium">{t('exampleConditions')}</label>
          <div className="flex flex-wrap gap-2">
            {exampleConditions.map((example, index) => (
              <Button
                key={index}
                variant="ghost"
                size="sm"
                className="text-xs h-auto p-2 text-left"
                onClick={() => onConditionChange(example)}
              >
                <code>{example}</code>
              </Button>
            ))}
          </div>
        </div>

        {/* 清空按钮 */}
        {condition.trim() && (
          <div className="flex justify-end">
            <Button
              variant="outline"
              size="sm"
              onClick={() => onConditionChange('')}
            >
              {t('clearCondition')}
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

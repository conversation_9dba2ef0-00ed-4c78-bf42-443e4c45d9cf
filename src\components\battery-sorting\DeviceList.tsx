'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  Search, 
  Server, 
  Wifi, 
  WifiOff, 
  AlertTriangle,
  ChevronRight,
  ChevronDown,
  Zap
} from 'lucide-react';
import { BatteryDevice, BatteryChannel } from '@/lib/types';
import { useTranslations } from 'next-intl';

interface DeviceListProps {
  devices: BatteryDevice[];
  selectedDeviceId?: string;
  selectedChannelId?: string;
  onDeviceSelect: (deviceId: string) => void;
  onChannelSelect: (channelId: string, deviceId: string) => void;
}

export function DeviceList({
  devices,
  selectedDeviceId,
  selectedChannelId,
  onDeviceSelect,
  onChannelSelect
}: DeviceListProps) {
  const t = useTranslations('batterySorting.deviceList');
  const [searchTerm, setSearchTerm] = useState('');
  const [expandedDevices, setExpandedDevices] = useState<Set<string>>(new Set());

  // 过滤设备
  const filteredDevices = devices.filter(device =>
    device.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    device.location.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // 切换设备展开状态
  const toggleDeviceExpanded = (deviceId: string) => {
    const newExpanded = new Set(expandedDevices);
    if (newExpanded.has(deviceId)) {
      newExpanded.delete(deviceId);
    } else {
      newExpanded.add(deviceId);
    }
    setExpandedDevices(newExpanded);
  };

  // 获取设备状态图标
  const getDeviceStatusIcon = (status: BatteryDevice['status']) => {
    switch (status) {
      case 'online':
        return <Wifi className="h-4 w-4 text-green-500" />;
      case 'offline':
        return <WifiOff className="h-4 w-4 text-gray-500" />;
      case 'error':
        return <AlertTriangle className="h-4 w-4 text-red-500" />;
      default:
        return <Server className="h-4 w-4 text-gray-500" />;
    }
  };

  // 获取通道状态颜色
  const getChannelStatusColor = (status: BatteryChannel['status']) => {
    switch (status) {
      case 'idle':
        return 'bg-gray-500';
      case 'testing':
        return 'bg-blue-500';
      case 'completed':
        return 'bg-green-500';
      case 'error':
        return 'bg-red-500';
      default:
        return 'bg-gray-500';
    }
  };

  // 获取通道状态文本
  const getChannelStatusText = (status: BatteryChannel['status']) => {
    switch (status) {
      case 'idle':
        return t('channelStatus.idle');
      case 'testing':
        return t('channelStatus.testing');
      case 'completed':
        return t('channelStatus.completed');
      case 'error':
        return t('channelStatus.error');
      default:
        return t('channelStatus.unknown');
    }
  };

  // 计算设备统计
  const getDeviceStats = (device: BatteryDevice) => {
    const totalChannels = device.channels.length;
    const activeChannels = device.channels.filter(c => c.status === 'testing').length;
    const completedChannels = device.channels.filter(c => c.status === 'completed').length;
    const errorChannels = device.channels.filter(c => c.status === 'error').length;

    return {
      total: totalChannels,
      active: activeChannels,
      completed: completedChannels,
      error: errorChannels
    };
  };

  return (
    <Card className="h-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Server className="h-5 w-5" />
          {t('title')}
          <Badge variant="secondary">{filteredDevices.length}</Badge>
        </CardTitle>
        
        {/* 搜索框 */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder={t('searchPlaceholder')}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
      </CardHeader>
      
      <CardContent className="p-0">
        <ScrollArea className="h-[600px]">
          <div className="space-y-2 p-4">
            {filteredDevices.length === 0 ? (
              <div className="text-center text-muted-foreground py-8">
                {searchTerm ? t('noSearchResults') : t('noDevices')}
              </div>
            ) : (
              filteredDevices.map((device) => {
                const stats = getDeviceStats(device);
                const isExpanded = expandedDevices.has(device.id);
                const isSelected = selectedDeviceId === device.id;

                return (
                  <div key={device.id} className="space-y-2">
                    {/* 设备头部 */}
                    <div
                      className={`p-3 rounded-lg border cursor-pointer transition-colors ${
                        isSelected 
                          ? 'border-primary bg-primary/5' 
                          : 'border-border hover:bg-muted/50'
                      }`}
                      onClick={() => {
                        onDeviceSelect(device.id);
                        toggleDeviceExpanded(device.id);
                      }}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-6 w-6 p-0"
                            onClick={(e) => {
                              e.stopPropagation();
                              toggleDeviceExpanded(device.id);
                            }}
                          >
                            {isExpanded ? (
                              <ChevronDown className="h-4 w-4" />
                            ) : (
                              <ChevronRight className="h-4 w-4" />
                            )}
                          </Button>
                          
                          {getDeviceStatusIcon(device.status)}
                          
                          <div>
                            <div className="font-medium">{device.name}</div>
                            <div className="text-sm text-muted-foreground">
                              {device.location} • {device.type}
                            </div>
                          </div>
                        </div>

                        <div className="flex items-center gap-2">
                          <Badge variant="outline" className="text-xs">
                            {stats.total} {t('channels')}
                          </Badge>
                          {stats.active > 0 && (
                            <Badge variant="default" className="text-xs">
                              {stats.active} {t('active')}
                            </Badge>
                          )}
                          {stats.error > 0 && (
                            <Badge variant="destructive" className="text-xs">
                              {stats.error} {t('error')}
                            </Badge>
                          )}
                        </div>
                      </div>

                      {/* 设备状态统计 */}
                      <div className="mt-2 flex gap-4 text-xs text-muted-foreground">
                        <span>{t('lastUpdate')}: {device.lastUpdate.toLocaleString()}</span>
                      </div>
                    </div>

                    {/* 通道列表 */}
                    {isExpanded && (
                      <div className="ml-6 space-y-1">
                        {device.channels.map((channel) => {
                          const isChannelSelected = selectedChannelId === channel.id;
                          
                          return (
                            <div
                              key={channel.id}
                              className={`p-2 rounded border cursor-pointer transition-colors ${
                                isChannelSelected
                                  ? 'border-primary bg-primary/5'
                                  : 'border-border hover:bg-muted/30'
                              }`}
                              onClick={() => onChannelSelect(channel.id, device.id)}
                            >
                              <div className="flex items-center justify-between">
                                <div className="flex items-center gap-2">
                                  <Zap className="h-3 w-3 text-muted-foreground" />
                                  <span className="text-sm font-medium">
                                    {t('channel')} {channel.channelNumber}
                                  </span>
                                  {channel.batteryId && (
                                    <Badge variant="outline" className="text-xs">
                                      {channel.batteryId}
                                    </Badge>
                                  )}
                                </div>

                                <div className="flex items-center gap-2">
                                  <div
                                    className={`w-2 h-2 rounded-full ${getChannelStatusColor(channel.status)}`}
                                  />
                                  <span className="text-xs text-muted-foreground">
                                    {getChannelStatusText(channel.status)}
                                  </span>
                                </div>
                              </div>

                              {/* 通道详细信息 */}
                              {(channel.currentStep || channel.currentCycle) && (
                                <div className="mt-1 flex gap-4 text-xs text-muted-foreground">
                                  {channel.currentStep && (
                                    <span>{t('currentStep')}: {channel.currentStep}</span>
                                  )}
                                  {channel.currentCycle && (
                                    <span>{t('currentCycle')}: {channel.currentCycle}</span>
                                  )}
                                </div>
                              )}
                            </div>
                          );
                        })}
                      </div>
                    )}
                  </div>
                );
              })
            )}
          </div>
        </ScrollArea>
      </CardContent>
    </Card>
  );
}

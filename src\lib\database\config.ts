/**
 * 本地存储配置
 * 使用 localStorage 替代 SQLite 数据库
 */

// 存储键名
const STORAGE_KEYS = {
  PROCESSES: 'pdcs_processes',
  PROCESS_STEPS: 'pdcs_process_steps',
  PROCESS_EXECUTIONS: 'pdcs_process_executions',
  PROCESS_TEMPLATES: 'pdcs_process_templates',
  METADATA: 'pdcs_metadata'
} as const;

// 本地存储服务类
export class LocalStorageService {
  private static instance: LocalStorageService | null = null;

  private constructor() {
    this.initializeStorage();
  }

  static getInstance(): LocalStorageService {
    if (!LocalStorageService.instance) {
      LocalStorageService.instance = new LocalStorageService();
    }
    return LocalStorageService.instance;
  }

  private initializeStorage(): void {
    // 初始化各个表的存储
    Object.values(STORAGE_KEYS).forEach(key => {
      if (!localStorage.getItem(key)) {
        localStorage.setItem(key, JSON.stringify([]));
      }
    });

    // 初始化元数据
    if (!localStorage.getItem(STORAGE_KEYS.METADATA)) {
      localStorage.setItem(STORAGE_KEYS.METADATA, JSON.stringify({
        version: '1.0',
        createdAt: new Date().toISOString(),
        lastUpdated: new Date().toISOString()
      }));
    }
  }

  // 通用的 CRUD 操作
  insert<T extends Record<string, any>>(table: keyof typeof STORAGE_KEYS, data: T): T {
    const items = this.getAll<T>(table);
    const newItem = {
      ...data,
      id: data.id || this.generateId(),
      createdAt: data.createdAt || new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    items.push(newItem);
    localStorage.setItem(STORAGE_KEYS[table], JSON.stringify(items));
    this.updateMetadata();
    return newItem;
  }

  getAll<T>(table: keyof typeof STORAGE_KEYS): T[] {
    const data = localStorage.getItem(STORAGE_KEYS[table]);
    return data ? JSON.parse(data) : [];
  }

  getById<T>(table: keyof typeof STORAGE_KEYS, id: string): T | null {
    const items = this.getAll<T>(table);
    return items.find((item: any) => item.id === id) || null;
  }

  update<T extends Record<string, any>>(table: keyof typeof STORAGE_KEYS, id: string, data: Partial<T>): boolean {
    const items = this.getAll<T>(table);
    const index = items.findIndex((item: any) => item.id === id);

    if (index === -1) return false;

    items[index] = {
      ...items[index],
      ...data,
      updatedAt: new Date().toISOString()
    };

    localStorage.setItem(STORAGE_KEYS[table], JSON.stringify(items));
    this.updateMetadata();
    return true;
  }

  delete(table: keyof typeof STORAGE_KEYS, id: string): boolean {
    const items = this.getAll(table);
    const filteredItems = items.filter((item: any) => item.id !== id);

    if (filteredItems.length === items.length) return false;

    localStorage.setItem(STORAGE_KEYS[table], JSON.stringify(filteredItems));
    this.updateMetadata();
    return true;
  }

  // 查询方法
  query<T>(table: keyof typeof STORAGE_KEYS, predicate: (item: T) => boolean): T[] {
    const items = this.getAll<T>(table);
    return items.filter(predicate);
  }

  // 生成唯一ID
  private generateId(): string {
    return `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // 更新元数据
  private updateMetadata(): void {
    const metadata = {
      version: '1.0',
      lastUpdated: new Date().toISOString()
    };
    localStorage.setItem(STORAGE_KEYS.METADATA, JSON.stringify(metadata));
  }

  // 清空所有数据
  clear(): void {
    Object.values(STORAGE_KEYS).forEach(key => {
      localStorage.removeItem(key);
    });
    this.initializeStorage();
  }

  // 导出数据
  exportData(): string {
    const data: Record<string, any> = {};
    Object.entries(STORAGE_KEYS).forEach(([key, storageKey]) => {
      data[key] = this.getAll(storageKey as keyof typeof STORAGE_KEYS);
    });
    return JSON.stringify(data, null, 2);
  }

  // 导入数据
  importData(jsonData: string): boolean {
    try {
      const data = JSON.parse(jsonData);
      Object.entries(STORAGE_KEYS).forEach(([key, storageKey]) => {
        if (data[key]) {
          localStorage.setItem(storageKey, JSON.stringify(data[key]));
        }
      });
      this.updateMetadata();
      return true;
    } catch (error) {
      console.error('导入数据失败:', error);
      return false;
    }
  }
}

// 便利方法
export function getLocalStorage(): LocalStorageService {
  return LocalStorageService.getInstance();
}

// 初始化存储
export function initializeStorage(): LocalStorageService {
  return LocalStorageService.getInstance();
}

// 存储健康检查
export function checkStorageHealth(): boolean {
  try {
    const testKey = 'pdcs_health_check';
    const testValue = 'test';
    localStorage.setItem(testKey, testValue);
    const result = localStorage.getItem(testKey);
    localStorage.removeItem(testKey);
    return result === testValue;
  } catch (error) {
    console.error('存储健康检查失败:', error);
    return false;
  }
}

// 导出存储键名供外部使用
export { STORAGE_KEYS };

'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Plus, Trash2, Edit } from 'lucide-react';
import { SortingVariable, BatteryParameterType } from '@/lib/types';
import { getBatteryParameterTypes } from '@/lib/utils/batterySorting';
import { useTranslations } from 'next-intl';

interface VariableDefinitionPanelProps {
  variables: SortingVariable[];
  onVariablesChange: (variables: SortingVariable[]) => void;
}

export function VariableDefinitionPanel({
  variables,
  onVariablesChange
}: VariableDefinitionPanelProps) {
  const t = useTranslations('batterySorting.variables');
  const tDataTypes = useTranslations('batterySorting.dataTypes');
  const [editingVariable, setEditingVariable] = useState<SortingVariable | null>(null);
  const [isAddingNew, setIsAddingNew] = useState(false);

  const parameterTypes = getBatteryParameterTypes();

  // 创建新变量的默认值
  const createNewVariable = (): SortingVariable => ({
    id: generateId(),
    name: '',
    parameterType: BatteryParameterType.DISCHARGE_CAPACITY,
    stepNumber: undefined,
    cycleNumber: undefined,
    description: ''
  });

  const handleAddVariable = () => {
    setEditingVariable(createNewVariable());
    setIsAddingNew(true);
  };

  const handleEditVariable = (variable: SortingVariable) => {
    setEditingVariable({ ...variable });
    setIsAddingNew(false);
  };

  const handleSaveVariable = () => {
    if (!editingVariable || !editingVariable.name.trim()) return;

    if (isAddingNew) {
      onVariablesChange([...variables, editingVariable]);
    } else {
      onVariablesChange(
        variables.map(v => v.id === editingVariable.id ? editingVariable : v)
      );
    }

    setEditingVariable(null);
    setIsAddingNew(false);
  };

  const handleDeleteVariable = (variableId: string) => {
    onVariablesChange(variables.filter(v => v.id !== variableId));
  };

  const handleCancelEdit = () => {
    setEditingVariable(null);
    setIsAddingNew(false);
  };

  const getParameterTypeLabel = (type: BatteryParameterType) => {
    const paramType = parameterTypes.find(p => p.value === type);
    if (!paramType) return type;

    // 使用翻译键获取标签
    const translatedLabel = tDataTypes(type);
    return `${translatedLabel}${paramType.unit ? ` (${paramType.unit})` : ''}`;
  };

  const getParameterTypeCategory = (type: BatteryParameterType) => {
    const paramType = parameterTypes.find(p => p.value === type);
    return paramType?.category || '其他';
  };

  const generateId = () => Date.now().toString(36) + Math.random().toString(36).substr(2);

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            {t('title')}
            <Badge variant="secondary">{variables.length}</Badge>
          </CardTitle>
          <Button onClick={handleAddVariable} size="sm">
            <Plus className="h-4 w-4 mr-2" />
            {t('addVariable')}
          </Button>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* 变量列表 */}
        <div className="border rounded-lg">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>{t('variableName')}</TableHead>
                <TableHead>{t('dataType')}</TableHead>
                <TableHead>{t('stepNumber')}</TableHead>
                <TableHead>{t('cycleNumber')}</TableHead>
                <TableHead className="w-[100px]">{t('actions')}</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {variables.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={5} className="text-center text-muted-foreground py-8">
                    {t('noVariables')}
                  </TableCell>
                </TableRow>
              ) : (
                variables.map((variable) => (
                  <TableRow key={variable.id}>
                    <TableCell className="font-medium">{variable.name}</TableCell>
                    <TableCell>
                      <div className="flex flex-col gap-1">
                        <span className="text-sm">{getParameterTypeLabel(variable.parameterType)}</span>
                        <Badge variant="outline" className="text-xs w-fit">
                          {getParameterTypeCategory(variable.parameterType)}
                        </Badge>
                      </div>
                    </TableCell>
                    <TableCell>
                      {variable.stepNumber !== undefined ? (
                        <Badge variant="secondary">工步 {variable.stepNumber}</Badge>
                      ) : (
                        <span className="text-muted-foreground">-</span>
                      )}
                    </TableCell>
                    <TableCell>
                      {variable.cycleNumber !== undefined ? (
                        <Badge variant="secondary">循环 {variable.cycleNumber}</Badge>
                      ) : (
                        <span className="text-muted-foreground">-</span>
                      )}
                    </TableCell>
                    <TableCell>
                      <div className="flex gap-1">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleEditVariable(variable)}
                        >
                          <Edit className="h-3 w-3" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDeleteVariable(variable.id)}
                        >
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>

        {/* 编辑表单 */}
        {editingVariable && (
          <Card className="border-2 border-primary">
            <CardHeader>
              <CardTitle className="text-lg">
                {isAddingNew ? t('addNewVariable') : t('editVariable')}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="variableName">{t('variableName')}</Label>
                  <Input
                    id="variableName"
                    value={editingVariable.name}
                    onChange={(e) => setEditingVariable({
                      ...editingVariable,
                      name: e.target.value
                    })}
                    placeholder={t('variableNamePlaceholder')}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="parameterType">{t('dataType')}</Label>
                  <Select
                    value={editingVariable.parameterType}
                    onValueChange={(value: BatteryParameterType) => 
                      setEditingVariable({
                        ...editingVariable,
                        parameterType: value
                      })
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {Object.entries(
                        parameterTypes.reduce((acc, param) => {
                          if (!acc[param.category]) acc[param.category] = [];
                          acc[param.category].push(param);
                          return acc;
                        }, {} as Record<string, typeof parameterTypes>)
                      ).map(([category, params]) => (
                        <div key={category}>
                          <div className="px-2 py-1 text-sm font-medium text-muted-foreground">
                            {category}
                          </div>
                          {params.map((param) => (
                            <SelectItem key={param.value} value={param.value}>
                              {tDataTypes(param.value)}{param.unit && ` (${param.unit})`}
                            </SelectItem>
                          ))}
                        </div>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="stepNumber">{t('stepNumber')}</Label>
                  <Input
                    id="stepNumber"
                    type="number"
                    value={editingVariable.stepNumber || ''}
                    onChange={(e) => setEditingVariable({
                      ...editingVariable,
                      stepNumber: e.target.value ? parseInt(e.target.value) : undefined
                    })}
                    placeholder={t('stepNumberPlaceholder')}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="cycleNumber">{t('cycleNumber')}</Label>
                  <Input
                    id="cycleNumber"
                    type="number"
                    value={editingVariable.cycleNumber || ''}
                    onChange={(e) => setEditingVariable({
                      ...editingVariable,
                      cycleNumber: e.target.value ? parseInt(e.target.value) : undefined
                    })}
                    placeholder={t('cycleNumberPlaceholder')}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">{t('description')}</Label>
                <Input
                  id="description"
                  value={editingVariable.description || ''}
                  onChange={(e) => setEditingVariable({
                    ...editingVariable,
                    description: e.target.value
                  })}
                  placeholder={t('descriptionPlaceholder')}
                />
              </div>

              <div className="flex gap-2 justify-end">
                <Button variant="outline" onClick={handleCancelEdit}>
                  {t('cancel')}
                </Button>
                <Button 
                  onClick={handleSaveVariable}
                  disabled={!editingVariable.name.trim()}
                >
                  {isAddingNew ? t('add') : t('save')}
                </Button>
              </div>
            </CardContent>
          </Card>
        )}
      </CardContent>
    </Card>
  );
}

import React from 'react';
import { cn } from '@/lib/utils';
import { Card } from '@/components/ui/card';

interface InteractiveCardProps extends React.ComponentProps<"div"> {
  children: React.ReactNode;
  isActive?: boolean;
  isDisabled?: boolean;
  variant?: 'default' | 'elevated' | 'minimal';
  hoverEffect?: 'lift' | 'glow' | 'border' | 'scale';
  activeEffect?: 'border' | 'background' | 'glow';
}

export const InteractiveCard = React.forwardRef<HTMLDivElement, InteractiveCardProps>(
  ({ 
    children, 
    className, 
    isActive = false, 
    isDisabled = false,
    variant = 'default',
    hoverEffect = 'lift',
    activeEffect = 'border',
    ...props 
  }, ref) => {
    const baseClasses = cn(
      // 基础样式
      'transition-all duration-300 ease-out cursor-pointer transform-gpu',

      // 变体样式
      {
        'shadow-sm border': variant === 'default',
        'shadow-lg border': variant === 'elevated',
        'shadow-none border-0': variant === 'minimal',
      },

      // 禁用状态
      {
        'opacity-50 cursor-not-allowed pointer-events-none grayscale': isDisabled,
      },

      // Hover效果 - 统一的hover样式
      !isDisabled && {
        // 抬升效果
        'hover:shadow-lg hover:-translate-y-1 hover:border-blue-500/50': hoverEffect === 'lift',
        // 发光效果
        'hover:shadow-xl hover:shadow-blue-500/20 hover:border-blue-500': hoverEffect === 'glow',
        // 边框效果
        'hover:border-blue-500 hover:shadow-md hover:bg-accent/30': hoverEffect === 'border',
        // 缩放效果
        'hover:scale-[1.02] hover:shadow-md hover:border-blue-500/50': hoverEffect === 'scale',
      },

      // 激活状态
      isActive && {
        // 边框激活
        'border-blue-500 bg-blue-50 dark:bg-blue-950 shadow-md ring-1 ring-blue-500/20': activeEffect === 'border',
        // 背景激活
        'bg-primary/10 border-primary shadow-md': activeEffect === 'background',
        // 发光激活
        'shadow-lg shadow-blue-500/30 border-blue-500 ring-1 ring-blue-500/20': activeEffect === 'glow',
      },

      className
    );

    return (
      <Card ref={ref} className={baseClasses} {...props}>
        {children}
      </Card>
    );
  }
);

InteractiveCard.displayName = 'InteractiveCard';

// 列表项组件
interface InteractiveListItemProps {
  children: React.ReactNode;
  className?: string;
  isActive?: boolean;
  isDisabled?: boolean;
  onClick?: () => void;
  variant?: 'default' | 'compact' | 'spacious';
}

export const InteractiveListItem = React.forwardRef<HTMLDivElement, InteractiveListItemProps>(
  ({ 
    children, 
    className, 
    isActive = false, 
    isDisabled = false,
    onClick,
    variant = 'default',
    ...props 
  }, ref) => {
    const baseClasses = cn(
      // 基础样式
      'transition-all duration-300 ease-out cursor-pointer rounded-lg border transform-gpu',

      // 变体样式
      {
        'p-4': variant === 'default',
        'p-2': variant === 'compact',
        'p-6': variant === 'spacious',
      },

      // 禁用状态
      {
        'opacity-50 cursor-not-allowed pointer-events-none grayscale': isDisabled,
      },

      // Hover效果 - 统一的hover样式
      !isDisabled && 'hover:border-blue-500 hover:shadow-md hover:bg-accent/30 hover:-translate-y-0.5',

      // 激活状态
      isActive && 'border-blue-500 bg-blue-50 dark:bg-blue-950 shadow-md ring-1 ring-blue-500/20',

      className
    );

    return (
      <div 
        ref={ref} 
        className={baseClasses} 
        onClick={!isDisabled ? onClick : undefined}
        {...props}
      >
        {children}
      </div>
    );
  }
);

InteractiveListItem.displayName = 'InteractiveListItem';

// 通道卡片专用组件
interface ChannelCardProps {
  children: React.ReactNode;
  className?: string;
  isDisabled?: boolean;
  onClick?: () => void;
}

export const ChannelCard = React.forwardRef<HTMLDivElement, ChannelCardProps>(
  ({ children, className, isDisabled = false, onClick, ...props }, ref) => {
    return (
      <InteractiveCard
        ref={ref}
        className={className}
        isDisabled={isDisabled}
        variant="default"
        hoverEffect="border"
        onClick={onClick}
        {...props}
      >
        {children}
      </InteractiveCard>
    );
  }
);

ChannelCard.displayName = 'ChannelCard';

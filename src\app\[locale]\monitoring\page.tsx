'use client';

import { useState, useEffect, useRef, useCallback } from 'react';
import { useTranslations } from 'next-intl';
import { useSearchParams } from 'next/navigation';
import { useDeviceStore } from '@/stores/deviceStore';
import { DeviceStatus } from '@/lib/types';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { InteractiveCard, InteractiveListItem } from '@/components/ui/interactive-card';
import { EnhancedChannelCard } from '@/components/channel/enhanced-channel-card';
import { scrollElementToCenter } from '@/utils/scrollAnimation';

import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Slider } from '@/components/ui/slider';
import { Label } from '@/components/ui/label';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import {
  Monitor, Activity, Thermometer, Gauge, Settings, List, LayoutGrid,
  Send, RotateCcw, Pause, Play, Eye, AlertCircle, RefreshCw, Target,
  ThermometerSun, Filter, CheckCircle, ArrowRight, ArrowDown, Archive,
  TrendingUp, FileText, Clock, ChevronLeft, ChevronRight, Sliders
} from 'lucide-react';
import { DeviceSettingsDialog } from '@/components/device/DeviceSettingsDialog';
import { DeviceManagementDialog } from '@/components/device/DeviceManagementDialog';



export default function MonitoringPage() {
  const tNav = useTranslations('navigation');
  const tCommon = useTranslations('common');
  const searchParams = useSearchParams();

  // 全局状态管理
  const {
    devices,
    channels,
    selectedDevice,
    setSelectedDevice,
    getChannelsByDevice,
    updateChannelStatus,
    simulationSettings,
    updateSimulationSettings,
    initializeData,
    isSimulationRunning,
    startSimulation,
    stopSimulation
  } = useDeviceStore();

  // 本地状态管理
  const [currentView, setCurrentView] = useState<'devices' | 'channels'>('devices');
  const [viewMode, setViewMode] = useState<'list' | 'cards'>('cards');
  const [selectedDeviceForChannels, setSelectedDeviceForChannels] = useState<string | null>(null);
  const [selectedChannel, setSelectedChannel] = useState<number | null>(null);

  // 设备设置对话框状态
  const [deviceSettingsOpen, setDeviceSettingsOpen] = useState(false);
  const [selectedDeviceForSettings, setSelectedDeviceForSettings] = useState<any>(null);

  // 设备管理对话框状态
  const [deviceManagementOpen, setDeviceManagementOpen] = useState(false);

  // 引用管理
  const deviceListRef = useRef<HTMLDivElement>(null);
  const deviceRefs = useRef<Map<string, HTMLDivElement>>(new Map());

  // 获取当前设备的通道数据
  const currentChannels = selectedDeviceForChannels ? getChannelsByDevice(selectedDeviceForChannels) : [];

  // 设备选择处理函数
  const handleDeviceSelect = useCallback(async (deviceName: string, skipAnimation: boolean = false) => {
    if (deviceName === selectedDeviceForChannels) return;

    setSelectedDeviceForChannels(deviceName);

    // 如果跳过动画，直接返回
    if (skipAnimation) return;

    // 滚动动画
    const deviceElement = deviceRefs.current.get(deviceName);
    const listContainer = deviceListRef.current;

    if (deviceElement && listContainer) {
      try {
        // 滚动到中心位置
        await scrollElementToCenter(deviceElement, listContainer, {
          duration: 600,
          offset: 0
        });
      } catch (error) {
        console.warn('设备滚动动画失败:', error);
      }
    }
  }, [selectedDeviceForChannels, getChannelsByDevice]);

  // 查看通道处理函数
  const handleViewChannels = useCallback((deviceName: string) => {
    setSelectedDeviceForChannels(deviceName);
    setCurrentView('channels');
  }, []);

  // 返回设备视图
  const handleBackToDevices = useCallback(() => {
    setCurrentView('devices');
  }, []);

  // 处理URL参数
  useEffect(() => {
    const deviceParam = searchParams.get('device');
    if (deviceParam && devices.length > 0) {
      // 检查设备是否存在
      const deviceExists = devices.some(device => device.deviceName === deviceParam);
      if (deviceExists) {
        handleDeviceSelect(deviceParam, true); // 跳过动画
        setCurrentView('channels'); // 直接显示通道视图
      }
    }
  }, [searchParams, devices, handleDeviceSelect]);

  // 初始化数据
  useEffect(() => {
    if (devices.length === 0) {
      initializeData();
    }
  }, [devices.length, initializeData]);

  // 页面卸载时清理定时器
  useEffect(() => {
    return () => {
      stopSimulation();
    };
  }, [stopSimulation]);

  // 辅助函数
  const formatIpAddress = (ip: number[]) => {
    return ip.join('.');
  };

  const getStatusColor = (status: string) => {
    // 设备状态颜色
    switch (status) {
      case DeviceStatus.WORKING: return 'bg-green-500';
      case DeviceStatus.OFFLINE: return 'bg-red-500';
      case DeviceStatus.ERROR: return 'bg-red-500';
      case DeviceStatus.INITIALIZING: return 'bg-blue-500';
      case DeviceStatus.CALIBRATING: return 'bg-yellow-500';
      default: break;
    }

    // 通道状态颜色
    const channelColors = {
      '充电': 'bg-green-500',
      '放电': 'bg-blue-500',
      '搁置': 'bg-yellow-500',
      '暂停': 'bg-red-500',
      '完成': 'bg-gray-500',
      '复位': 'bg-purple-500',
      '运行中': 'bg-green-500',
      '待机': 'bg-yellow-500',
      '故障': 'bg-red-500'
    };
    return channelColors[status as keyof typeof channelColors] || 'bg-gray-500';
  };

  // 格式化时间
  const formatTime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const getValidTemps = (temps: (number | null)[]) =>
    temps.filter(t => t !== null) as number[];

  const getAvgTemp = (temps: (number | null)[]) => {
    const validTemps = getValidTemps(temps);
    return validTemps.length > 0 ?
      (validTemps.reduce((sum, temp) => sum + temp, 0) / validTemps.length).toFixed(1) :
      'N/A';
  };

  // 设备设置对话框处理函数
  const handleOpenDeviceSettings = (deviceName: string) => {
    const device = devices.find(d => d.deviceName === deviceName);
    if (device) {
      setSelectedDeviceForSettings(device);
      setDeviceSettingsOpen(true);
    }
  };

  const handleOpenChannelSettings = (deviceName: string) => {
    // 跳转到通道页面
    window.location.href = `/zh/channels?device=${encodeURIComponent(deviceName)}`;
  };

  // 设备操作函数
  const deviceOperations = {
    send_process: (deviceName: string) => {
      // 跳转到工艺流程管理页面
      window.location.href = `/zh/process?device=${encodeURIComponent(deviceName)}&action=send`;
    },
    set_advanced_config: (deviceName: string) => {
      // 跳转到工艺流程高级设置
      window.location.href = `/zh/process?device=${encodeURIComponent(deviceName)}&action=config`;
    },
    reset_device: (deviceName: string) => {
      if (confirm(`确定要复位设备 ${deviceName} 吗？`)) {
        console.log(`复位设备: ${deviceName}`);
        // TODO: 实现设备复位功能
      }
    },
    device_settings: (deviceName: string) => handleOpenDeviceSettings(deviceName),
    channel_settings: (deviceName: string) => handleOpenChannelSettings(deviceName),
    scan_barcode: (deviceName: string) => {
      console.log(`条码录入: ${deviceName}`);
      // TODO: 实现条码录入功能
    },
    enable_channel: (deviceName: string) => {
      console.log(`批量启用通道: ${deviceName}`);
      // TODO: 实现批量启用通道功能
    },
    disable_channel: (deviceName: string) => {
      console.log(`批量禁用通道: ${deviceName}`);
      // TODO: 实现批量禁用通道功能
    },
    get_sorting_result: (deviceName: string) => {
      // 跳转到电池分选结果页面
      window.location.href = `/zh/battery-sorting/results?device=${encodeURIComponent(deviceName)}`;
    },
    clear_error: (deviceName: string) => {
      if (confirm(`确定要清除设备 ${deviceName} 的异常状态吗？`)) {
        console.log(`异常清除: ${deviceName}`);
        // TODO: 实现异常清除功能
      }
    },
    update_firmware: (deviceName: string) => {
      console.log(`刷新下位机版本: ${deviceName}`);
      // TODO: 实现固件更新功能
    },
    calibrate_temperature: (deviceName: string) => {
      console.log(`温度校准: ${deviceName}`);
      // TODO: 实现温度校准功能
    },
    get_temperature: (deviceName: string) => {
      console.log(`查看环境温度: ${deviceName}`);
      // TODO: 实现环境温度查看功能
    }
  };

  // 通道操作函数
  const channelOperations = {
    channel_reset: (channelNumber: number) => console.log(`通道复位: ${channelNumber}`),
    channel_pause: (channelNumber: number) => console.log(`通道暂停: ${channelNumber}`),
    schedule_pause_current: (channelNumber: number) => console.log(`当前工步预约暂停: ${channelNumber}`),
    schedule_pause_at: (channelNumber: number) => console.log(`指定工步预约暂停: ${channelNumber}`),
    cancel_scheduled_pause: (channelNumber: number) => console.log(`取消预约暂停: ${channelNumber}`),
    channel_resume: (channelNumber: number) => console.log(`通道接续: ${channelNumber}`),
    jump_to_next_step: (channelNumber: number) => console.log(`跳转到下一个工步: ${channelNumber}`),
    jump_to_step: (channelNumber: number) => console.log(`跳转到指定工步: ${channelNumber}`),
    channel_register: (channelNumber: number) => console.log(`通道寄存: ${channelNumber}`),
    show_curve: (channelNumber: number) => {
      // 跳转到数据分析页面
      window.location.href = `/zh/analytics?device=${encodeURIComponent(selectedDeviceForChannels || '')}&channel=${channelNumber}`;
    },
    show_recipe: (channelNumber: number) => {
      // 跳转到工艺流程监控页面
      window.location.href = `/zh/process?device=${encodeURIComponent(selectedDeviceForChannels || '')}&channel=${channelNumber}`;
    },
    toggle_channel: (channelNumber: number, enabled: boolean) => {
      console.log(`通道${enabled ? '启用' : '禁用'}: ${channelNumber}`);
      // 更新通道状态
      if (selectedDeviceForChannels) {
        updateChannelStatus(selectedDeviceForChannels, channelNumber, { isEnabled: enabled });
      }
    }
  };

  // 通道控制条组件
  const ChannelControlBar = ({ channel, viewMode }: { channel: any, viewMode: 'list' | 'cards' }) => {
    const [isOpen, setIsOpen] = useState(false);

    return (
      <Popover open={isOpen} onOpenChange={setIsOpen}>
        <PopoverTrigger asChild>
          <div
            className="absolute inset-0 cursor-pointer"
            onClick={() => setIsOpen(!isOpen)}
          />
        </PopoverTrigger>
        <PopoverContent
          className="w-auto max-w-[90vw] p-0 border-0 bg-transparent shadow-none"
          side={viewMode === 'cards' ? 'bottom' : 'right'}
          align={viewMode === 'cards' ? 'center' : 'center'}
          sideOffset={viewMode === 'cards' ? -80 : -50}
          alignOffset={viewMode === 'cards' ? 0 : 0}
          onInteractOutside={() => setIsOpen(false)}
        >
          <div className="relative">
            {/* 毛玻璃胶囊容器 */}
            <div className="bg-black/20 backdrop-blur-md rounded-full px-4 py-3 shadow-2xl border border-white/20 animate-in slide-in-from-bottom-2 duration-300">
              <div className="flex items-center gap-1">
                {/* 复位 */}
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <button
                        className="w-10 h-10 rounded-full bg-white/10 hover:bg-white/20 transition-all duration-200 flex items-center justify-center group"
                        onClick={() => channelOperations.channel_reset(channel.channelNumber)}
                      >
                        <RotateCcw className="h-4 w-4 text-white group-hover:scale-110 transition-transform" />
                      </button>
                    </TooltipTrigger>
                    <TooltipContent side="top" className="bg-black/80 text-white border-white/20">
                      复位
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>

                {/* 暂停 */}
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <button
                        className="w-10 h-10 rounded-full bg-white/10 hover:bg-white/20 transition-all duration-200 flex items-center justify-center group"
                        onClick={() => channelOperations.channel_pause(channel.channelNumber)}
                      >
                        <Pause className="h-4 w-4 text-white group-hover:scale-110 transition-transform" />
                      </button>
                    </TooltipTrigger>
                    <TooltipContent side="top" className="bg-black/80 text-white border-white/20">
                      暂停
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>

                {/* 接续 */}
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <button
                        className="w-10 h-10 rounded-full bg-white/10 hover:bg-white/20 transition-all duration-200 flex items-center justify-center group"
                        onClick={() => channelOperations.channel_resume(channel.channelNumber)}
                      >
                        <Play className="h-4 w-4 text-white group-hover:scale-110 transition-transform" />
                      </button>
                    </TooltipTrigger>
                    <TooltipContent side="top" className="bg-black/80 text-white border-white/20">
                      接续
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>

                {/* 分隔线 */}
                <div className="w-px h-6 bg-white/20 mx-1"></div>

                {/* 当前工步预约暂停 */}
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <button
                        className="w-10 h-10 rounded-full bg-white/10 hover:bg-white/20 transition-all duration-200 flex items-center justify-center group"
                        onClick={() => channelOperations.schedule_pause_current(channel.channelNumber)}
                      >
                        <Clock className="h-4 w-4 text-white group-hover:scale-110 transition-transform" />
                      </button>
                    </TooltipTrigger>
                    <TooltipContent side="top" className="bg-black/80 text-white border-white/20">
                      当前工步预约暂停
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>

                {/* 指定工步预约暂停 */}
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <button
                        className="w-10 h-10 rounded-full bg-white/10 hover:bg-white/20 transition-all duration-200 flex items-center justify-center group"
                        onClick={() => channelOperations.schedule_pause_at(channel.channelNumber)}
                      >
                        <AlertCircle className="h-4 w-4 text-white group-hover:scale-110 transition-transform" />
                      </button>
                    </TooltipTrigger>
                    <TooltipContent side="top" className="bg-black/80 text-white border-white/20">
                      指定工步预约暂停
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>

                {/* 取消预约暂停 */}
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <button
                        className="w-10 h-10 rounded-full bg-white/10 hover:bg-white/20 transition-all duration-200 flex items-center justify-center group"
                        onClick={() => channelOperations.cancel_scheduled_pause(channel.channelNumber)}
                      >
                        <CheckCircle className="h-4 w-4 text-white group-hover:scale-110 transition-transform" />
                      </button>
                    </TooltipTrigger>
                    <TooltipContent side="top" className="bg-black/80 text-white border-white/20">
                      取消预约暂停
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>

                {/* 跳转到下一个工步 */}
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <button
                        className="w-10 h-10 rounded-full bg-white/10 hover:bg-white/20 transition-all duration-200 flex items-center justify-center group"
                        onClick={() => channelOperations.jump_to_next_step(channel.channelNumber)}
                      >
                        <ArrowRight className="h-4 w-4 text-white group-hover:scale-110 transition-transform" />
                      </button>
                    </TooltipTrigger>
                    <TooltipContent side="top" className="bg-black/80 text-white border-white/20">
                      跳转到下一个工步
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>

                {/* 跳转到指定工步 */}
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <button
                        className="w-10 h-10 rounded-full bg-white/10 hover:bg-white/20 transition-all duration-200 flex items-center justify-center group"
                        onClick={() => channelOperations.jump_to_step(channel.channelNumber)}
                      >
                        <ArrowDown className="h-4 w-4 text-white group-hover:scale-110 transition-transform" />
                      </button>
                    </TooltipTrigger>
                    <TooltipContent side="top" className="bg-black/80 text-white border-white/20">
                      跳转到指定工步
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>

                {/* 寄存 */}
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <button
                        className="w-10 h-10 rounded-full bg-white/10 hover:bg-white/20 transition-all duration-200 flex items-center justify-center group"
                        onClick={() => channelOperations.channel_register(channel.channelNumber)}
                      >
                        <Archive className="h-4 w-4 text-white group-hover:scale-110 transition-transform" />
                      </button>
                    </TooltipTrigger>
                    <TooltipContent side="top" className="bg-black/80 text-white border-white/20">
                      寄存
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>

                {/* 分隔线 */}
                <div className="w-px h-6 bg-white/20 mx-1"></div>

                {/* 启用/禁用切换 */}
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <button
                        className={`w-10 h-10 rounded-full transition-all duration-200 flex items-center justify-center group border ${
                          channel.isEnabled
                            ? 'bg-green-500/20 hover:bg-green-500/40 border-green-400/30'
                            : 'bg-red-500/20 hover:bg-red-500/40 border-red-400/30'
                        }`}
                        onClick={() => channelOperations.toggle_channel(channel.channelNumber, !channel.isEnabled)}
                      >
                        {channel.isEnabled ? (
                          <CheckCircle className="h-4 w-4 text-green-300 group-hover:scale-110 transition-transform" />
                        ) : (
                          <AlertCircle className="h-4 w-4 text-red-300 group-hover:scale-110 transition-transform" />
                        )}
                      </button>
                    </TooltipTrigger>
                    <TooltipContent side="top" className="bg-black/80 text-white border-white/20">
                      {channel.isEnabled ? '禁用通道' : '启用通道'}
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>

                {/* 分隔线 */}
                <div className="w-px h-6 bg-white/20 mx-1"></div>

                {/* 查看曲线 */}
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <button
                        className="w-10 h-10 rounded-full bg-green-500/20 hover:bg-green-500/40 transition-all duration-200 flex items-center justify-center group border border-green-400/30"
                        onClick={() => channelOperations.show_curve(channel.channelNumber)}
                      >
                        <TrendingUp className="h-4 w-4 text-green-300 group-hover:scale-110 transition-transform" />
                      </button>
                    </TooltipTrigger>
                    <TooltipContent side="top" className="bg-black/80 text-white border-white/20">
                      查看曲线
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>

                {/* 查看工艺 */}
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <button
                        className="w-10 h-10 rounded-full bg-blue-500/20 hover:bg-blue-500/40 transition-all duration-200 flex items-center justify-center group border border-blue-400/30"
                        onClick={() => channelOperations.show_recipe(channel.channelNumber)}
                      >
                        <FileText className="h-4 w-4 text-blue-300 group-hover:scale-110 transition-transform" />
                      </button>
                    </TooltipTrigger>
                    <TooltipContent side="top" className="bg-black/80 text-white border-white/20">
                      查看工艺
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
            </div>
          </div>
        </PopoverContent>
      </Popover>
    );
  };

  // 设备控制条组件 - 根据视图模式调整方向和位置
  const DeviceControlBar = ({ device, viewMode }: { device: any, viewMode: 'list' | 'cards' }) => {
    const [isOpen, setIsOpen] = useState(false);

    return (
      <Popover open={isOpen} onOpenChange={setIsOpen}>
        <PopoverTrigger asChild>
          <div
            className="absolute inset-0 cursor-pointer"
            onClick={() => setIsOpen(!isOpen)}
          />
        </PopoverTrigger>
        <PopoverContent
          className="w-auto max-w-[90vw] p-0 border-0 bg-transparent shadow-none"
          side={viewMode === 'cards' ? 'bottom' : 'right'}
          align={viewMode === 'cards' ? 'center' : 'center'}
          sideOffset={viewMode === 'cards' ? -80 : -50}
          alignOffset={viewMode === 'cards' ? 0 : 0}
          onInteractOutside={() => setIsOpen(false)}
        >
          <div className="relative">
            {/* 毛玻璃胶囊容器 */}
            <div className="bg-black/20 backdrop-blur-md rounded-full px-4 py-3 shadow-2xl border border-white/20 animate-in slide-in-from-bottom-2 duration-300">
              <div className="flex items-center gap-1">
                {/* 发送流程 */}
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <button
                        className="w-10 h-10 rounded-full bg-white/10 hover:bg-white/20 transition-all duration-200 flex items-center justify-center group"
                        onClick={() => deviceOperations.send_process(device.deviceName)}
                      >
                        <Send className="h-4 w-4 text-white group-hover:scale-110 transition-transform" />
                      </button>
                    </TooltipTrigger>
                    <TooltipContent side="top" className="bg-black/80 text-white border-white/20">
                      发送流程
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>

                {/* 复位 */}
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <button
                        className="w-10 h-10 rounded-full bg-white/10 hover:bg-white/20 transition-all duration-200 flex items-center justify-center group"
                        onClick={() => deviceOperations.reset_device(device.deviceName)}
                      >
                        <RotateCcw className="h-4 w-4 text-white group-hover:scale-110 transition-transform" />
                      </button>
                    </TooltipTrigger>
                    <TooltipContent side="top" className="bg-black/80 text-white border-white/20">
                      复位
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>

                {/* 设置 */}
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <button
                        className="w-10 h-10 rounded-full bg-white/10 hover:bg-white/20 transition-all duration-200 flex items-center justify-center group"
                        onClick={() => deviceOperations.device_settings(device.deviceName)}
                      >
                        <Settings className="h-4 w-4 text-white group-hover:scale-110 transition-transform" />
                      </button>
                    </TooltipTrigger>
                    <TooltipContent side="top" className="bg-black/80 text-white border-white/20">
                      设置
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>

                {/* 通道设置 */}
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <button
                        className="w-10 h-10 rounded-full bg-white/10 hover:bg-white/20 transition-all duration-200 flex items-center justify-center group"
                        onClick={() => deviceOperations.channel_settings(device.deviceName)}
                      >
                        <Sliders className="h-4 w-4 text-white group-hover:scale-110 transition-transform" />
                      </button>
                    </TooltipTrigger>
                    <TooltipContent side="top" className="bg-black/80 text-white border-white/20">
                      通道设置
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>

                {/* 分隔线 */}
                <div className="w-px h-6 bg-white/20 mx-1"></div>

                {/* 通道启用 */}
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <button
                        className="w-10 h-10 rounded-full bg-white/10 hover:bg-white/20 transition-all duration-200 flex items-center justify-center group"
                        onClick={() => deviceOperations.enable_channel(device.deviceName)}
                      >
                        <CheckCircle className="h-4 w-4 text-white group-hover:scale-110 transition-transform" />
                      </button>
                    </TooltipTrigger>
                    <TooltipContent side="top" className="bg-black/80 text-white border-white/20">
                      通道启用
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>

                {/* 查看分选 */}
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <button
                        className="w-10 h-10 rounded-full bg-white/10 hover:bg-white/20 transition-all duration-200 flex items-center justify-center group"
                        onClick={() => deviceOperations.get_sorting_result(device.deviceName)}
                      >
                        <Filter className="h-4 w-4 text-white group-hover:scale-110 transition-transform" />
                      </button>
                    </TooltipTrigger>
                    <TooltipContent side="top" className="bg-black/80 text-white border-white/20">
                      查看分选
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>

                {/* 异常清除 */}
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <button
                        className="w-10 h-10 rounded-full bg-white/10 hover:bg-white/20 transition-all duration-200 flex items-center justify-center group"
                        onClick={() => deviceOperations.clear_error(device.deviceName)}
                      >
                        <AlertCircle className="h-4 w-4 text-white group-hover:scale-110 transition-transform" />
                      </button>
                    </TooltipTrigger>
                    <TooltipContent side="top" className="bg-black/80 text-white border-white/20">
                      异常清除
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>

                {/* 温度校准Pro */}
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <button
                        className="w-10 h-10 rounded-full bg-white/10 hover:bg-white/20 transition-all duration-200 flex items-center justify-center group"
                        onClick={() => deviceOperations.calibrate_temperature(device.deviceName)}
                      >
                        <Target className="h-4 w-4 text-white group-hover:scale-110 transition-transform" />
                      </button>
                    </TooltipTrigger>
                    <TooltipContent side="top" className="bg-black/80 text-white border-white/20">
                      温度校准Pro
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>

                {/* 查看环境温度 */}
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <button
                        className="w-10 h-10 rounded-full bg-white/10 hover:bg-white/20 transition-all duration-200 flex items-center justify-center group"
                        onClick={() => deviceOperations.get_temperature(device.deviceName)}
                      >
                        <ThermometerSun className="h-4 w-4 text-white group-hover:scale-110 transition-transform" />
                      </button>
                    </TooltipTrigger>
                    <TooltipContent side="top" className="bg-black/80 text-white border-white/20">
                      查看环境温度
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>

                {/* 分隔线 */}
                <div className="w-px h-6 bg-white/20 mx-1"></div>

                {/* 查看通道 - 带颜色，放在最后 */}
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <button
                        className="w-10 h-10 rounded-full bg-blue-500/20 hover:bg-blue-500/40 transition-all duration-200 flex items-center justify-center group border border-blue-400/30"
                        onClick={() => handleViewChannels(device.deviceName)}
                      >
                        <Monitor className="h-4 w-4 text-blue-300 group-hover:scale-110 transition-transform" />
                      </button>
                    </TooltipTrigger>
                    <TooltipContent side="top" className="bg-black/80 text-white border-white/20">
                      查看通道
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
            </div>
          </div>
        </PopoverContent>
      </Popover>
    );
  };

  return (
    <div className="space-y-6">
      {/* 滑动容器 */}
      <div className="relative overflow-hidden">
        <div
          className="flex transition-transform duration-500 ease-in-out"
          style={{ transform: `translateX(${currentView === 'devices' ? '0%' : '-100%'})` }}
        >
          {/* 设备监控视图 */}
          <div className="w-full flex-shrink-0">
            <div className="flex items-center justify-between mb-6">
              <div>
                <h1 className="text-3xl font-bold tracking-tight">设备监控</h1>
                <p className="text-muted-foreground">
                  实时监控系统设备状态和参数
                </p>
              </div>
        <div className="flex gap-2">
          {/* 视图切换按钮 */}
          <div className="flex border rounded-lg">
            <Button
              variant={viewMode === 'list' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('list')}
              className="rounded-r-none"
            >
              <List className="h-4 w-4" />
            </Button>
            <Button
              variant={viewMode === 'cards' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('cards')}
              className="rounded-l-none"
            >
              <LayoutGrid className="h-4 w-4" />
            </Button>
          </div>

          {/* 模拟设置 */}
          <Popover>
            <PopoverTrigger asChild>
              <Button variant="outline">
                <Settings className="mr-2 h-4 w-4" />
                模拟设置
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-80">
              <div className="space-y-4">
                <h4 className="font-medium">模拟参数设置</h4>

                <div className="space-y-2">
                  <Label>设备数量: {simulationSettings.deviceCount[0]}</Label>
                  <Slider
                    value={simulationSettings.deviceCount}
                    onValueChange={(value) => updateSimulationSettings({ deviceCount: value })}
                    max={20}
                    min={1}
                    step={1}
                  />
                </div>

                <div className="space-y-2">
                  <Label>温度变化范围: ±{simulationSettings.tempVariation[0]}°C</Label>
                  <Slider
                    value={simulationSettings.tempVariation}
                    onValueChange={(value) => updateSimulationSettings({ tempVariation: value })}
                    max={20}
                    min={1}
                    step={1}
                  />
                </div>

                <div className="space-y-2">
                  <Label>更新间隔: {simulationSettings.updateInterval[0]}秒</Label>
                  <Slider
                    value={simulationSettings.updateInterval}
                    onValueChange={(value) => updateSimulationSettings({ updateInterval: value })}
                    max={10}
                    min={1}
                    step={1}
                  />
                </div>

                <div className="space-y-2">
                  <Label>错误率: {simulationSettings.errorRate[0]}%</Label>
                  <Slider
                    value={simulationSettings.errorRate}
                    onValueChange={(value) => updateSimulationSettings({ errorRate: value })}
                    max={50}
                    min={0}
                    step={1}
                  />
                </div>

                <div className="pt-4 border-t">
                  <div className="flex gap-2 mb-2">
                    <Button
                      size="sm"
                      variant={isSimulationRunning ? "destructive" : "default"}
                      onClick={isSimulationRunning ? stopSimulation : startSimulation}
                      className="flex-1"
                    >
                      {isSimulationRunning ? "停止模拟" : "开始模拟"}
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => initializeData()}
                    >
                      重置数据
                    </Button>
                  </div>

                  <div className="flex gap-2">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => {
                        // 保存模拟设置，由于使用了persist中间件，设置会自动保存
                        // 这里可以添加保存成功的提示
                        console.log('模拟设置已保存:', simulationSettings);
                      }}
                      className="flex-1"
                    >
                      保存设置
                    </Button>
                  </div>

                  <p className="text-xs text-muted-foreground mt-2">
                    {isSimulationRunning
                      ? `模拟运行中，每${simulationSettings.updateInterval[0]}秒更新一次数据`
                      : "点击开始模拟以实时更新设备数据"
                    }
                  </p>
                </div>
              </div>
            </PopoverContent>
          </Popover>

          {/* 设备管理 */}
          <Button variant="outline" onClick={() => setDeviceManagementOpen(true)}>
            <Monitor className="mr-2 h-4 w-4" />
            设备管理
          </Button>

          <Button>
            <Monitor className="mr-2 h-4 w-4" />
            全屏监控
          </Button>
        </div>
      </div>

      {/* 监控概览 */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">在线设备</CardTitle>
            <Activity className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{devices.filter(d => d.status !== DeviceStatus.OFFLINE).length}</div>
            <p className="text-xs text-muted-foreground">
              共{devices.length}台设备
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">平均环境温度</CardTitle>
            <Thermometer className="h-4 w-4 text-orange-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {(() => {
                const allTemps = devices.flatMap(d => getValidTemps(d.ambientTemp));
                return allTemps.length > 0 ?
                  (allTemps.reduce((sum, temp) => sum + temp, 0) / allTemps.length).toFixed(1) + '°C' :
                  'N/A';
              })()}
            </div>
            <p className="text-xs text-muted-foreground">
              正常范围内
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">工作设备</CardTitle>
            <Gauge className="h-4 w-4 text-blue-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{devices.filter(d => d.status === DeviceStatus.WORKING).length}</div>
            <p className="text-xs text-muted-foreground">
              正在工作中
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">数据更新</CardTitle>
            <Monitor className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">实时</div>
            <p className="text-xs text-muted-foreground">
              {simulationSettings.updateInterval[0]}秒刷新
            </p>
          </CardContent>
        </Card>
      </div>

      {/* 设备视图 */}
      <div className="h-[calc(100vh-20rem)] overflow-hidden border rounded-lg bg-card mt-6">
        <div className="h-full overflow-y-auto scrollbar-hide p-6">
          {viewMode === 'list' && (
            <div className="space-y-4">
              {devices.map((device) => (
                <div key={device.deviceName} className="relative">
                  <div className="border rounded-lg p-4 transition-all duration-300 hover:border-blue-500">
                    <div className="grid grid-cols-6 gap-4 items-center">
                      <div className="flex items-center space-x-3">
                        <div className={`w-3 h-3 rounded-full ${getStatusColor(device.status)}`}></div>
                        <div>
                          <p className="font-medium">{device.deviceName}</p>
                          <p className="text-sm text-muted-foreground">{formatIpAddress(device.ipAddress)}</p>
                        </div>
                      </div>
                      <div>
                        <Badge className={getStatusColor(device.status)}>{device.status}</Badge>
                      </div>
                      <div>
                        <p className="text-sm">环境温度</p>
                        <p className="font-medium">{getAvgTemp(device.ambientTemp)}°C</p>
                      </div>
                      <div>
                        <p className="text-sm">探针温度</p>
                        <p className="font-medium">{getAvgTemp(device.probeTemp)}°C</p>
                      </div>
                      <div>
                        <p className="text-sm">版本</p>
                        <p className="font-medium">{device.version}</p>
                      </div>
                      <div>
                        <p className="text-sm">位置</p>
                        <p className="font-medium">{device.otherInfo.location}</p>
                      </div>
                    </div>
                  </div>

                  {/* 设备控制条 */}
                  <DeviceControlBar device={device} viewMode="list" />
                </div>
              ))}
            </div>
          )}

          {/* 卡片视图 */}
          {viewMode === 'cards' && (
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
              {devices.map((device) => (
                    <div key={device.deviceName} className="relative">
                      <Card className="transition-all duration-300 hover:border-blue-500 hover:shadow-lg">
                        <CardHeader className="pb-3">
                          <div className="flex items-center justify-between">
                            <CardTitle className="text-lg">{device.deviceName}</CardTitle>
                            <Badge className={getStatusColor(device.status)}>{device.status}</Badge>
                          </div>
                          <CardDescription>{formatIpAddress(device.ipAddress)}</CardDescription>
                        </CardHeader>
                        <CardContent>
                          <div className="space-y-3">
                            <div className="grid grid-cols-2 gap-3">
                              <div>
                                <p className="text-sm text-muted-foreground">环境温度</p>
                                <p className="font-medium">{getAvgTemp(device.ambientTemp)}°C</p>
                              </div>
                              <div>
                                <p className="text-sm text-muted-foreground">探针温度</p>
                                <p className="font-medium">{getAvgTemp(device.probeTemp)}°C</p>
                              </div>
                              <div>
                                <p className="text-sm text-muted-foreground">主板温度</p>
                                <p className="font-medium">{getAvgTemp(device.boardTemp)}°C</p>
                              </div>
                              <div>
                                <p className="text-sm text-muted-foreground">通道数</p>
                                <p className="font-medium">{getValidTemps(device.channelTemp).length}/64</p>
                              </div>
                            </div>
                            <div className="pt-2 border-t">
                              <div className="flex justify-between text-sm">
                                <span>版本: {device.version}</span>
                                <span>{device.otherInfo.location}</span>
                              </div>
                            </div>
                          </div>
                        </CardContent>
                      </Card>

                      {/* 设备控制条 */}
                      <DeviceControlBar device={device} viewMode="cards" />
                    </div>
                  ))}
              </div>
            )}
          </div>
        </div>
          </div>

          {/* 通道监控视图 */}
          <div className="w-full flex-shrink-0">
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center gap-4">
                <Button
                  variant="outline"
                  size="default"
                  onClick={handleBackToDevices}
                  className="flex items-center gap-2 bg-gradient-to-r from-blue-500 to-blue-600 text-white border-blue-500 hover:from-blue-600 hover:to-blue-700 hover:border-blue-600 shadow-md"
                >
                  <ChevronLeft className="h-4 w-4" />
                  返回设备监控
                </Button>
                <div>
                  <h1 className="text-3xl font-bold tracking-tight">
                    {selectedDeviceForChannels} - 通道监控
                  </h1>
                  <p className="text-muted-foreground">
                    实时监控设备通道状态和数据
                  </p>
                </div>
              </div>

              {/* 通道视图切换按钮 */}
              <div className="flex border rounded-lg">
                <Button
                  variant={viewMode === 'list' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setViewMode('list')}
                  className="rounded-r-none"
                >
                  <List className="h-4 w-4" />
                </Button>
                <Button
                  variant={viewMode === 'cards' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setViewMode('cards')}
                  className="rounded-l-none"
                >
                  <LayoutGrid className="h-4 w-4" />
                </Button>
              </div>
            </div>

            {/* 通道内容区域 */}
            <div className="flex gap-6 h-[calc(100vh-12rem)]">
              {/* 左侧设备列表 */}
              <div className="w-80 border rounded-lg bg-muted/30 flex flex-col">
                <div className="p-4 border-b flex-shrink-0">
                  <h2 className="font-semibold">设备列表</h2>
                  <p className="text-sm text-muted-foreground">选择要监控的设备</p>
                </div>
                <div className="p-4 flex-1 overflow-hidden">
                  <div
                    ref={deviceListRef}
                    className="h-full overflow-y-auto scrollbar-hide space-y-2 device-list-container"
                  >
                    {devices.map((device) => (
                      <div
                        key={device.deviceName}
                        ref={(el) => {
                          if (el) {
                            deviceRefs.current.set(device.deviceName, el);
                          } else {
                            deviceRefs.current.delete(device.deviceName);
                          }
                        }}
                        onClick={() => handleDeviceSelect(device.deviceName)}
                      >
                        <InteractiveCard
                          isActive={selectedDeviceForChannels === device.deviceName}
                          hoverEffect="border"
                          activeEffect="border"
                        >
                        <CardContent className="p-3">
                          <div className="flex items-center justify-between mb-2">
                            <span className="font-medium">{device.deviceName}</span>
                            <Badge className={getStatusColor(device.status)}>{device.status}</Badge>
                          </div>
                          <div className="text-sm text-muted-foreground space-y-1">
                            <div>IP: {formatIpAddress(device.ipAddress)}</div>
                            <div>活跃通道: {getChannelsByDevice(device.deviceName).filter(c => c.isEnabled).length}/64</div>
                          </div>
                        </CardContent>
                        </InteractiveCard>
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              {/* 右侧通道视图 */}
              <div className="flex-1 border rounded-lg bg-card flex flex-col">
                {selectedDeviceForChannels ? (
                  <>
                    <div className="flex items-center justify-between p-4 border-b flex-shrink-0">
                      <h2 className="text-lg font-semibold">{selectedDeviceForChannels} - 通道监控</h2>
                      <div className="text-sm text-muted-foreground">
                        共 {currentChannels.length} 个通道，{currentChannels.filter(c => c.isEnabled).length} 个启用
                      </div>
                    </div>

                    <div className="p-4 flex-1 overflow-hidden">
                      <div className="h-full overflow-y-auto scrollbar-hide">
                        {/* 通道卡片视图 */}
                        {viewMode === 'cards' && (
                          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5">
                            {currentChannels.map((channel) => (
                              <div key={channel.channelNumber} className="relative">
                                <div
                                  className={`relative transition-all duration-200 ${
                                    selectedChannel === channel.channelNumber
                                      ? 'ring-2 ring-blue-500 ring-offset-2 shadow-lg'
                                      : ''
                                  }`}
                                  onClick={() => setSelectedChannel(
                                    selectedChannel === channel.channelNumber ? null : channel.channelNumber
                                  )}
                                >
                                  <EnhancedChannelCard
                                    channel={channel}
                                    onChannelClick={(channel) => {
                                      setSelectedChannel(
                                        selectedChannel === channel.channelNumber ? null : channel.channelNumber
                                      );
                                    }}
                                  />

                                  {/* 禁用遮罩层 */}
                                  {!channel.isEnabled && (
                                    <div className="absolute inset-0 bg-muted/95 backdrop-blur-sm rounded-lg flex items-center justify-center z-10 animate-in fade-in duration-300 pointer-events-none">
                                      <div className="bg-background/90 text-muted-foreground px-4 py-2 rounded-lg shadow-lg border border-border backdrop-blur-sm">
                                        <div className="flex items-center gap-2">
                                          <div className="w-2 h-2 rounded-full bg-muted-foreground/60"></div>
                                          <span className="font-medium text-sm">通道 {channel.channelNumber} 已禁用</span>
                                        </div>
                                      </div>
                                    </div>
                                  )}
                                </div>

                                {/* 通道控制条 */}
                                <ChannelControlBar channel={channel} viewMode="cards" />
                              </div>
                            ))}
                          </div>
                        )}

                        {/* 通道列表视图 */}
                        {viewMode === 'list' && (
                          <div className="space-y-4">
                            {currentChannels.map((channel) => (
                              <div key={channel.channelNumber} className="relative">
                                <div
                                  className={`relative transition-all duration-200 ${
                                    selectedChannel === channel.channelNumber
                                      ? 'ring-2 ring-blue-500 ring-offset-2 shadow-lg rounded-lg'
                                      : ''
                                  }`}
                                  onClick={() => setSelectedChannel(
                                    selectedChannel === channel.channelNumber ? null : channel.channelNumber
                                  )}
                                >
                                  <InteractiveListItem isDisabled={!channel.isEnabled}>
                                    <div className="grid grid-cols-8 gap-4 items-center">
                                      <div className="flex items-center space-x-3">
                                        <div className={`w-3 h-3 rounded-full ${getStatusColor(channel.stepStatus)}`}></div>
                                        <div>
                                          <p className="font-medium">通道 {channel.channelNumber}</p>
                                          <p className="text-sm text-muted-foreground">{channel.stepMode}</p>
                                        </div>
                                      </div>
                                      <div>
                                        <Badge className={getStatusColor(channel.stepStatus)}>{channel.stepStatus}</Badge>
                                      </div>
                                      <div>
                                        <p className="text-sm">电流</p>
                                        <p className="font-medium">{channel.current}A</p>
                                      </div>
                                      <div>
                                        <p className="text-sm">电压</p>
                                        <p className="font-medium">{channel.voltage}V</p>
                                      </div>
                                      <div>
                                        <p className="text-sm">容量</p>
                                        <p className="font-medium">{channel.capacity}mAh</p>
                                      </div>
                                      <div>
                                        <p className="text-sm">温度</p>
                                        <p className="font-medium">{channel.temperature}°C</p>
                                      </div>
                                      <div>
                                        <p className="text-sm">工步</p>
                                        <p className="font-medium">{channel.stepNumber}/{channel.totalStepNumber}</p>
                                      </div>
                                      <div>
                                        <p className="text-sm">进程时间</p>
                                        <p className="font-medium">{formatTime(channel.processTime)}</p>
                                      </div>
                                    </div>
                                  </InteractiveListItem>

                                  {/* 禁用遮罩层 */}
                                  {!channel.isEnabled && (
                                    <div className="absolute inset-0 bg-muted/95 backdrop-blur-sm rounded-lg flex items-center justify-center z-10 animate-in fade-in duration-300 pointer-events-none">
                                      <div className="bg-background/90 text-muted-foreground px-4 py-2 rounded-lg shadow-lg border border-border backdrop-blur-sm">
                                        <div className="flex items-center gap-2">
                                          <div className="w-2 h-2 rounded-full bg-muted-foreground/60"></div>
                                          <span className="font-medium text-sm">通道 {channel.channelNumber} 已禁用</span>
                                        </div>
                                      </div>
                                    </div>
                                  )}
                                </div>

                                {/* 通道控制条 */}
                                <ChannelControlBar channel={channel} viewMode="list" />
                              </div>
                            ))}
                          </div>
                        )}
                      </div>
                    </div>
                  </>
                ) : (
                  <div className="flex items-center justify-center h-full text-muted-foreground">
                    请选择一个设备查看通道信息
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 设备设置对话框 */}
      <DeviceSettingsDialog
        device={selectedDeviceForSettings}
        open={deviceSettingsOpen}
        onOpenChange={setDeviceSettingsOpen}
      />

      {/* 设备管理对话框 */}
      <DeviceManagementDialog
        open={deviceManagementOpen}
        onOpenChange={setDeviceManagementOpen}
      />
    </div>
  );
}

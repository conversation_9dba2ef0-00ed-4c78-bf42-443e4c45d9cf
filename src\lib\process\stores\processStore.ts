'use client';

import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import { Process, ProcessInfo, CreateProcessData } from '../types/process';
import { ProcessService } from '../services/processService';

interface ProcessState {
  // 当前选中的工艺流程
  currentProcess: Process | null;
  
  // 工艺流程列表
  processes: ProcessInfo[];
  
  // 加载状态
  isLoading: boolean;
  
  // 错误信息
  error: string | null;
  
  // 是否已初始化
  isInitialized: boolean;
}

interface ProcessActions {
  // 设置当前工艺流程
  setCurrentProcess: (process: Process | null) => void;
  
  // 加载工艺流程列表
  loadProcesses: () => Promise<void>;
  
  // 加载单个工艺流程
  loadProcess: (id: string) => Promise<Process | null>;
  
  // 创建新的工艺流程
  createProcess: (data: CreateProcessData) => Promise<Process>;

  // 删除工艺流程
  deleteProcess: (id: string) => Promise<void>;

  // 刷新当前工艺流程
  refreshCurrentProcess: () => Promise<void>;
  
  // 清除错误
  clearError: () => void;
  
  // 重置状态
  reset: () => void;
  
  // 初始化
  initialize: () => Promise<void>;
}

type ProcessStore = ProcessState & ProcessActions;

const initialState: ProcessState = {
  currentProcess: null,
  processes: [],
  isLoading: false,
  error: null,
  isInitialized: false,
};

export const useProcessStore = create<ProcessStore>()(
  devtools(
    persist(
      (set, get) => ({
        ...initialState,

        setCurrentProcess: (process) => {
          set({ currentProcess: process }, false, 'setCurrentProcess');
        },

        loadProcesses: async () => {
          set({ isLoading: true, error: null }, false, 'loadProcesses/start');
          
          try {
            const processService = new ProcessService();
            const processes = await processService.getProcesses({
              sortBy: 'updatedAt',
              sortOrder: 'desc',
              limit: 100
            });
            
            set({ 
              processes, 
              isLoading: false 
            }, false, 'loadProcesses/success');
          } catch (error) {
            const errorMessage = error instanceof Error ? error.message : '加载工艺流程失败';
            set({ 
              error: errorMessage, 
              isLoading: false 
            }, false, 'loadProcesses/error');
          }
        },

        loadProcess: async (id) => {
          set({ isLoading: true, error: null }, false, 'loadProcess/start');
          
          try {
            const processService = new ProcessService();
            const process = await processService.getProcess(id);
            
            if (process) {
              set({ 
                currentProcess: process, 
                isLoading: false 
              }, false, 'loadProcess/success');
            } else {
              set({ 
                error: '工艺流程不存在', 
                isLoading: false 
              }, false, 'loadProcess/notFound');
            }
            
            return process;
          } catch (error) {
            const errorMessage = error instanceof Error ? error.message : '加载工艺流程失败';
            set({ 
              error: errorMessage, 
              isLoading: false 
            }, false, 'loadProcess/error');
            return null;
          }
        },

        createProcess: async (data) => {
          set({ isLoading: true, error: null }, false, 'createProcess/start');
          
          try {
            const processService = new ProcessService();
            const newProcess = await processService.createProcess(data);
            
            // 更新工艺流程列表
            const { processes } = get();
            const updatedProcesses = [newProcess, ...processes];
            
            set({ 
              processes: updatedProcesses,
              currentProcess: newProcess,
              isLoading: false 
            }, false, 'createProcess/success');
            
            return newProcess;
          } catch (error) {
            const errorMessage = error instanceof Error ? error.message : '创建工艺流程失败';
            set({ 
              error: errorMessage, 
              isLoading: false 
            }, false, 'createProcess/error');
            throw error;
          }
        },

        deleteProcess: async (id) => {
          set({ isLoading: true, error: null }, false, 'deleteProcess/start');

          try {
            const processService = new ProcessService();
            await processService.deleteProcess(id);

            // 从工艺流程列表中移除
            const { processes } = get();
            const updatedProcesses = processes.filter(p => p.id !== id);

            set({
              processes: updatedProcesses,
              isLoading: false
            }, false, 'deleteProcess/success');
          } catch (error) {
            const errorMessage = error instanceof Error ? error.message : '删除工艺流程失败';
            set({
              error: errorMessage,
              isLoading: false
            }, false, 'deleteProcess/error');
            throw error;
          }
        },

        refreshCurrentProcess: async () => {
          const { currentProcess } = get();
          if (!currentProcess) {
            return;
          }
          
          await get().loadProcess(currentProcess.id);
        },

        clearError: () => {
          set({ error: null }, false, 'clearError');
        },

        reset: () => {
          set(initialState, false, 'reset');
        },

        initialize: async () => {
          if (get().isInitialized) {
            return;
          }
          
          set({ isInitialized: true }, false, 'initialize');
          await get().loadProcesses();
        },
      }),
      {
        name: 'process-store',
        partialize: (state) => ({
          currentProcess: state.currentProcess,
          // 不持久化processes列表，每次重新加载
        }),
      }
    ),
    {
      name: 'process-store',
    }
  )
);

// 便利的选择器hooks
export const useCurrentProcess = () => useProcessStore((state) => state.currentProcess);
export const useProcesses = () => useProcessStore((state) => state.processes);
export const useProcessLoading = () => useProcessStore((state) => state.isLoading);
export const useProcessError = () => useProcessStore((state) => state.error);

'use client';

import React from 'react';
import { PieChart, Pie, Cell, BarChart, Bar, LineChart, Line, XAxis, YAxis, CartesianGrid, <PERSON>lt<PERSON>, Legend, ResponsiveContainer } from 'recharts';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { SortingStatistics } from '@/lib/types/battery-sorting';

interface SortingStatisticsChartsProps {
  statistics: SortingStatistics;
  className?: string;
}

const GRADE_COLORS = {
  'A+': '#16a34a',
  'A': '#22c55e',
  'B+': '#3b82f6',
  'B': '#60a5fa',
  'C': '#f59e0b',
  'D': '#f97316',
  'Reject': '#ef4444'
};

const CustomTooltip = ({ active, payload, label }: any) => {
  if (active && payload && payload.length) {
    return (
      <div className="bg-background border rounded-lg shadow-lg p-3">
        <p className="font-medium mb-2">{label}</p>
        {payload.map((entry: any, index: number) => (
          <p key={index} className="text-sm" style={{ color: entry.color }}>
            {entry.name}: {entry.value}
            {entry.payload.percentage && ` (${entry.payload.percentage.toFixed(1)}%)`}
          </p>
        ))}
      </div>
    );
  }
  return null;
};

export function SortingStatisticsCharts({ statistics, className }: SortingStatisticsChartsProps) {
  return (
    <div className={className}>
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 等级分布饼图 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              等级分布
              <Badge variant="outline">{statistics.totalTested} 个样品</Badge>
            </CardTitle>
            <CardDescription>
              电池分选等级分布情况
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-[300px]">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={statistics.gradeDistribution}
                    cx="50%"
                    cy="50%"
                    innerRadius={60}
                    outerRadius={100}
                    paddingAngle={2}
                    dataKey="count"
                    nameKey="grade"
                  >
                    {statistics.gradeDistribution.map((entry, index) => (
                      <Cell 
                        key={`cell-${index}`} 
                        fill={GRADE_COLORS[entry.grade as keyof typeof GRADE_COLORS] || '#6b7280'} 
                      />
                    ))}
                  </Pie>
                  <Tooltip content={<CustomTooltip />} />
                  <Legend />
                </PieChart>
              </ResponsiveContainer>
            </div>
            
            {/* 等级统计 */}
            <div className="grid grid-cols-2 gap-2 mt-4">
              {statistics.gradeDistribution.map((item) => (
                <div key={item.grade} className="text-center">
                  <div 
                    className="text-lg font-semibold"
                    style={{ color: GRADE_COLORS[item.grade as keyof typeof GRADE_COLORS] || '#6b7280' }}
                  >
                    {item.count}
                  </div>
                  <div className="text-xs text-muted-foreground">
                    {item.grade} ({item.percentage.toFixed(1)}%)
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* 失败原因分析 */}
        <Card>
          <CardHeader>
            <CardTitle>失败原因分析</CardTitle>
            <CardDescription>
              不合格电池的主要失败原因
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-[300px]">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={statistics.failureReasons} layout="horizontal">
                  <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />
                  <XAxis type="number" className="text-xs" />
                  <YAxis 
                    type="category" 
                    dataKey="reason" 
                    className="text-xs"
                    width={80}
                  />
                  <Tooltip content={<CustomTooltip />} />
                  <Bar dataKey="count" fill="#ef4444" radius={[0, 4, 4, 0]} />
                </BarChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>

        {/* 批次质量统计 */}
        <Card>
          <CardHeader>
            <CardTitle>批次质量统计</CardTitle>
            <CardDescription>
              各批次的通过率和平均评分
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-[300px]">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={statistics.batchStatistics}>
                  <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />
                  <XAxis 
                    dataKey="batchNumber" 
                    className="text-xs"
                    angle={-45}
                    textAnchor="end"
                    height={80}
                  />
                  <YAxis className="text-xs" />
                  <Tooltip content={<CustomTooltip />} />
                  <Legend />
                  <Bar dataKey="passRate" fill="#16a34a" name="通过率 (%)" />
                  <Bar dataKey="averageScore" fill="#3b82f6" name="平均评分" />
                </BarChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>

        {/* 质量趋势 */}
        <Card>
          <CardHeader>
            <CardTitle>质量趋势</CardTitle>
            <CardDescription>
              通过率和平均评分的时间趋势
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-[300px]">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart data={statistics.trends}>
                  <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />
                  <XAxis 
                    dataKey="date" 
                    className="text-xs"
                  />
                  <YAxis className="text-xs" />
                  <Tooltip content={<CustomTooltip />} />
                  <Legend />
                  <Line 
                    type="monotone" 
                    dataKey="passRate" 
                    stroke="#16a34a" 
                    strokeWidth={2}
                    name="通过率 (%)"
                    dot={{ r: 4 }}
                  />
                  <Line 
                    type="monotone" 
                    dataKey="averageScore" 
                    stroke="#3b82f6" 
                    strokeWidth={2}
                    name="平均评分"
                    dot={{ r: 4 }}
                  />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 总体统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mt-6">
        <Card>
          <CardContent className="p-6">
            <div className="text-2xl font-bold text-blue-600">
              {statistics.totalTested}
            </div>
            <p className="text-xs text-muted-foreground">总测试数</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="text-2xl font-bold text-green-600">
              {statistics.totalPassed}
            </div>
            <p className="text-xs text-muted-foreground">合格数量</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="text-2xl font-bold text-red-600">
              {statistics.totalFailed}
            </div>
            <p className="text-xs text-muted-foreground">不合格数量</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="text-2xl font-bold text-orange-600">
              {statistics.passRate.toFixed(1)}%
            </div>
            <p className="text-xs text-muted-foreground">总通过率</p>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

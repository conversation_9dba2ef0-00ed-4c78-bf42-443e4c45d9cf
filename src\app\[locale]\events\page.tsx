'use client';

import { useEffect } from 'react';
import { useTranslations } from 'next-intl';
import { useEventStore } from '@/stores/eventStore';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import {
  Filter,
  Download,
  RefreshCw,
  Settings,
  Calendar,
  Search,
  BarChart3,
  Clock,
  AlertTriangle,
  Info,
  XCircle,
  CheckCircle,
} from 'lucide-react';
import { EventLevel, EventCategory, QuickTimeRange } from '@/types/events';
import { EventList } from '@/components/events/EventList';
import { EventFilters } from '@/components/events/EventFilters';
import { EventDetails } from '@/components/events/EventDetails';
import { DateTimeRangePicker, type DateTimeRange } from '@/components/ui/date-time-range-picker';
import { cn } from '@/lib/utils';

export default function EventsPage() {
  const t = useTranslations('events');

  const {
    events,
    filteredEvents,
    statistics,
    isLoading,
    isRealtime,
    quickTimeRange,
    viewMode,
    pagination,
    filter,
    setFilter,
    setRealtime,
    setQuickTimeRange,
    updateStatistics,
    applyFilters,
    generateMockEvents,
  } = useEventStore();

  // 处理时间范围更新
  const handleTimeRangeUpdate = ({ range }: { range: DateTimeRange }) => {
    if (range.from && range.to) {
      setFilter({
        startTime: range.from.getTime(),
        endTime: range.to.getTime(),
      });
      setQuickTimeRange(QuickTimeRange.CUSTOM);
    }
  };

  // 初始化数据
  useEffect(() => {
    if (events.length === 0) {
      // 生成一些测试数据
      generateMockEvents(100);
    }
    updateStatistics();
    applyFilters();
  }, []);



  return (
    <div className="flex flex-col gap-6 p-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">事件查看</h1>
          <p className="text-muted-foreground">
            查看和管理系统事件，包括设备状态、通道操作、用户行为等
          </p>
        </div>

        <div className="flex items-center gap-2">
          <Button
            variant={isRealtime ? "default" : "outline"}
            size="sm"
            onClick={() => setRealtime(!isRealtime)}
          >
            <RefreshCw className={cn("h-4 w-4 mr-2", isRealtime && "animate-spin")} />
            实时更新
          </Button>

          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            导出
          </Button>

          <Button variant="outline" size="sm">
            <Settings className="h-4 w-4 mr-2" />
            设置
          </Button>
        </div>
      </div>

      {/* 统计概览 */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">总事件数</CardTitle>
            <BarChart3 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{statistics.total}</div>
            <p className="text-xs text-muted-foreground">
              最近24小时: {statistics.recentCount}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">严重事件</CardTitle>
            <XCircle className="h-4 w-4 text-red-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">
              {statistics.byLevel[EventLevel.CRITICAL] + statistics.byLevel[EventLevel.ERROR]}
            </div>
            <p className="text-xs text-muted-foreground">
              需要关注的事件
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">设备事件</CardTitle>
            <Settings className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{statistics.byCategory[EventCategory.DEVICE]}</div>
            <p className="text-xs text-muted-foreground">
              设备状态变更
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">通道事件</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{statistics.byCategory[EventCategory.CHANNEL]}</div>
            <p className="text-xs text-muted-foreground">
              通道操作记录
            </p>
          </CardContent>
        </Card>
      </div>

      {/* 筛选和控制栏 */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <DateTimeRangePicker
                initialDateFrom={filter.startTime ? new Date(filter.startTime) : undefined}
                initialDateTo={filter.endTime ? new Date(filter.endTime) : undefined}
                onUpdate={handleTimeRangeUpdate}
                className="w-auto"
              />

              <EventFilters />
            </div>

            <div className="flex items-center gap-2">
              <span className="text-sm text-muted-foreground">
                显示 {filteredEvents.length} / {statistics.total} 个事件
              </span>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* 事件列表 */}
      <EventList />

      {/* 事件详情 */}
      <EventDetails />
    </div>
  );
}

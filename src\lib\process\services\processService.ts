/**
 * 工艺流程服务
 */

import { v4 as uuidv4 } from 'uuid';
import { getLocalStorage } from '@/lib/database/config';
import {
  Process,
  ProcessInfo,
  ProcessStep,
  ProcessExecution,
  ProcessTemplate,
  CreateProcessData,
  UpdateProcessData,
  ProcessQueryParams,
  ProcessExecutionQueryParams,
  ProcessStatistics,
  ProcessExecutionResult,
  ProcessValidationResult,
  ProcessExportData,
  ProcessImportData
} from '../types/process';
import { Step } from '../types/step';

export class ProcessService {
  private storage = getLocalStorage();

  /**
   * 创建新的工艺流程
   */
  async createProcess(data: CreateProcessData): Promise<Process> {
    const processId = uuidv4();
    const now = new Date();

    // 验证工步数据
    const validation = this.validateProcess({ ...data, steps: data.steps });
    if (!validation.isValid) {
      throw new Error(`工艺流程验证失败: ${validation.errors.join(', ')}`);
    }

    const estimatedDuration = this.calculateEstimatedDuration(data.steps);

    // 创建工艺流程基本信息
    const processInfo = {
      id: processId,
      name: data.name,
      description: data.description || '',
      creator_name: data.creatorName,
      creator_id: data.creatorId || '',
      created_at: now.toISOString(),
      updated_at: now.toISOString(),
      status: 'draft',
      total_steps: data.steps.length,
      current_step: 0,
      estimated_duration: estimatedDuration,
      actual_duration: null,
      start_time: null,
      end_time: null,
      metadata: JSON.stringify(data.metadata || {})
    };

    // 保存工艺流程
    this.storage.insert('PROCESSES', processInfo);

    // 创建工步数据
    const processSteps: ProcessStep[] = [];
    data.steps.forEach((step, index) => {
      const stepId = uuidv4();
      const stepData = {
        id: stepId,
        process_id: processId,
        step_id: step.id,
        step_order: index + 1,
        step_data: JSON.stringify(step),
        created_at: now.toISOString()
      };

      this.storage.insert('PROCESS_STEPS', stepData);

      processSteps.push({
        id: stepId,
        processId,
        stepId: step.id,
        stepOrder: index + 1,
        stepData: step,
        createdAt: now
      });
    });

    // 返回完整的工艺流程对象
    return {
      id: processId,
      name: data.name,
      description: data.description,
      creatorName: data.creatorName,
      creatorId: data.creatorId,
      createdAt: now,
      updatedAt: now,
      status: 'draft',
      totalSteps: data.steps.length,
      currentStep: 0,
      estimatedDuration,
      metadata: data.metadata || {},
      steps: processSteps
    };
  }

  /**
   * 更新工艺流程
   */
  async updateProcess(processId: string, data: UpdateProcessData): Promise<Process> {
    const existingProcess = this.storage.get<any>('PROCESSES', processId);
    if (!existingProcess) {
      throw new Error('工艺流程不存在');
    }

    const now = new Date();

    // 验证工步数据（如果提供了工步）
    if (data.steps) {
      const validation = this.validateProcess({ ...data, steps: data.steps });
      if (!validation.isValid) {
        throw new Error(`工艺流程验证失败: ${validation.errors.join(', ')}`);
      }
    }

    // 更新工艺流程基本信息
    const updatedProcessInfo = {
      ...existingProcess,
      name: data.name ?? existingProcess.name,
      description: data.description ?? existingProcess.description,
      updated_at: now.toISOString(),
      total_steps: data.steps ? data.steps.length : existingProcess.total_steps,
      estimated_duration: data.steps ? this.calculateEstimatedDuration(data.steps) : existingProcess.estimated_duration,
      metadata: data.metadata ? { ...existingProcess.metadata, ...data.metadata } : existingProcess.metadata
    };

    // 保存更新的工艺流程信息
    this.storage.set('PROCESSES', processId, updatedProcessInfo);

    // 如果提供了新的工步，更新工步数据
    if (data.steps) {
      // 删除旧的工步
      const existingSteps = this.storage.getAll<any>('PROCESS_STEPS');
      existingSteps.forEach(step => {
        if (step.processId === processId) {
          this.storage.delete('PROCESS_STEPS', step.id);
        }
      });

      // 添加新的工步
      const processSteps: ProcessStep[] = [];
      data.steps.forEach((step, index) => {
        const stepId = uuidv4();
        this.storage.set('PROCESS_STEPS', stepId, {
          id: stepId,
          processId,
          stepId: step.id,
          stepOrder: index + 1,
          stepData: step,
          createdAt: now
        });

        processSteps.push({
          id: stepId,
          processId,
          stepId: step.id,
          stepOrder: index + 1,
          stepData: step,
          createdAt: now
        });
      });

      // 返回完整的工艺流程对象
      return {
        id: processId,
        name: updatedProcessInfo.name,
        description: updatedProcessInfo.description,
        creatorName: updatedProcessInfo.creator_name,
        creatorId: updatedProcessInfo.creator_id,
        createdAt: new Date(updatedProcessInfo.created_at),
        updatedAt: now,
        status: updatedProcessInfo.status,
        totalSteps: data.steps.length,
        currentStep: updatedProcessInfo.current_step || 0,
        estimatedDuration: this.calculateEstimatedDuration(data.steps),
        metadata: updatedProcessInfo.metadata || {},
        steps: processSteps
      };
    } else {
      // 如果没有提供工步，获取现有工步
      const existingSteps = this.storage.getAll<any>('PROCESS_STEPS')
        .filter(step => step.processId === processId)
        .sort((a, b) => a.stepOrder - b.stepOrder);

      return {
        id: processId,
        name: updatedProcessInfo.name,
        description: updatedProcessInfo.description,
        creatorName: updatedProcessInfo.creator_name,
        creatorId: updatedProcessInfo.creator_id,
        createdAt: new Date(updatedProcessInfo.created_at),
        updatedAt: now,
        status: updatedProcessInfo.status,
        totalSteps: updatedProcessInfo.total_steps,
        currentStep: updatedProcessInfo.current_step || 0,
        estimatedDuration: updatedProcessInfo.estimated_duration,
        metadata: updatedProcessInfo.metadata || {},
        steps: existingSteps
      };
    }
  }

  /**
   * 获取工艺流程列表
   */
  async getProcesses(params: ProcessQueryParams = {}): Promise<ProcessInfo[]> {
    let processes = this.storage.getAll<any>('PROCESSES');

    // 应用筛选条件
    if (params.status && params.status.length > 0) {
      processes = processes.filter(p => params.status!.includes(p.status));
    }

    if (params.creatorId) {
      processes = processes.filter(p => p.creator_id === params.creatorId);
    }

    if (params.search) {
      const searchTerm = params.search.toLowerCase();
      processes = processes.filter(p =>
        p.name.toLowerCase().includes(searchTerm) ||
        (p.description && p.description.toLowerCase().includes(searchTerm))
      );
    }

    if (params.createdAfter) {
      processes = processes.filter(p => new Date(p.created_at) >= params.createdAfter!);
    }

    if (params.createdBefore) {
      processes = processes.filter(p => new Date(p.created_at) <= params.createdBefore!);
    }

    // 排序
    const sortBy = params.sortBy || 'created_at';
    const sortOrder = params.sortOrder || 'desc';

    processes.sort((a, b) => {
      let aValue = a[sortBy === 'createdAt' ? 'created_at' : sortBy === 'updatedAt' ? 'updated_at' : sortBy];
      let bValue = b[sortBy === 'createdAt' ? 'created_at' : sortBy === 'updatedAt' ? 'updated_at' : sortBy];

      if (sortBy === 'created_at' || sortBy === 'updated_at') {
        aValue = new Date(aValue).getTime();
        bValue = new Date(bValue).getTime();
      }

      if (sortOrder === 'desc') {
        return bValue > aValue ? 1 : -1;
      } else {
        return aValue > bValue ? 1 : -1;
      }
    });

    // 分页
    if (params.offset) {
      processes = processes.slice(params.offset);
    }
    if (params.limit) {
      processes = processes.slice(0, params.limit);
    }

    return processes.map(row => ({
      id: row.id,
      name: row.name,
      description: row.description,
      creatorName: row.creator_name,
      creatorId: row.creator_id,
      createdAt: new Date(row.created_at),
      updatedAt: new Date(row.updated_at),
      status: row.status,
      totalSteps: row.total_steps,
      currentStep: row.current_step,
      estimatedDuration: row.estimated_duration,
      actualDuration: row.actual_duration,
      startTime: row.start_time ? new Date(row.start_time) : undefined,
      endTime: row.end_time ? new Date(row.end_time) : undefined,
      metadata: row.metadata ? JSON.parse(row.metadata) : {}
    }));
  }

  /**
   * 获取单个工艺流程（包含工步）
   */
  async getProcess(id: string): Promise<Process | null> {
    // 获取工艺流程基本信息
    const processRow = this.storage.getById<any>('PROCESSES', id);

    if (!processRow) {
      return null;
    }

    // 获取工步信息
    const stepRows = this.storage.query<any>('PROCESS_STEPS', (step) => step.process_id === id);

    // 按步骤顺序排序
    stepRows.sort((a, b) => a.step_order - b.step_order);

    const steps: ProcessStep[] = stepRows.map(row => ({
      id: row.id,
      processId: row.process_id,
      stepId: row.step_id,
      stepOrder: row.step_order,
      stepData: JSON.parse(row.step_data),
      createdAt: new Date(row.created_at)
    }));

    return {
      id: processRow.id,
      name: processRow.name,
      description: processRow.description,
      creatorName: processRow.creator_name,
      creatorId: processRow.creator_id,
      createdAt: new Date(processRow.created_at),
      updatedAt: new Date(processRow.updated_at),
      status: processRow.status,
      totalSteps: processRow.total_steps,
      currentStep: processRow.current_step,
      estimatedDuration: processRow.estimated_duration,
      actualDuration: processRow.actual_duration,
      startTime: processRow.start_time ? new Date(processRow.start_time) : undefined,
      endTime: processRow.end_time ? new Date(processRow.end_time) : undefined,
      metadata: processRow.metadata ? JSON.parse(processRow.metadata) : {},
      steps
    };
  }

  /**
   * 计算预计执行时长
   */
  private calculateEstimatedDuration(steps: Step[]): number {
    if (!steps || steps.length === 0) {
      return 0;
    }

    return steps.reduce((total, step) => {
      // 解析时长字符串 (格式: "hh:mm:ss.ms")
      const duration = step.parameters.time.duration.value;
      const timeMatch = duration.match(/^(\d{1,2}):(\d{1,2}):(\d{1,2})(\.\d{1,3})?$/);
      
      if (!timeMatch) return total;

      const hours = parseInt(timeMatch[1], 10);
      const minutes = parseInt(timeMatch[2], 10);
      const seconds = parseInt(timeMatch[3], 10);
      const milliseconds = timeMatch[4] ? parseFloat(timeMatch[4]) * 1000 : 0;

      const stepDuration = hours * 3600 + minutes * 60 + seconds + milliseconds / 1000;
      return total + stepDuration;
    }, 0);
  }

  /**
   * 向工艺流程添加工步
   */
  async addStepToProcess(processId: string, step: Step): Promise<ProcessStep> {
    const stepId = uuidv4();
    const now = new Date();

    // 获取当前工艺流程的工步数量，确定新工步的顺序
    const existingSteps = this.storage.query<any>('PROCESS_STEPS', (s) => s.process_id === processId);
    const nextOrder = existingSteps.length > 0 ? Math.max(...existingSteps.map(s => s.step_order)) + 1 : 1;

    // 插入新工步
    const stepData = {
      id: stepId,
      process_id: processId,
      step_id: step.id,
      step_order: nextOrder,
      step_data: JSON.stringify(step),
      created_at: now.toISOString()
    };

    this.storage.insert('PROCESS_STEPS', stepData);

    // 更新工艺流程的工步总数
    this.storage.update('PROCESSES', processId, {
      total_steps: existingSteps.length + 1,
      updated_at: now.toISOString()
    });

    return {
      id: stepId,
      processId,
      stepId: step.id,
      stepOrder: nextOrder,
      stepData: step,
      createdAt: now
    };
  }

  /**
   * 更新工艺流程中的工步
   */
  async updateStepInProcess(processId: string, stepId: string, step: Step): Promise<boolean> {
    const now = new Date();

    // 查找要更新的工步
    const existingSteps = this.storage.query<any>('PROCESS_STEPS',
      (s) => s.process_id === processId && s.step_id === stepId
    );

    if (existingSteps.length === 0) {
      return false;
    }

    const existingStep = existingSteps[0];

    // 更新工步数据
    const success = this.storage.update('PROCESS_STEPS', existingStep.id, {
      step_data: JSON.stringify(step),
      created_at: now.toISOString()
    });

    if (success) {
      // 更新工艺流程的修改时间
      this.storage.update('PROCESSES', processId, {
        updated_at: now.toISOString()
      });
      return true;
    }

    return false;
  }

  /**
   * 从工艺流程中删除工步
   */
  async removeStepFromProcess(processId: string, stepId: string): Promise<boolean> {
    const now = new Date();

    // 查找要删除的工步
    const existingSteps = this.storage.query<any>('PROCESS_STEPS',
      (s) => s.process_id === processId && s.step_id === stepId
    );

    if (existingSteps.length === 0) {
      return false;
    }

    const existingStep = existingSteps[0];

    // 删除工步
    const success = this.storage.delete('PROCESS_STEPS', existingStep.id);

    if (success) {
      // 获取剩余工步数量
      const remainingSteps = this.storage.query<any>('PROCESS_STEPS',
        (s) => s.process_id === processId
      );

      // 更新工艺流程的工步总数和修改时间
      this.storage.update('PROCESSES', processId, {
        total_steps: remainingSteps.length,
        updated_at: now.toISOString()
      });
      return true;
    }

    return false;
  }

  /**
   * 删除工艺流程
   */
  async deleteProcess(id: string): Promise<boolean> {
    try {
      // 删除工艺流程相关的工步
      const processSteps = this.storage.query<any>('PROCESS_STEPS',
        (s) => s.process_id === id
      );

      processSteps.forEach(step => {
        this.storage.delete('PROCESS_STEPS', step.id);
      });

      // 删除工艺流程执行记录
      const executions = this.storage.query<any>('PROCESS_EXECUTIONS',
        (e) => e.process_id === id
      );

      executions.forEach(execution => {
        this.storage.delete('PROCESS_EXECUTIONS', execution.id);
      });

      // 删除工艺流程本身
      const success = this.storage.delete('PROCESSES', id);

      return success;
    } catch (error) {
      console.error('删除工艺流程失败:', error);
      return false;
    }
  }

  /**
   * 验证工艺流程
   */
  private validateProcess(data: ProcessImportData): ProcessValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    // 验证基本信息
    if (!data.name || data.name.trim().length === 0) {
      errors.push('工艺流程名称不能为空');
    }

    if (data.name && data.name.length > 100) {
      errors.push('工艺流程名称不能超过100个字符');
    }

    // 验证工步（允许空工艺流程）
    // 注释掉工步数量验证，允许创建空的工艺流程
    // if (!data.steps || data.steps.length === 0) {
    //   errors.push('工艺流程必须包含至少一个工步');
    // }

    if (data.steps && data.steps.length > 1000) {
      warnings.push('工艺流程包含过多工步，可能影响性能');
    }

    // 验证工步数据（仅当有工步时）
    if (data.steps && data.steps.length > 0) {
      data.steps.forEach((step, index) => {
        if (!step.name || step.name.trim().length === 0) {
          errors.push(`第${index + 1}个工步名称不能为空`);
        }

        if (!step.parameters?.electrical?.current?.value) {
          errors.push(`第${index + 1}个工步缺少电流参数`);
        }

        if (!step.parameters?.time?.duration?.value) {
          errors.push(`第${index + 1}个工步缺少时长参数`);
        }
      });
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }
}

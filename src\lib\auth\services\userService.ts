/**
 * 用户服务 - 模拟实现
 */

import { User, UserRole, UserPermissions, LoginCredentials } from '../types/user';

// 模拟用户数据
const mockUsers: User[] = [
  {
    id: 'user_001',
    username: 'admin',
    name: '系统管理员',
    email: '<EMAIL>',
    role: 'admin',
    department: '技术部',
    avatar: undefined,
    createdAt: new Date('2024-01-01'),
    lastLoginAt: new Date(),
    isActive: true
  },
  {
    id: 'user_002',
    username: 'engineer01',
    name: '张工程师',
    email: '<EMAIL>',
    role: 'engineer',
    department: '研发部',
    avatar: undefined,
    createdAt: new Date('2024-01-15'),
    lastLoginAt: new Date(),
    isActive: true
  },
  {
    id: 'user_003',
    username: 'operator01',
    name: '李操作员',
    email: '<EMAIL>',
    role: 'operator',
    department: '生产部',
    avatar: undefined,
    createdAt: new Date('2024-02-01'),
    lastLoginAt: new Date(),
    isActive: true
  }
];

// 当前登录用户（模拟）
let currentUser: User | null = mockUsers[1]; // 默认为张工程师

// 角色权限映射
const rolePermissions: Record<UserRole, UserPermissions> = {
  admin: {
    canCreateProcess: true,
    canEditProcess: true,
    canDeleteProcess: true,
    canExecuteProcess: true,
    canViewProcess: true,
    canManageUsers: true,
    canManageDevices: true,
    canViewReports: true
  },
  engineer: {
    canCreateProcess: true,
    canEditProcess: true,
    canDeleteProcess: true,
    canExecuteProcess: true,
    canViewProcess: true,
    canManageUsers: false,
    canManageDevices: false,
    canViewReports: true
  },
  operator: {
    canCreateProcess: false,
    canEditProcess: false,
    canDeleteProcess: false,
    canExecuteProcess: true,
    canViewProcess: true,
    canManageUsers: false,
    canManageDevices: false,
    canViewReports: false
  },
  viewer: {
    canCreateProcess: false,
    canEditProcess: false,
    canDeleteProcess: false,
    canExecuteProcess: false,
    canViewProcess: true,
    canManageUsers: false,
    canManageDevices: false,
    canViewReports: false
  }
};

export class UserService {
  /**
   * 获取当前登录用户
   */
  static getCurrentUser(): User | null {
    return currentUser;
  }

  /**
   * 模拟登录
   */
  static async login(credentials: LoginCredentials): Promise<User> {
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 500));

    const user = mockUsers.find(u => u.username === credentials.username);
    if (!user) {
      throw new Error('用户名或密码错误');
    }

    // 更新最后登录时间
    user.lastLoginAt = new Date();
    currentUser = user;

    return user;
  }

  /**
   * 登出
   */
  static logout(): void {
    currentUser = null;
  }

  /**
   * 检查用户权限
   */
  static hasPermission(permission: keyof UserPermissions): boolean {
    if (!currentUser) {
      return false;
    }

    const permissions = rolePermissions[currentUser.role];
    return permissions[permission];
  }

  /**
   * 获取用户权限
   */
  static getUserPermissions(): UserPermissions | null {
    if (!currentUser) {
      return null;
    }

    return rolePermissions[currentUser.role];
  }

  /**
   * 获取所有用户（管理员功能）
   */
  static async getAllUsers(): Promise<User[]> {
    if (!this.hasPermission('canManageUsers')) {
      throw new Error('权限不足');
    }

    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 300));
    return [...mockUsers];
  }

  /**
   * 切换用户（开发/测试用）
   */
  static switchUser(userId: string): boolean {
    const user = mockUsers.find(u => u.id === userId);
    if (user) {
      currentUser = user;
      return true;
    }
    return false;
  }

  /**
   * 获取用户显示名称
   */
  static getUserDisplayName(user?: User): string {
    const targetUser = user || currentUser;
    if (!targetUser) {
      return '未知用户';
    }

    return targetUser.name || targetUser.username;
  }

  /**
   * 检查是否已登录
   */
  static isAuthenticated(): boolean {
    return currentUser !== null && currentUser.isActive;
  }

  /**
   * 刷新用户信息
   */
  static async refreshUser(): Promise<User | null> {
    if (!currentUser) {
      return null;
    }

    // 模拟从服务器刷新用户信息
    await new Promise(resolve => setTimeout(resolve, 200));
    
    // 在实际应用中，这里会从服务器获取最新的用户信息
    const updatedUser = mockUsers.find(u => u.id === currentUser!.id);
    if (updatedUser) {
      currentUser = updatedUser;
    }

    return currentUser;
  }
}

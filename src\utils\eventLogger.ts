import { useEventStore } from '@/stores/eventStore';
import { 
  EventType, 
  EventLevel, 
  EventCategory, 
  EventSource, 
  EventData 
} from '@/types/events';

// 事件日志记录器类
export class EventLogger {
  private static instance: EventLogger;
  
  private constructor() {}
  
  public static getInstance(): EventLogger {
    if (!EventLogger.instance) {
      EventLogger.instance = new EventLogger();
    }
    return EventLogger.instance;
  }
  
  // 记录设备事件
  public logDeviceEvent(
    type: EventType,
    level: EventLevel,
    deviceName: string,
    title: string,
    description: string,
    data?: EventData
  ) {
    const source: EventSource = {
      type: 'device',
      deviceName,
    };
    
    this.logEvent(type, EventCategory.DEVICE, level, source, title, description, data);
  }
  
  // 记录通道事件
  public logChannelEvent(
    type: EventType,
    level: EventLevel,
    deviceName: string,
    channelNumber: number,
    title: string,
    description: string,
    data?: EventData
  ) {
    const source: EventSource = {
      type: 'channel',
      deviceName,
      channelNumber,
    };
    
    this.logEvent(type, EventCategory.CHANNEL, level, source, title, description, data);
  }
  
  // 记录用户事件
  public logUserEvent(
    type: EventType,
    level: EventLevel,
    userId: string,
    userName: string,
    title: string,
    description: string,
    data?: EventData
  ) {
    const source: EventSource = {
      type: 'user',
      userId,
      userName,
    };
    
    this.logEvent(type, EventCategory.USER, level, source, title, description, data);
  }
  
  // 记录系统事件
  public logSystemEvent(
    type: EventType,
    level: EventLevel,
    systemComponent: string,
    title: string,
    description: string,
    data?: EventData
  ) {
    const source: EventSource = {
      type: 'system',
      systemComponent,
    };
    
    this.logEvent(type, EventCategory.SYSTEM, level, source, title, description, data);
  }
  
  // 通用事件记录方法
  private logEvent(
    type: EventType,
    category: EventCategory,
    level: EventLevel,
    source: EventSource,
    title: string,
    description: string,
    data?: EventData
  ) {
    // 在客户端环境中记录事件
    if (typeof window !== 'undefined') {
      const { addEvent } = useEventStore.getState();
      
      addEvent({
        type,
        category,
        level,
        source,
        title,
        description,
        data,
      });
    }
  }
}

// 导出单例实例
export const eventLogger = EventLogger.getInstance();

// 便捷方法
export const logDeviceOnline = (deviceName: string) => {
  eventLogger.logDeviceEvent(
    EventType.DEVICE_ONLINE,
    EventLevel.INFO,
    deviceName,
    `设备${deviceName}上线`,
    `设备${deviceName}已成功连接到系统`
  );
};

export const logDeviceOffline = (deviceName: string) => {
  eventLogger.logDeviceEvent(
    EventType.DEVICE_OFFLINE,
    EventLevel.WARNING,
    deviceName,
    `设备${deviceName}离线`,
    `设备${deviceName}失去连接，请检查网络状态`
  );
};

export const logChannelEnabled = (deviceName: string, channelNumber: number) => {
  eventLogger.logChannelEvent(
    EventType.CHANNEL_ENABLED,
    EventLevel.INFO,
    deviceName,
    channelNumber,
    `通道${channelNumber}已启用`,
    `设备${deviceName}的通道${channelNumber}已被启用`
  );
};

export const logChannelDisabled = (deviceName: string, channelNumber: number) => {
  eventLogger.logChannelEvent(
    EventType.CHANNEL_DISABLED,
    EventLevel.WARNING,
    deviceName,
    channelNumber,
    `通道${channelNumber}已禁用`,
    `设备${deviceName}的通道${channelNumber}已被禁用`
  );
};

export const logChannelParameterExceeded = (
  deviceName: string, 
  channelNumber: number, 
  parameter: string,
  currentValue: number,
  threshold: number
) => {
  eventLogger.logChannelEvent(
    EventType.CHANNEL_PARAMETER_EXCEEDED,
    EventLevel.ERROR,
    deviceName,
    channelNumber,
    `通道${channelNumber}${parameter}超限`,
    `设备${deviceName}的通道${channelNumber}${parameter}超过安全阈值`,
    {
      currentValue,
      threshold,
      parameters: { parameter },
    }
  );
};

export const logChannelStepChanged = (
  deviceName: string, 
  channelNumber: number, 
  fromStep: string,
  toStep: string
) => {
  eventLogger.logChannelEvent(
    EventType.CHANNEL_STEP_CHANGED,
    EventLevel.INFO,
    deviceName,
    channelNumber,
    `通道${channelNumber}工步变更`,
    `设备${deviceName}的通道${channelNumber}工步从${fromStep}变更为${toStep}`,
    {
      fromStatus: fromStep,
      toStatus: toStep,
    }
  );
};

export const logUserLogin = (userId: string, userName: string) => {
  eventLogger.logUserEvent(
    EventType.USER_LOGIN,
    EventLevel.INFO,
    userId,
    userName,
    `用户${userName}登录`,
    `用户${userName}成功登录系统`
  );
};

export const logUserLogout = (userId: string, userName: string) => {
  eventLogger.logUserEvent(
    EventType.USER_LOGOUT,
    EventLevel.INFO,
    userId,
    userName,
    `用户${userName}登出`,
    `用户${userName}已登出系统`
  );
};

export const logUserDeviceControl = (
  userId: string, 
  userName: string, 
  deviceName: string, 
  operation: string
) => {
  eventLogger.logUserEvent(
    EventType.USER_DEVICE_CONTROL,
    EventLevel.INFO,
    userId,
    userName,
    `用户${userName}控制设备`,
    `用户${userName}对设备${deviceName}执行了${operation}操作`,
    {
      operation,
      target: deviceName,
    }
  );
};

export const logUserChannelOperation = (
  userId: string, 
  userName: string, 
  deviceName: string, 
  channelNumber: number,
  operation: string
) => {
  eventLogger.logUserEvent(
    EventType.USER_CHANNEL_OPERATION,
    EventLevel.INFO,
    userId,
    userName,
    `用户${userName}操作通道`,
    `用户${userName}对设备${deviceName}的通道${channelNumber}执行了${operation}操作`,
    {
      operation,
      target: `${deviceName}-通道${channelNumber}`,
    }
  );
};

export const logSystemError = (
  component: string, 
  errorCode: string, 
  errorMessage: string
) => {
  eventLogger.logSystemEvent(
    EventType.SYSTEM_ERROR,
    EventLevel.CRITICAL,
    component,
    `系统错误`,
    `${component}组件发生错误：${errorMessage}`,
    {
      errorCode,
      errorMessage,
    }
  );
};

export const logSystemWarning = (
  component: string, 
  message: string
) => {
  eventLogger.logSystemEvent(
    EventType.SYSTEM_WARNING,
    EventLevel.WARNING,
    component,
    `系统警告`,
    `${component}组件警告：${message}`
  );
};

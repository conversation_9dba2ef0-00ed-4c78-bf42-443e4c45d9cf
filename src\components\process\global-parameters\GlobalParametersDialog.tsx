'use client';

import React, { useState } from 'react';
import { Settings, ChevronDown, ChevronRight } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';

import { GlobalParameters, DEFAULT_GLOBAL_PARAMETERS, GLOBAL_PARAM_GROUP_LABELS } from '@/lib/process/types/globalParameters';

interface GlobalParametersDialogProps {
  trigger?: React.ReactNode;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  initialParameters?: GlobalParameters;
  onSave?: (parameters: GlobalParameters) => void;
  className?: string;
}

export function GlobalParametersDialog({
  trigger,
  open: externalOpen,
  onOpenChange: externalOnOpenChange,
  initialParameters = DEFAULT_GLOBAL_PARAMETERS,
  onSave,
  className
}: GlobalParametersDialogProps) {
  const [internalOpen, setInternalOpen] = useState(false);
  const [parameters, setParameters] = useState<GlobalParameters>(initialParameters);
  const [expandedGroups, setExpandedGroups] = useState<Record<string, boolean>>({
    protectionParams: true,
    displayParams: false,
    temperatureCompensation: false,
    stepTemperatureCompensation: false,
    processMatchingRules: false,
    sortingConditions: false
  });

  // 使用外部控制的open状态，如果没有则使用内部状态
  const isOpen = externalOpen !== undefined ? externalOpen : internalOpen;
  const setIsOpen = externalOnOpenChange || setInternalOpen;

  const toggleGroup = (groupKey: string) => {
    setExpandedGroups(prev => ({
      ...prev,
      [groupKey]: !prev[groupKey]
    }));
  };

  const handleSave = () => {
    onSave?.(parameters);
    setIsOpen(false);
  };

  const handleReset = () => {
    setParameters(DEFAULT_GLOBAL_PARAMETERS);
  };

  // 渲染保护参数组
  const renderProtectionParams = () => (
    <div className="space-y-4">
      <div className="grid grid-cols-2 gap-4">
        <div>
          <Label htmlFor="alarmCapacity">报警容量 (mAh)</Label>
          <Input
            id="alarmCapacity"
            type="number"
            value={parameters.protectionParams.alarmSettings.alarmCapacity.value}
            onChange={(e) => setParameters(prev => ({
              ...prev,
              protectionParams: {
                ...prev.protectionParams,
                alarmSettings: {
                  ...prev.protectionParams.alarmSettings,
                  alarmCapacity: {
                    ...prev.protectionParams.alarmSettings.alarmCapacity,
                    value: Number(e.target.value)
                  }
                }
              }
            }))}
            min={0}
            max={50000}
          />
        </div>
        <div>
          <Label htmlFor="ccVoltageFluctuation">恒流电压波动 (mV)</Label>
          <Input
            id="ccVoltageFluctuation"
            type="number"
            value={parameters.protectionParams.alarmSettings.ccVoltageFluctuation.value}
            onChange={(e) => setParameters(prev => ({
              ...prev,
              protectionParams: {
                ...prev.protectionParams,
                alarmSettings: {
                  ...prev.protectionParams.alarmSettings,
                  ccVoltageFluctuation: {
                    ...prev.protectionParams.alarmSettings.ccVoltageFluctuation,
                    value: Number(e.target.value)
                  }
                }
              }
            }))}
            min={0}
            max={1000}
          />
        </div>
      </div>
      
      <div className="flex items-center space-x-2">
        <Checkbox
          id="openCircuitDetection"
          checked={parameters.protectionParams.abnormalDetection.openCircuitDetection.value}
          onCheckedChange={(checked) => setParameters(prev => ({
            ...prev,
            protectionParams: {
              ...prev.protectionParams,
              abnormalDetection: {
                ...prev.protectionParams.abnormalDetection,
                openCircuitDetection: {
                  ...prev.protectionParams.abnormalDetection.openCircuitDetection,
                  value: checked as boolean
                }
              }
            }
          }))}
        />
        <Label htmlFor="openCircuitDetection">开路检测</Label>
      </div>

      <div>
        <Label htmlFor="alarmTemperatureLimit">报警温度限制 (℃)</Label>
        <Input
          id="alarmTemperatureLimit"
          type="number"
          value={parameters.protectionParams.temperatureMonitoring.alarmTemperatureLimit.value}
          onChange={(e) => setParameters(prev => ({
            ...prev,
            protectionParams: {
              ...prev.protectionParams,
              temperatureMonitoring: {
                ...prev.protectionParams.temperatureMonitoring,
                alarmTemperatureLimit: {
                  ...prev.protectionParams.temperatureMonitoring.alarmTemperatureLimit,
                  value: Number(e.target.value)
                }
              }
            }
          }))}
          min={-40}
          max={100}
        />
      </div>
    </div>
  );

  // 渲染显示设置组
  const renderDisplayParams = () => (
    <div className="space-y-4">
      <div className="flex items-center space-x-2">
        <Checkbox
          id="timeCondition"
          checked={parameters.displayParams.endConditions.timeCondition.value}
          onCheckedChange={(checked) => setParameters(prev => ({
            ...prev,
            displayParams: {
              ...prev.displayParams,
              endConditions: {
                ...prev.displayParams.endConditions,
                timeCondition: {
                  ...prev.displayParams.endConditions.timeCondition,
                  value: checked as boolean
                }
              }
            }
          }))}
        />
        <Label htmlFor="timeCondition">时间结束条件</Label>
      </div>

      <div className="flex items-center space-x-2">
        <Checkbox
          id="voltageDiffRecord"
          checked={parameters.displayParams.recordConditions.voltageDiffRecord.value}
          onCheckedChange={(checked) => setParameters(prev => ({
            ...prev,
            displayParams: {
              ...prev.displayParams,
              recordConditions: {
                ...prev.displayParams.recordConditions,
                voltageDiffRecord: {
                  ...prev.displayParams.recordConditions.voltageDiffRecord,
                  value: checked as boolean
                }
              }
            }
          }))}
        />
        <Label htmlFor="voltageDiffRecord">电压差记录条件</Label>
      </div>
    </div>
  );

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        {trigger || (
          <Button variant="outline" className={className}>
            <Settings className="mr-2 h-4 w-4" />
            全局参数设置
          </Button>
        )}
      </DialogTrigger>
      
      <DialogContent className="max-w-4xl max-h-[90vh] flex flex-col">
        <DialogHeader className="flex-shrink-0">
          <DialogTitle>全局参数设置</DialogTitle>
          <DialogDescription>
            配置工艺流程的全局参数，包括保护参数、显示设置、温度补偿等
          </DialogDescription>
        </DialogHeader>

        <div className="flex-1 overflow-y-auto space-y-4 pr-2" style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}>
          <style jsx>{`
            div::-webkit-scrollbar {
              display: none;
            }
          `}</style>

          {/* 保护参数组 */}
          <Collapsible open={expandedGroups.protectionParams} onOpenChange={() => toggleGroup('protectionParams')}>
            <Card>
              <CollapsibleTrigger asChild>
                <CardHeader className="cursor-pointer hover:bg-muted/50 transition-colors">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <CardTitle className="text-lg">{GLOBAL_PARAM_GROUP_LABELS.protectionParams}</CardTitle>
                      <Badge variant="secondary">必需</Badge>
                    </div>
                    {expandedGroups.protectionParams ? <ChevronDown className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />}
                  </div>
                  <CardDescription>
                    硬件级安全保护机制，实时监控异常状态并触发保护动作
                  </CardDescription>
                </CardHeader>
              </CollapsibleTrigger>
              <CollapsibleContent>
                <CardContent>
                  {renderProtectionParams()}
                </CardContent>
              </CollapsibleContent>
            </Card>
          </Collapsible>

          {/* 显示设置组 */}
          <Collapsible open={expandedGroups.displayParams} onOpenChange={() => toggleGroup('displayParams')}>
            <Card>
              <CollapsibleTrigger asChild>
                <CardHeader className="cursor-pointer hover:bg-muted/50 transition-colors">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <CardTitle className="text-lg">{GLOBAL_PARAM_GROUP_LABELS.displayParams}</CardTitle>
                      <Badge variant="outline">可选</Badge>
                    </div>
                    {expandedGroups.displayParams ? <ChevronDown className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />}
                  </div>
                  <CardDescription>
                    控制测试结果数据的显示和记录条件
                  </CardDescription>
                </CardHeader>
              </CollapsibleTrigger>
              <CollapsibleContent>
                <CardContent>
                  {renderDisplayParams()}
                </CardContent>
              </CollapsibleContent>
            </Card>
          </Collapsible>

          {/* 其他参数组占位符 */}
          <Card className="opacity-60">
            <CardHeader>
              <CardTitle className="text-lg">温度补偿和其他高级设置</CardTitle>
              <CardDescription>
                温度容量补偿、工步温度补偿、流程匹配规则、分选条件等高级功能
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center text-muted-foreground py-8">
                <Settings className="h-8 w-8 mx-auto mb-2 opacity-50" />
                <p>高级参数设置功能开发中...</p>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* 操作按钮 - 固定在底部 */}
        <div className="flex-shrink-0 flex items-center justify-between pt-4 border-t">
          <Button variant="outline" onClick={handleReset}>
            重置为默认值
          </Button>

          <div className="flex items-center space-x-2">
            <Button variant="outline" onClick={() => setIsOpen(false)}>
              取消
            </Button>
            <Button onClick={handleSave}>
              保存设置
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}

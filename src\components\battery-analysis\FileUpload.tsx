'use client';

import React, { useCallback, useState } from 'react';
import { useDropzone } from 'react-dropzone';
import { Upload, FileText, AlertCircle, CheckCircle2, X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { useBatteryDataImport } from '@/lib/hooks/useBatteryDataImport';
import { BatteryDataSet } from '@/lib/types';
import { useTranslations } from 'next-intl';

interface FileUploadProps {
  onUploadSuccess: (dataSet: BatteryDataSet) => void;
  onUploadError?: (error: string) => void;
  maxFileSize?: number; // MB
  acceptedFileTypes?: string[];
}

export function FileUpload({
  onUploadSuccess,
  onUploadError,
  maxFileSize = 50,
  acceptedFileTypes = ['.csv']
}: FileUploadProps) {
  const t = useTranslations('batteryAnalysis');
  const { importState, importFile, resetImport } = useBatteryDataImport();
  const [dragActive, setDragActive] = useState(false);

  const onDrop = useCallback(async (acceptedFiles: File[]) => {
    if (acceptedFiles.length === 0) return;

    const file = acceptedFiles[0];
    
    try {
      const result = await importFile(file);
      if (result) {
        onUploadSuccess(result);
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '上传失败';
      onUploadError?.(errorMessage);
    }
  }, [importFile, onUploadSuccess, onUploadError]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'text/csv': acceptedFileTypes
    },
    maxFiles: 1,
    maxSize: maxFileSize * 1024 * 1024,
    onDragEnter: () => setDragActive(true),
    onDragLeave: () => setDragActive(false),
    onDropAccepted: () => setDragActive(false),
    onDropRejected: (rejectedFiles) => {
      setDragActive(false);
      const file = rejectedFiles[0];
      if (file.errors.length > 0) {
        const error = file.errors[0];
        let errorMessage = '文件上传失败';
        
        switch (error.code) {
          case 'file-too-large':
            errorMessage = `文件大小不能超过 ${maxFileSize}MB`;
            break;
          case 'file-invalid-type':
            errorMessage = '请选择CSV格式的文件';
            break;
          default:
            errorMessage = error.message;
        }
        
        onUploadError?.(errorMessage);
      }
    }
  });

  const handleReset = () => {
    resetImport();
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Upload className="h-5 w-5" />
          {t('fileUpload.title')}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* 上传区域 */}
        {!importState.result && (
          <div
            {...getRootProps()}
            className={`
              border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors
              ${isDragActive || dragActive 
                ? 'border-primary bg-primary/5' 
                : 'border-muted-foreground/25 hover:border-primary/50'
              }
              ${importState.isLoading ? 'pointer-events-none opacity-50' : ''}
            `}
          >
            <input {...getInputProps()} />
            
            <div className="flex flex-col items-center gap-4">
              <div className="p-4 rounded-full bg-muted">
                <FileText className="h-8 w-8 text-muted-foreground" />
              </div>
              
              <div className="space-y-2">
                <p className="text-lg font-medium">
                  {isDragActive || dragActive 
                    ? t('fileUpload.dropHere')
                    : t('fileUpload.dragDrop')
                  }
                </p>
                <p className="text-sm text-muted-foreground">
                  {t('fileUpload.supportedFormats')}: {acceptedFileTypes.join(', ')}
                </p>
                <p className="text-xs text-muted-foreground">
                  {t('fileUpload.maxSize')}: {maxFileSize}MB
                </p>
              </div>
              
              <Button variant="outline" disabled={importState.isLoading}>
                <Upload className="h-4 w-4 mr-2" />
                {t('fileUpload.selectFile')}
              </Button>
            </div>
          </div>
        )}

        {/* 上传进度 */}
        {importState.isLoading && (
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">{t('fileUpload.uploading')}</span>
              <span className="text-sm text-muted-foreground">{importState.progress}%</span>
            </div>
            <Progress value={importState.progress} className="w-full" />
          </div>
        )}

        {/* 错误信息 */}
        {importState.error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{importState.error}</AlertDescription>
          </Alert>
        )}

        {/* 成功结果 */}
        {importState.result && (
          <div className="space-y-4">
            <Alert>
              <CheckCircle2 className="h-4 w-4" />
              <AlertDescription>
                {t('fileUpload.success')}
              </AlertDescription>
            </Alert>
            
            <Card>
              <CardContent className="pt-6">
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <h4 className="font-medium">{importState.result.name}</h4>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={handleReset}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-muted-foreground">{t('fileUpload.recordCount')}:</span>
                      <Badge variant="secondary" className="ml-2">
                        {importState.result.recordCount.toLocaleString()}
                      </Badge>
                    </div>
                    <div>
                      <span className="text-muted-foreground">{t('fileUpload.fileSize')}:</span>
                      <Badge variant="secondary" className="ml-2">
                        {(importState.result.fileSize / 1024).toFixed(1)} KB
                      </Badge>
                    </div>
                    <div>
                      <span className="text-muted-foreground">{t('fileUpload.cycleRange')}:</span>
                      <Badge variant="secondary" className="ml-2">
                        {importState.result.metadata.cycleRange?.start} - {importState.result.metadata.cycleRange?.end}
                      </Badge>
                    </div>
                    <div>
                      <span className="text-muted-foreground">{t('fileUpload.uploadTime')}:</span>
                      <Badge variant="secondary" className="ml-2">
                        {importState.result.uploadTime.toLocaleTimeString()}
                      </Badge>
                    </div>
                  </div>
                  
                  {importState.result.description && (
                    <p className="text-sm text-muted-foreground">
                      {importState.result.description}
                    </p>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

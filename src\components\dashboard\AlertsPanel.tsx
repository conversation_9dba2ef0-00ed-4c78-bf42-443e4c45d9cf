'use client';

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  AlertTriangle, 
  Info, 
  AlertCircle,
  Check,
  Clock,
  X
} from 'lucide-react';
import { AlertData } from '@/lib/types/dashboard';
import { formatDistanceToNow } from 'date-fns';
import { zhCN } from 'date-fns/locale';
import { cn } from '@/lib/utils';

interface AlertsPanelProps {
  alerts: AlertData[];
  className?: string;
  onAcknowledge?: (alertId: string) => void;
  onDismiss?: (alertId: string) => void;
}

const ALERT_ICONS = {
  critical: AlertCircle,
  warning: AlertTriangle,
  info: Info
};

const ALERT_COLORS = {
  critical: 'text-red-600 border-red-200 bg-red-50',
  warning: 'text-orange-600 border-orange-200 bg-orange-50',
  info: 'text-blue-600 border-blue-200 bg-blue-50'
};

const ALERT_BADGE_VARIANTS = {
  critical: 'destructive' as const,
  warning: 'secondary' as const,
  info: 'outline' as const
};

export function AlertsPanel({ 
  alerts, 
  className, 
  onAcknowledge, 
  onDismiss 
}: AlertsPanelProps) {
  const unacknowledgedAlerts = alerts.filter(alert => !alert.acknowledged);
  const criticalAlerts = alerts.filter(alert => alert.level === 'critical');
  
  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          系统告警
          <div className="flex items-center space-x-2">
            {criticalAlerts.length > 0 && (
              <Badge variant="destructive" className="text-xs">
                {criticalAlerts.length} 严重
              </Badge>
            )}
            <Badge variant="outline">{unacknowledgedAlerts.length} 未确认</Badge>
          </div>
        </CardTitle>
        <CardDescription>
          系统告警和通知信息
        </CardDescription>
      </CardHeader>
      <CardContent>
        <ScrollArea className="h-[400px] pr-4">
          <div className="space-y-3">
            {alerts.map((alert) => {
              const IconComponent = ALERT_ICONS[alert.level];
              const alertStyle = ALERT_COLORS[alert.level];
              const badgeVariant = ALERT_BADGE_VARIANTS[alert.level];
              
              return (
                <div 
                  key={alert.id} 
                  className={cn(
                    'p-3 rounded-lg border',
                    alertStyle,
                    alert.acknowledged && 'opacity-60'
                  )}
                >
                  <div className="flex items-start space-x-3">
                    <div className="mt-0.5">
                      <IconComponent className="h-4 w-4" />
                    </div>
                    
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between mb-1">
                        <h4 className="text-sm font-medium truncate">
                          {alert.title}
                        </h4>
                        <div className="flex items-center space-x-1">
                          <Badge variant={badgeVariant} className="text-xs">
                            {getLevelLabel(alert.level)}
                          </Badge>
                          {alert.acknowledged && (
                            <Badge variant="outline" className="text-xs">
                              <Check className="h-3 w-3 mr-1" />
                              已确认
                            </Badge>
                          )}
                        </div>
                      </div>
                      
                      <p className="text-sm mb-2">
                        {alert.message}
                      </p>
                      
                      <div className="flex items-center justify-between text-xs">
                        <div className="flex items-center space-x-2">
                          <Clock className="h-3 w-3" />
                          <span>
                            {formatDistanceToNow(alert.timestamp, { 
                              addSuffix: true,
                              locale: zhCN 
                            })}
                          </span>
                        </div>
                        
                        <div className="flex items-center space-x-1">
                          {alert.deviceId && (
                            <Badge variant="outline" className="text-xs">
                              {alert.deviceId}
                            </Badge>
                          )}
                          {alert.channelId && (
                            <Badge variant="outline" className="text-xs">
                              {alert.channelId}
                            </Badge>
                          )}
                          <Badge variant="outline" className="text-xs">
                            {alert.source}
                          </Badge>
                        </div>
                      </div>
                      
                      {!alert.acknowledged && (onAcknowledge || onDismiss) && (
                        <div className="flex items-center space-x-2 mt-2">
                          {onAcknowledge && (
                            <Button 
                              size="sm" 
                              variant="outline" 
                              className="text-xs h-6"
                              onClick={() => onAcknowledge(alert.id)}
                            >
                              <Check className="h-3 w-3 mr-1" />
                              确认
                            </Button>
                          )}
                          {onDismiss && (
                            <Button 
                              size="sm" 
                              variant="outline" 
                              className="text-xs h-6"
                              onClick={() => onDismiss(alert.id)}
                            >
                              <X className="h-3 w-3 mr-1" />
                              忽略
                            </Button>
                          )}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              );
            })}
            
            {alerts.length === 0 && (
              <div className="text-center py-8">
                <Check className="h-8 w-8 text-green-500 mx-auto mb-2" />
                <p className="text-muted-foreground">暂无告警信息</p>
              </div>
            )}
          </div>
        </ScrollArea>
      </CardContent>
    </Card>
  );
}

function getLevelLabel(level: string): string {
  switch (level) {
    case 'critical':
      return '严重';
    case 'warning':
      return '警告';
    case 'info':
      return '信息';
    default:
      return level;
  }
}

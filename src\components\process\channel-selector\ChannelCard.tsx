'use client';

import React from 'react';
import { cn } from '@/lib/utils';
import { 
  Zap, 
  ZapOff, 
  Pause, 
  AlertTriangle, 
  Wifi, 
  WifiOff,
  Settings,
  Activity,
  Thermometer,
  Clock
} from 'lucide-react';

import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

import { BatteryStatusIndicator, SimpleBatteryIcon } from './BatteryIcon';
import { Channel, ChannelStatus, ChannelType } from '@/lib/process/types/channel';

interface ChannelCardProps {
  channel: Channel;
  isSelected: boolean;
  onSelect: (channel: Channel) => void;
  onToggleSelect: (channel: Channel) => void;
  showBatteryInfo?: boolean;
  showRealTimeData?: boolean;
  showStatistics?: boolean;
  compactMode?: boolean;
  multiSelect?: boolean;
  className?: string;
}

export function ChannelCard({
  channel,
  isSelected,
  onSelect,
  onToggleSelect,
  showBatteryInfo = true,
  showRealTimeData = true,
  showStatistics = false,
  compactMode = false,
  multiSelect = false,
  className
}: ChannelCardProps) {

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'running':
        return <Zap className="h-4 w-4 text-green-500" />;
      case 'paused':
        return <Pause className="h-4 w-4 text-yellow-500" />;
      case 'error':
        return <AlertTriangle className="h-4 w-4 text-red-500" />;
      case 'offline':
        return <WifiOff className="h-4 w-4 text-gray-500" />;
      case 'maintenance':
        return <Settings className="h-4 w-4 text-blue-500" />;
      default:
        return <ZapOff className="h-4 w-4 text-gray-400" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'running':
        return 'border-green-500 bg-green-50';
      case 'paused':
        return 'border-yellow-500 bg-yellow-50';
      case 'error':
        return 'border-red-500 bg-red-50';
      case 'offline':
        return 'border-gray-300 bg-gray-50';
      case 'maintenance':
        return 'border-blue-500 bg-blue-50';
      default:
        return 'border-gray-200 bg-white';
    }
  };

  const getStatusBadge = (status: string) => {
    const statusConfig: Record<string, { label: string; variant: any }> = {
      'idle': { label: '空闲', variant: 'secondary' },
      'running': { label: '运行中', variant: 'default' },
      'paused': { label: '暂停', variant: 'outline' },
      'error': { label: '错误', variant: 'destructive' },
      'offline': { label: '离线', variant: 'secondary' },
      'maintenance': { label: '维护', variant: 'outline' }
    };

    const config = statusConfig[status] || { label: status, variant: 'secondary' };
    return (
      <Badge variant={config.variant} className="text-xs">
        {config.label}
      </Badge>
    );
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'charge':
        return '⚡';
      case 'discharge':
        return '🔋';
      case 'hybrid':
        return '⚡🔋';
      default:
        return '📱';
    }
  };

  const handleClick = () => {
    if (multiSelect) {
      onToggleSelect(channel);
    } else {
      onSelect(channel);
    }
  };

  const formatValue = (value: number, unit: string, precision: number = 1) => {
    return `${value.toFixed(precision)} ${unit}`;
  };

  const formatTime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    return `${hours}h ${minutes}m`;
  };

  return (
    <TooltipProvider>
      <Card
        className={cn(
          'cursor-pointer transition-all duration-200 hover:shadow-md',
          'min-h-[120px] w-full', // 确保最小高度和宽度
          isSelected
            ? 'ring-2 ring-primary ring-offset-2 shadow-lg'
            : 'hover:ring-1 hover:ring-primary/50',
          getStatusColor(channel.status),
          compactMode ? 'min-h-[80px]' : 'min-h-[120px]', // 紧凑模式下的最小高度
          className
        )}
        onClick={handleClick}
      >
        <CardContent className={cn('space-y-3', compactMode ? 'p-3' : 'p-4')}>
          {/* 头部信息 */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="flex items-center space-x-2">
                <span className="text-lg font-bold text-primary">
                  {channel.channelNumber}
                </span>
                <span className="text-xs text-muted-foreground">
                  {getTypeIcon(channel.type)}
                </span>
              </div>
              {!compactMode && (
                <div className="text-sm font-medium truncate max-w-24">
                  {channel.name}
                </div>
              )}
            </div>

            <div className="flex items-center space-x-2">
              {getStatusIcon(channel.status)}
              {channel.status === 'offline' && (
                <WifiOff className="h-3 w-3 text-gray-400" />
              )}
            </div>
          </div>

          {/* 状态和类型 */}
          <div className="flex items-center justify-between gap-2">
            {getStatusBadge(channel.status)}
            {!compactMode && (
              <Badge variant="outline" className="text-xs">
                {channel.type}
              </Badge>
            )}
          </div>

          {/* 电池信息 */}
          {showBatteryInfo && channel.batteryInfo && (
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <BatteryStatusIndicator
                  level={channel.batteryInfo.soc}
                  charging={channel.status === 'running' && channel.type !== 'discharge'}
                  size="sm"
                />
                <div className="text-xs text-muted-foreground">
                  {channel.batteryInfo.name}
                </div>
              </div>
              
              {!compactMode && (
                <div className="grid grid-cols-2 gap-1 text-xs">
                  <div>
                    <span className="text-muted-foreground">容量:</span>
                    <span className="ml-1 font-medium">
                      {channel.batteryInfo.capacity}mAh
                    </span>
                  </div>
                  <div>
                    <span className="text-muted-foreground">循环:</span>
                    <span className="ml-1 font-medium">
                      {channel.batteryInfo.cycleCount}
                    </span>
                  </div>
                </div>
              )}
            </div>
          )}

          {/* 实时数据 */}
          {showRealTimeData && channel.realTimeData && (
            <div className="space-y-2">
              <div className="grid grid-cols-2 gap-2 text-xs">
                <Tooltip>
                  <TooltipTrigger asChild>
                    <div className="flex items-center space-x-1">
                      <Activity className="h-3 w-3 text-blue-500" />
                      <span className="font-mono">
                        {formatValue(channel.realTimeData.current, 'mA', 0)}
                      </span>
                    </div>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>当前电流</p>
                  </TooltipContent>
                </Tooltip>

                <Tooltip>
                  <TooltipTrigger asChild>
                    <div className="flex items-center space-x-1">
                      <Zap className="h-3 w-3 text-yellow-500" />
                      <span className="font-mono">
                        {formatValue(channel.realTimeData.voltage, 'mV', 0)}
                      </span>
                    </div>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>当前电压</p>
                  </TooltipContent>
                </Tooltip>

                {!compactMode && (
                  <>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <div className="flex items-center space-x-1">
                          <Thermometer className="h-3 w-3 text-red-500" />
                          <span className="font-mono">
                            {formatValue(channel.realTimeData.temperature, '℃', 1)}
                          </span>
                        </div>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>当前温度</p>
                      </TooltipContent>
                    </Tooltip>

                    <Tooltip>
                      <TooltipTrigger asChild>
                        <div className="flex items-center space-x-1">
                          <span className="text-green-500 font-bold text-xs">P</span>
                          <span className="font-mono">
                            {formatValue(channel.realTimeData.power, 'mW', 0)}
                          </span>
                        </div>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>当前功率</p>
                      </TooltipContent>
                    </Tooltip>
                  </>
                )}
              </div>
            </div>
          )}

          {/* 当前工步信息 */}
          {!compactMode && channel.currentStep && (
            <div className="space-y-1">
              <div className="flex items-center space-x-1">
                <Clock className="h-3 w-3 text-blue-500" />
                <span className="text-xs font-medium">
                  工步 {channel.currentStep.stepNumber}: {channel.currentStep.stepName}
                </span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-1.5">
                <div 
                  className="bg-blue-500 h-1.5 rounded-full transition-all duration-300"
                  style={{ width: `${channel.currentStep.progress}%` }}
                />
              </div>
              <div className="text-xs text-muted-foreground">
                进度: {channel.currentStep.progress.toFixed(1)}%
              </div>
            </div>
          )}

          {/* 统计信息 */}
          {showStatistics && !compactMode && (
            <div className="grid grid-cols-2 gap-1 text-xs text-muted-foreground">
              <div>
                运行: {formatTime(channel.statistics.totalRunTime)}
              </div>
              <div>
                循环: {channel.statistics.totalCycles}
              </div>
            </div>
          )}

          {/* 选择指示器 */}
          {isSelected && (
            <div className="absolute top-2 right-2">
              <div className="w-3 h-3 bg-primary rounded-full flex items-center justify-center">
                <div className="w-1.5 h-1.5 bg-white rounded-full" />
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </TooltipProvider>
  );
}

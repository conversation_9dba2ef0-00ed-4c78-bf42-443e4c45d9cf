'use client';

import React from 'react';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { RealTimeData } from '@/lib/types/dashboard';
import { format } from 'date-fns';

interface RealTimeMonitorProps {
  data: RealTimeData[];
  className?: string;
}

const CustomTooltip = ({ active, payload, label }: any) => {
  if (active && payload && payload.length) {
    const time = format(new Date(label), 'HH:mm:ss');
    return (
      <div className="bg-background border rounded-lg shadow-lg p-3">
        <p className="font-medium mb-2">{time}</p>
        {payload.map((entry: any, index: number) => (
          <p key={index} className="text-sm" style={{ color: entry.color }}>
            {entry.name}: {entry.value.toFixed(2)} {getUnit(entry.dataKey)}
          </p>
        ))}
      </div>
    );
  }
  return null;
};

const getUnit = (dataKey: string) => {
  switch (dataKey) {
    case 'voltage':
      return 'V';
    case 'current':
      return 'A';
    case 'temperature':
      return '°C';
    case 'power':
      return 'W';
    default:
      return '';
  }
};

const formatXAxisLabel = (tickItem: string) => {
  return format(new Date(tickItem), 'HH:mm');
};

export function RealTimeMonitor({ data, className }: RealTimeMonitorProps) {
  const latestData = data[data.length - 1];
  
  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          实时监控数据
          <Badge variant="outline">实时更新</Badge>
        </CardTitle>
        <CardDescription>
          过去1小时的关键参数实时监控
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="h-[300px]">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
              <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />
              <XAxis 
                dataKey="timestamp" 
                tickFormatter={formatXAxisLabel}
                className="text-xs"
                interval="preserveStartEnd"
              />
              <YAxis className="text-xs" />
              <Tooltip content={<CustomTooltip />} />
              <Line 
                type="monotone" 
                dataKey="voltage" 
                stroke="#2563eb" 
                strokeWidth={2}
                name="电压"
                dot={false}
                yAxisId="left"
              />
              <Line 
                type="monotone" 
                dataKey="current" 
                stroke="#16a34a" 
                strokeWidth={2}
                name="电流"
                dot={false}
                yAxisId="left"
              />
              <Line 
                type="monotone" 
                dataKey="temperature" 
                stroke="#dc2626" 
                strokeWidth={2}
                name="温度"
                dot={false}
                yAxisId="right"
              />
              <Line 
                type="monotone" 
                dataKey="power" 
                stroke="#d97706" 
                strokeWidth={2}
                name="功率"
                dot={false}
                yAxisId="left"
              />
            </LineChart>
          </ResponsiveContainer>
        </div>
        
        {/* 当前实时数值 */}
        {latestData && (
          <div className="grid grid-cols-4 gap-4 mt-4">
            <div className="text-center">
              <div className="text-lg font-semibold text-blue-600">
                {latestData.voltage.toFixed(2)}
              </div>
              <div className="text-xs text-muted-foreground">电压 (V)</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-semibold text-green-600">
                {latestData.current.toFixed(2)}
              </div>
              <div className="text-xs text-muted-foreground">电流 (A)</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-semibold text-red-600">
                {latestData.temperature.toFixed(1)}
              </div>
              <div className="text-xs text-muted-foreground">温度 (°C)</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-semibold text-orange-600">
                {latestData.power.toFixed(2)}
              </div>
              <div className="text-xs text-muted-foreground">功率 (W)</div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

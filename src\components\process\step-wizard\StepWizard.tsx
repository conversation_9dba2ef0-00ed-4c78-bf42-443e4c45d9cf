'use client';

import React, { useState, useEffect } from 'react';
import { useTranslations } from 'next-intl';
import { ChevronLeft, ChevronRight, Check, X } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';

import { BasicParametersStep } from './BasicParametersStep';
import { AuxiliaryParametersStep } from './AuxiliaryParametersStep';
import { ReviewStep } from './ReviewStep';

import { StepFormData } from '@/lib/process/types/step';
import { DEFAULT_STEP_PARAMETERS, DEFAULT_AUXILIARY_PARAMETERS } from '@/lib/process/constants/stepConstants';

interface StepWizardProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  initialData?: StepFormData;
  onSubmit: (data: StepFormData) => void;
  isLoading?: boolean;
}

interface WizardStep {
  id: string;
  title: string;
  description: string;
  component: React.ComponentType<any>;
  isOptional?: boolean;
}

const WIZARD_STEPS: WizardStep[] = [
  {
    id: 'basic',
    title: '基本参数',
    description: '配置工步的基本信息和主要参数',
    component: BasicParametersStep
  },
  {
    id: 'auxiliary',
    title: '辅助参数',
    description: '配置高级保护和监控参数',
    component: AuxiliaryParametersStep,
    isOptional: true
  },
  {
    id: 'review',
    title: '确认配置',
    description: '检查并确认所有参数设置',
    component: ReviewStep
  }
];

export function StepWizard({ 
  open, 
  onOpenChange, 
  initialData, 
  onSubmit, 
  isLoading = false 
}: StepWizardProps) {
  const t = useTranslations('process.stepWizard');
  
  const [currentStepIndex, setCurrentStepIndex] = useState(0);
  const [stepData, setStepData] = useState<StepFormData>(() => {
    if (initialData) {
      return initialData;
    }
    
    return {
      name: '',
      status: 'discharge' as any,
      mode: 'cc' as any,
      parameters: DEFAULT_STEP_PARAMETERS,
      auxiliaryParams: DEFAULT_AUXILIARY_PARAMETERS
    };
  });
  
  const [completedSteps, setCompletedSteps] = useState<Set<string>>(new Set());
  const [stepErrors, setStepErrors] = useState<Record<string, string[]>>({});

  // 当对话框打开时重置状态
  useEffect(() => {
    if (open) {
      // 重置到第一步
      setCurrentStepIndex(0);

      // 重置数据
      if (initialData) {
        setStepData(initialData);
      } else {
        // 新建工步时使用初始化数据
        setStepData({
          name: '',
          status: 'discharge' as any,
          mode: 'cc' as any,
          parameters: DEFAULT_STEP_PARAMETERS,
          auxiliaryParams: DEFAULT_AUXILIARY_PARAMETERS
        });
      }

      // 重置其他状态
      setCompletedSteps(new Set());
      setStepErrors({});
    }
  }, [open, initialData]);

  const currentStep = WIZARD_STEPS[currentStepIndex];
  const isFirstStep = currentStepIndex === 0;
  const isLastStep = currentStepIndex === WIZARD_STEPS.length - 1;
  const progress = ((currentStepIndex + 1) / WIZARD_STEPS.length) * 100;

  const handleStepDataChange = (data: Partial<StepFormData>) => {
    setStepData(prev => ({ ...prev, ...data }));
  };

  const validateCurrentStep = (): boolean => {
    const errors: string[] = [];
    
    switch (currentStep.id) {
      case 'basic':
        if (!stepData.name.trim()) {
          errors.push('工步名称不能为空');
        }
        if (stepData.parameters.electrical.upperVoltage.value < stepData.parameters.electrical.lowerVoltage.value) {
          errors.push('上限电压应大于等于下限电压');
        }
        if (stepData.parameters.termination.upperTemperature.value <= stepData.parameters.termination.lowerTemperature.value) {
          errors.push('温度上限应大于温度下限');
        }
        break;
      
      case 'auxiliary':
        // 辅助参数验证（可选）
        break;
      
      case 'review':
        // 最终检查
        break;
    }

    setStepErrors(prev => ({ ...prev, [currentStep.id]: errors }));
    return errors.length === 0;
  };

  const handleNext = () => {
    if (validateCurrentStep()) {
      setCompletedSteps(prev => new Set([...prev, currentStep.id]));
      
      if (isLastStep) {
        handleSubmit();
      } else {
        setCurrentStepIndex(prev => prev + 1);
      }
    }
  };

  const handlePrevious = () => {
    if (!isFirstStep) {
      setCurrentStepIndex(prev => prev - 1);
    }
  };

  const handleStepClick = (stepIndex: number) => {
    // 只允许跳转到已完成的步骤或下一步
    const targetStep = WIZARD_STEPS[stepIndex];
    if (completedSteps.has(targetStep.id) || stepIndex <= currentStepIndex + 1) {
      setCurrentStepIndex(stepIndex);
    }
  };

  const handleSubmit = () => {
    if (validateCurrentStep()) {
      onSubmit(stepData);
    }
  };

  const handleCancel = () => {
    setCurrentStepIndex(0);
    setCompletedSteps(new Set());
    setStepErrors({});
    onOpenChange(false);
  };

  const CurrentStepComponent = currentStep.component;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-[95vw] w-[95vw] max-h-[95vh] h-[95vh] overflow-hidden flex flex-col">
        <DialogHeader>
          <DialogTitle className="text-xl">
            {initialData ? '编辑工步' : '新建工步'} - 引导配置
          </DialogTitle>
          <DialogDescription>
            通过分步引导完成工步参数配置
          </DialogDescription>
        </DialogHeader>

        {/* 进度条 */}
        <div className="space-y-2">
          <div className="flex items-center justify-between text-sm">
            <span>配置进度</span>
            <span>{currentStepIndex + 1} / {WIZARD_STEPS.length}</span>
          </div>
          <Progress value={progress} className="h-2" />
        </div>

        {/* 步骤导航 */}
        <div className="flex items-center justify-between py-4">
          {WIZARD_STEPS.map((step, index) => (
            <div key={step.id} className="flex items-center">
              <button
                onClick={() => handleStepClick(index)}
                className={`flex items-center justify-center w-8 h-8 rounded-full border-2 transition-colors ${
                  index === currentStepIndex
                    ? 'border-primary bg-primary text-primary-foreground'
                    : completedSteps.has(step.id)
                    ? 'border-green-500 bg-green-500 text-white'
                    : stepErrors[step.id]?.length > 0
                    ? 'border-red-500 bg-red-500 text-white'
                    : 'border-muted-foreground bg-background text-muted-foreground hover:border-primary'
                }`}
                disabled={!completedSteps.has(step.id) && index > currentStepIndex + 1}
              >
                {completedSteps.has(step.id) ? (
                  <Check className="h-4 w-4" />
                ) : stepErrors[step.id]?.length > 0 ? (
                  <X className="h-4 w-4" />
                ) : (
                  <span className="text-sm font-medium">{index + 1}</span>
                )}
              </button>
              
              <div className="ml-3 text-left">
                <div className="flex items-center space-x-2">
                  <span className={`text-sm font-medium ${
                    index === currentStepIndex ? 'text-primary' : 'text-foreground'
                  }`}>
                    {step.title}
                  </span>
                  {step.isOptional && (
                    <Badge variant="outline" className="text-xs">
                      可选
                    </Badge>
                  )}
                </div>
                <p className="text-xs text-muted-foreground">{step.description}</p>
              </div>
              
              {index < WIZARD_STEPS.length - 1 && (
                <ChevronRight className="h-4 w-4 text-muted-foreground mx-4" />
              )}
            </div>
          ))}
        </div>

        <Separator />

        {/* 当前步骤内容 */}
        <div className="flex-1 overflow-y-auto">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <span>{currentStep.title}</span>
                {currentStep.isOptional && (
                  <Badge variant="outline">可选</Badge>
                )}
              </CardTitle>
              <CardDescription>{currentStep.description}</CardDescription>
            </CardHeader>
            <CardContent>
              {/* 错误提示 */}
              {stepErrors[currentStep.id]?.length > 0 && (
                <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
                  <div className="text-sm text-red-800">
                    <p className="font-medium mb-1">请修正以下错误：</p>
                    <ul className="list-disc list-inside space-y-1">
                      {stepErrors[currentStep.id].map((error, index) => (
                        <li key={index}>{error}</li>
                      ))}
                    </ul>
                  </div>
                </div>
              )}

              {/* 步骤组件 */}
              <CurrentStepComponent
                data={stepData}
                onChange={handleStepDataChange}
                errors={stepErrors[currentStep.id] || []}
              />
            </CardContent>
          </Card>
        </div>

        {/* 操作按钮 */}
        <div className="flex items-center justify-between pt-4 border-t">
          <Button
            variant="outline"
            onClick={handleCancel}
          >
            取消
          </Button>

          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              onClick={handlePrevious}
              disabled={isFirstStep}
            >
              <ChevronLeft className="h-4 w-4 mr-1" />
              上一步
            </Button>

            <Button
              onClick={handleNext}
              disabled={isLoading}
            >
              {isLastStep ? (
                isLoading ? '保存中...' : '完成'
              ) : (
                <>
                  下一步
                  <ChevronRight className="h-4 w-4 ml-1" />
                </>
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}

'use client';

import React, { useState } from 'react';
import { useTranslations } from 'next-intl';
import { Shield, CheckCircle, AlertTriangle, XCircle, Info } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';

import { importStepsFromJson, JsonImportResult } from '@/lib/process/services/jsonService';

interface JsonValidatorProps {
  disabled?: boolean;
}

interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  stepCount: number;
  metadata?: {
    version: string;
    exportedBy: string;
    exportedAt: Date;
  };
}

export function JsonValidator({ disabled = false }: JsonValidatorProps) {
  const t = useTranslations('process.jsonValidator');
  
  const [isOpen, setIsOpen] = useState(false);
  const [jsonContent, setJsonContent] = useState('');
  const [validationResult, setValidationResult] = useState<ValidationResult | null>(null);
  const [isValidating, setIsValidating] = useState(false);

  const handleValidate = async () => {
    if (!jsonContent.trim()) {
      setValidationResult({
        isValid: false,
        errors: ['请输入JSON内容'],
        warnings: [],
        stepCount: 0
      });
      return;
    }

    setIsValidating(true);
    
    try {
      const result: JsonImportResult = importStepsFromJson(jsonContent);
      
      setValidationResult({
        isValid: result.success,
        errors: result.errors || [],
        warnings: result.warnings || [],
        stepCount: result.data?.length || 0,
        metadata: result.metadata
      });
    } catch (error) {
      setValidationResult({
        isValid: false,
        errors: [error instanceof Error ? error.message : '验证过程中发生错误'],
        warnings: [],
        stepCount: 0
      });
    } finally {
      setIsValidating(false);
    }
  };

  const handleClear = () => {
    setJsonContent('');
    setValidationResult(null);
  };

  const handleClose = () => {
    setIsOpen(false);
    setJsonContent('');
    setValidationResult(null);
  };

  const getValidationIcon = () => {
    if (!validationResult) return <Shield className="h-5 w-5 text-muted-foreground" />;
    
    if (validationResult.isValid) {
      return <CheckCircle className="h-5 w-5 text-green-500" />;
    } else {
      return <XCircle className="h-5 w-5 text-red-500" />;
    }
  };

  const getValidationStatus = () => {
    if (!validationResult) return '未验证';
    return validationResult.isValid ? '验证通过' : '验证失败';
  };

  const getValidationVariant = () => {
    if (!validationResult) return 'outline';
    return validationResult.isValid ? 'default' : 'destructive';
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" disabled={disabled}>
          <Shield className="h-4 w-4 mr-2" />
          验证JSON
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>JSON格式验证</DialogTitle>
          <DialogDescription>
            验证工步流程JSON文件的格式和内容是否正确
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* JSON输入区域 */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">JSON内容</CardTitle>
              <CardDescription>
                粘贴或输入要验证的JSON内容
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="jsonContent">JSON内容</Label>
                <Textarea
                  id="jsonContent"
                  value={jsonContent}
                  onChange={(e) => setJsonContent(e.target.value)}
                  placeholder="请粘贴JSON内容..."
                  className="min-h-[200px] font-mono text-sm"
                />
                <div className="flex items-center justify-between text-xs text-muted-foreground">
                  <span>字符数: {jsonContent.length}</span>
                  <span>行数: {jsonContent.split('\n').length}</span>
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <Button 
                  onClick={handleValidate}
                  disabled={isValidating || !jsonContent.trim()}
                >
                  {isValidating ? '验证中...' : '开始验证'}
                </Button>
                <Button 
                  variant="outline" 
                  onClick={handleClear}
                  disabled={isValidating}
                >
                  清空
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* 验证结果 */}
          {validationResult && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  {getValidationIcon()}
                  <span>验证结果</span>
                  <Badge variant={getValidationVariant()}>
                    {getValidationStatus()}
                  </Badge>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* 基本信息 */}
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                  <div className="text-center p-3 bg-muted rounded-md">
                    <div className="text-2xl font-bold">
                      {validationResult.isValid ? '✓' : '✗'}
                    </div>
                    <div className="text-muted-foreground">格式验证</div>
                  </div>
                  
                  <div className="text-center p-3 bg-muted rounded-md">
                    <div className="text-2xl font-bold text-blue-600">
                      {validationResult.stepCount}
                    </div>
                    <div className="text-muted-foreground">工步数量</div>
                  </div>
                  
                  <div className="text-center p-3 bg-muted rounded-md">
                    <div className="text-2xl font-bold text-orange-600">
                      {validationResult.warnings.length}
                    </div>
                    <div className="text-muted-foreground">警告数量</div>
                  </div>
                  
                  <div className="text-center p-3 bg-muted rounded-md">
                    <div className="text-2xl font-bold text-red-600">
                      {validationResult.errors.length}
                    </div>
                    <div className="text-muted-foreground">错误数量</div>
                  </div>
                </div>

                {/* 元数据信息 */}
                {validationResult.metadata && (
                  <>
                    <Separator />
                    <div>
                      <h4 className="font-medium mb-3">文件元数据</h4>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                        <div>
                          <span className="text-muted-foreground">版本：</span>
                          <Badge variant="outline" className="ml-2">
                            {validationResult.metadata.version}
                          </Badge>
                        </div>
                        <div>
                          <span className="text-muted-foreground">导出者：</span>
                          <span className="font-medium ml-2">{validationResult.metadata.exportedBy}</span>
                        </div>
                        <div>
                          <span className="text-muted-foreground">导出时间：</span>
                          <span className="font-medium ml-2">
                            {validationResult.metadata.exportedAt.toLocaleString()}
                          </span>
                        </div>
                      </div>
                    </div>
                  </>
                )}

                {/* 成功信息 */}
                {validationResult.isValid && (
                  <Alert>
                    <CheckCircle className="h-4 w-4" />
                    <AlertDescription>
                      JSON格式验证通过！文件包含 {validationResult.stepCount} 个有效的工步配置，
                      可以安全地导入到系统中。
                    </AlertDescription>
                  </Alert>
                )}

                {/* 错误信息 */}
                {validationResult.errors.length > 0 && (
                  <Alert variant="destructive">
                    <XCircle className="h-4 w-4" />
                    <AlertDescription>
                      <div className="font-medium mb-2">发现以下错误：</div>
                      <ul className="list-disc list-inside space-y-1">
                        {validationResult.errors.map((error, index) => (
                          <li key={index} className="text-sm">{error}</li>
                        ))}
                      </ul>
                    </AlertDescription>
                  </Alert>
                )}

                {/* 警告信息 */}
                {validationResult.warnings.length > 0 && (
                  <Alert>
                    <AlertTriangle className="h-4 w-4" />
                    <AlertDescription>
                      <div className="font-medium mb-2">注意事项：</div>
                      <ul className="list-disc list-inside space-y-1">
                        {validationResult.warnings.map((warning, index) => (
                          <li key={index} className="text-sm">{warning}</li>
                        ))}
                      </ul>
                    </AlertDescription>
                  </Alert>
                )}
              </CardContent>
            </Card>
          )}

          {/* 验证规则说明 */}
          <Card className="border-muted">
            <CardHeader>
              <CardTitle className="text-lg flex items-center space-x-2">
                <Info className="h-5 w-5" />
                <span>验证规则说明</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3 text-sm">
              <div>
                <h4 className="font-medium mb-2">格式验证</h4>
                <ul className="list-disc list-inside space-y-1 text-muted-foreground">
                  <li>JSON语法正确性检查</li>
                  <li>数据结构完整性验证</li>
                  <li>必需字段存在性检查</li>
                  <li>数据类型匹配验证</li>
                </ul>
              </div>

              <Separator />

              <div>
                <h4 className="font-medium mb-2">业务逻辑验证</h4>
                <ul className="list-disc list-inside space-y-1 text-muted-foreground">
                  <li>电压范围合理性检查（上限 &gt;= 下限）</li>
                  <li>温度范围合理性检查（上限 &gt; 下限）</li>
                  <li>时间格式正确性验证（hh:mm:ss.ms）</li>
                  <li>数值范围有效性检查</li>
                  <li>工步名称非空验证</li>
                </ul>
              </div>

              <Separator />

              <div>
                <h4 className="font-medium mb-2">兼容性检查</h4>
                <ul className="list-disc list-inside space-y-1 text-muted-foreground">
                  <li>文件版本兼容性检查</li>
                  <li>元数据一致性验证</li>
                  <li>工步数量匹配检查</li>
                </ul>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* 操作按钮 */}
        <div className="flex items-center justify-end space-x-2 pt-4 border-t">
          <Button variant="outline" onClick={handleClose}>
            关闭
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}

'use client';

import React, { useState, useRef } from 'react';
import { useTranslations } from 'next-intl';
import { Upload, FileText, AlertCircle, CheckCircle, X, Info } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Progress } from '@/components/ui/progress';

import { Step } from '@/lib/process/types/step';
import { 
  importStepsFromJson, 
  readFileAsText, 
  validateJsonFile, 
  JsonImportResult 
} from '@/lib/process/services/jsonService';

interface JsonImporterProps {
  onImport: (steps: Step[]) => void;
  isLoading?: boolean;
}

export function JsonImporter({ onImport, isLoading = false }: JsonImporterProps) {
  const t = useTranslations('process.jsonImporter');
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  const [isOpen, setIsOpen] = useState(false);
  const [dragActive, setDragActive] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [importResult, setImportResult] = useState<JsonImportResult | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);

  const handleFileSelect = (file: File) => {
    const validation = validateJsonFile(file);
    if (!validation.valid) {
      setImportResult({
        success: false,
        errors: [validation.error || '文件验证失败']
      });
      return;
    }

    setSelectedFile(file);
    setImportResult(null);
  };

  const handleFileInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      handleFileSelect(file);
    }
  };

  const handleDragOver = (event: React.DragEvent) => {
    event.preventDefault();
    setDragActive(true);
  };

  const handleDragLeave = (event: React.DragEvent) => {
    event.preventDefault();
    setDragActive(false);
  };

  const handleDrop = (event: React.DragEvent) => {
    event.preventDefault();
    setDragActive(false);

    const file = event.dataTransfer.files?.[0];
    if (file) {
      handleFileSelect(file);
    }
  };

  const handleProcessFile = async () => {
    if (!selectedFile) return;

    setIsProcessing(true);
    try {
      const fileContent = await readFileAsText(selectedFile);
      const result = importStepsFromJson(fileContent);
      setImportResult(result);
    } catch (error) {
      setImportResult({
        success: false,
        errors: [error instanceof Error ? error.message : '文件处理失败']
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const handleConfirmImport = () => {
    if (importResult?.success && importResult.data) {
      onImport(importResult.data);
      handleClose();
    }
  };

  const handleClose = () => {
    setIsOpen(false);
    setSelectedFile(null);
    setImportResult(null);
    setDragActive(false);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const renderFileUploadArea = () => (
    <div
      className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
        dragActive 
          ? 'border-primary bg-primary/5' 
          : 'border-muted-foreground/25 hover:border-primary/50'
      }`}
      onDragOver={handleDragOver}
      onDragLeave={handleDragLeave}
      onDrop={handleDrop}
    >
      <Upload className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
      <h3 className="text-lg font-semibold mb-2">选择或拖拽JSON文件</h3>
      <p className="text-muted-foreground mb-4">
        支持.json格式的工步流程文件，最大10MB
      </p>
      
      <Button 
        variant="outline" 
        onClick={() => fileInputRef.current?.click()}
        disabled={isProcessing}
      >
        <FileText className="h-4 w-4 mr-2" />
        选择文件
      </Button>
      
      <input
        ref={fileInputRef}
        type="file"
        accept=".json,application/json"
        onChange={handleFileInputChange}
        className="hidden"
      />
    </div>
  );

  const renderFileInfo = () => {
    if (!selectedFile) return null;

    return (
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">文件信息</CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="flex items-center justify-between">
            <span className="text-sm text-muted-foreground">文件名</span>
            <span className="font-medium">{selectedFile.name}</span>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-sm text-muted-foreground">文件大小</span>
            <span className="font-medium">{(selectedFile.size / 1024).toFixed(2)} KB</span>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-sm text-muted-foreground">文件类型</span>
            <span className="font-medium">{selectedFile.type || 'application/json'}</span>
          </div>
          
          <Separator />
          
          <div className="flex items-center space-x-2">
            <Button 
              onClick={handleProcessFile} 
              disabled={isProcessing}
              className="flex-1"
            >
              {isProcessing ? '解析中...' : '解析文件'}
            </Button>
            <Button 
              variant="outline" 
              onClick={() => setSelectedFile(null)}
              disabled={isProcessing}
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  };

  const renderImportResult = () => {
    if (!importResult) return null;

    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            {importResult.success ? (
              <CheckCircle className="h-5 w-5 text-green-500" />
            ) : (
              <AlertCircle className="h-5 w-5 text-red-500" />
            )}
            <span>解析结果</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* 成功信息 */}
          {importResult.success && importResult.data && (
            <Alert>
              <CheckCircle className="h-4 w-4" />
              <AlertDescription>
                成功解析 {importResult.data.length} 个工步，可以导入到系统中。
              </AlertDescription>
            </Alert>
          )}

          {/* 错误信息 */}
          {importResult.errors && importResult.errors.length > 0 && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                <div className="font-medium mb-2">发现以下错误：</div>
                <ul className="list-disc list-inside space-y-1">
                  {importResult.errors.map((error, index) => (
                    <li key={index} className="text-sm">{error}</li>
                  ))}
                </ul>
              </AlertDescription>
            </Alert>
          )}

          {/* 警告信息 */}
          {importResult.warnings && importResult.warnings.length > 0 && (
            <Alert>
              <Info className="h-4 w-4" />
              <AlertDescription>
                <div className="font-medium mb-2">注意事项：</div>
                <ul className="list-disc list-inside space-y-1">
                  {importResult.warnings.map((warning, index) => (
                    <li key={index} className="text-sm">{warning}</li>
                  ))}
                </ul>
              </AlertDescription>
            </Alert>
          )}

          {/* 元数据信息 */}
          {importResult.metadata && (
            <div className="space-y-3">
              <h4 className="font-medium">文件元数据</h4>
              <div className="grid grid-cols-2 gap-3 text-sm">
                <div>
                  <span className="text-muted-foreground">版本：</span>
                  <Badge variant="outline" className="ml-2">
                    {importResult.metadata.version}
                  </Badge>
                </div>
                <div>
                  <span className="text-muted-foreground">工步数量：</span>
                  <span className="font-medium ml-2">{importResult.metadata.totalSteps}</span>
                </div>
                <div>
                  <span className="text-muted-foreground">导出者：</span>
                  <span className="font-medium ml-2">{importResult.metadata.exportedBy}</span>
                </div>
                <div>
                  <span className="text-muted-foreground">导出时间：</span>
                  <span className="font-medium ml-2">
                    {importResult.metadata.exportedAt.toLocaleString()}
                  </span>
                </div>
              </div>
            </div>
          )}

          {/* 工步预览 */}
          {importResult.success && importResult.data && importResult.data.length > 0 && (
            <div className="space-y-3">
              <h4 className="font-medium">工步预览</h4>
              <div className="max-h-40 overflow-y-auto border rounded-md">
                {importResult.data.slice(0, 5).map((step, index) => (
                  <div key={index} className="p-3 border-b last:border-b-0">
                    <div className="flex items-center justify-between">
                      <div>
                        <span className="font-medium">{step.name}</span>
                        <div className="text-sm text-muted-foreground">
                          {step.status} | {step.mode} | {step.parameters.electrical.current.value}mA
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
                {importResult.data.length > 5 && (
                  <div className="p-3 text-center text-sm text-muted-foreground">
                    还有 {importResult.data.length - 5} 个工步...
                  </div>
                )}
              </div>
            </div>
          )}

          {/* 操作按钮 */}
          {importResult.success && importResult.data && (
            <div className="flex items-center space-x-2 pt-4">
              <Button 
                onClick={handleConfirmImport}
                disabled={isLoading}
                className="flex-1"
              >
                {isLoading ? '导入中...' : '确认导入'}
              </Button>
              <Button 
                variant="outline" 
                onClick={() => setImportResult(null)}
                disabled={isLoading}
              >
                重新选择
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    );
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="outline">
          <Upload className="h-4 w-4 mr-2" />
          导入JSON
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>导入工步流程</DialogTitle>
          <DialogDescription>
            从JSON文件导入工步流程配置
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* 处理进度 */}
          {isProcessing && (
            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span>正在解析文件...</span>
              </div>
              <Progress value={undefined} className="h-2" />
            </div>
          )}

          {/* 文件上传区域 */}
          {!selectedFile && !importResult && renderFileUploadArea()}

          {/* 文件信息 */}
          {selectedFile && !importResult && renderFileInfo()}

          {/* 导入结果 */}
          {renderImportResult()}
        </div>

        <div className="flex items-center justify-end space-x-2 pt-4 border-t">
          <Button variant="outline" onClick={handleClose}>
            关闭
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}

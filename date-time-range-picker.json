{"$schema": "https://ui.shadcn.com/schema/registry-item.json", "name": "date-time-range-picker", "type": "registry:component", "title": "Date Time Range Picker", "description": "A component for selecting a range of dates and times.", "dependencies": ["date-fns", "lucide-react"], "registryDependencies": ["button", "calendar", "popover"], "files": [{"path": "registry/default/date-time-range-picker/date-time-range-picker.tsx", "content": "\"use client\";\r\n\r\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\r\nimport { Calendar } from \"@/components/ui/calendar\";\r\nimport {\r\n  Popover,\r\n  PopoverContent,\r\n  PopoverTrigger,\r\n} from \"@/components/ui/popover\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport {\r\n  addMonths,\r\n  endOfDay,\r\n  endOfMonth,\r\n  endOfWeek,\r\n  format,\r\n  isEqual,\r\n  isValid,\r\n  startOfDay,\r\n  startOfMonth,\r\n  startOfWeek,\r\n  subDays,\r\n  subMonths,\r\n} from \"date-fns\";\r\nimport { enUS, type Locale } from \"date-fns/locale\";\r\nimport { CalendarIcon, CheckIcon, ChevronRightIcon } from \"lucide-react\";\r\nimport * as React from \"react\";\r\nimport { DateTimeInput } from \"./date-time-input\";\r\n\r\nexport interface DateTimeRange {\r\n  from: Date | undefined;\r\n  to: Date | undefined;\r\n}\r\n\r\ninterface Preset {\r\n  name: string;\r\n  label: string;\r\n}\r\n\r\nconst PRESETS: Preset[] = [\r\n  { name: \"last7\", label: \"Last 7 days\" },\r\n  { name: \"last14\", label: \"Last 14 days\" },\r\n  { name: \"last30\", label: \"Last 30 days\" },\r\n  { name: \"thisWeek\", label: \"This Week\" },\r\n  { name: \"lastWeek\", label: \"Last Week\" },\r\n  { name: \"thisMonth\", label: \"This Month\" },\r\n  { name: \"lastMonth\", label: \"Last Month\" },\r\n];\r\n\r\nexport interface DateTimeRangePickerProps {\r\n  onUpdate?: (values: { range: DateTimeRange }) => void;\r\n  initialDateFrom?: Date | string;\r\n  initialDateTo?: Date | string;\r\n  align?: \"start\" | \"center\" | \"end\";\r\n  locale?: Locale;\r\n  className?: string;\r\n}\r\n\r\nconst formatDateTime = (\r\n  date: Date | undefined,\r\n  locale: Locale = enUS,\r\n): string => {\r\n  if (!date || !isValid(date)) return \"Select date\";\r\n  return format(date, \"PPP p\", { locale });\r\n};\r\n\r\nconst getDateAdjustedForTimezone = (\r\n  dateInput: Date | string | undefined,\r\n): Date | undefined => {\r\n  if (!dateInput) return undefined;\r\n  if (typeof dateInput === \"string\") {\r\n    const parts = dateInput.split(\"-\").map((part) => Number.parseInt(part, 10));\r\n    return new Date(parts[0], parts[1] - 1, parts[2]);\r\n  }\r\n  return new Date(dateInput);\r\n};\r\n\r\nexport const DateTimeRangePicker: React.FC<DateTimeRangePickerProps> = ({\r\n  initialDateFrom,\r\n  initialDateTo,\r\n  onUpdate,\r\n  align = \"center\",\r\n  locale = enUS,\r\n  className,\r\n}) => {\r\n  const [isOpen, setIsOpen] = React.useState(false);\r\n  const [range, setRange] = React.useState<DateTimeRange>({\r\n    from: getDateAdjustedForTimezone(initialDateFrom),\r\n    to: getDateAdjustedForTimezone(initialDateTo),\r\n  });\r\n\r\n  const openedRangeRef = React.useRef<DateTimeRange>(range);\r\n  const [selectedPreset, setSelectedPreset] = React.useState<\r\n    string | undefined\r\n  >(undefined);\r\n  const [calendarMonths, setCalendarMonths] = React.useState<[Date, Date]>([\r\n    new Date(),\r\n    addMonths(new Date(), 1),\r\n  ]);\r\n\r\n  const getPresetRange = React.useCallback(\r\n    (presetName: string): DateTimeRange => {\r\n      const now = new Date();\r\n      const today = startOfDay(now);\r\n      const endToday = endOfDay(now);\r\n\r\n      switch (presetName) {\r\n        case \"today\":\r\n          return { from: today, to: endToday };\r\n        case \"yesterday\": {\r\n          const yesterday = subDays(today, 1);\r\n          return { from: yesterday, to: endOfDay(yesterday) };\r\n        }\r\n        case \"last7\":\r\n          return { from: subDays(today, 6), to: endToday };\r\n        case \"last14\":\r\n          return { from: subDays(today, 13), to: endToday };\r\n        case \"last30\":\r\n          return { from: subDays(today, 29), to: endToday };\r\n        case \"thisWeek\":\r\n          return {\r\n            from: startOfWeek(today, { weekStartsOn: 0 }),\r\n            to: endToday,\r\n          };\r\n        case \"lastWeek\": {\r\n          const lastWeekStart = startOfWeek(subDays(today, 7), {\r\n            weekStartsOn: 0,\r\n          });\r\n          const lastWeekEnd = endOfWeek(lastWeekStart, { weekStartsOn: 0 });\r\n          return {\r\n            from: lastWeekStart,\r\n            to: lastWeekEnd,\r\n          };\r\n        }\r\n        case \"thisMonth\":\r\n          return {\r\n            from: startOfMonth(today),\r\n            to: endToday,\r\n          };\r\n        case \"lastMonth\": {\r\n          const lastMonth = subMonths(today, 1);\r\n          return {\r\n            from: startOfMonth(lastMonth),\r\n            to: endOfMonth(lastMonth),\r\n          };\r\n        }\r\n        default:\r\n          throw new Error(`Unknown date range preset: ${presetName}`);\r\n      }\r\n    },\r\n    [],\r\n  );\r\n\r\n  const setPreset = (preset: string): void => {\r\n    const newRange = getPresetRange(preset);\r\n    setRange(newRange);\r\n    setSelectedPreset(preset);\r\n    if (newRange.from) {\r\n      setCalendarMonths([newRange.from, addMonths(newRange.from, 1)]);\r\n    }\r\n  };\r\n\r\n  const checkPreset = React.useCallback(() => {\r\n    if (!range.from || !range.to) return;\r\n\r\n    for (const preset of PRESETS) {\r\n      const presetRange = getPresetRange(preset.name);\r\n      if (\r\n        isEqual(startOfDay(range.from), startOfDay(presetRange.from!)) &&\r\n        isEqual(endOfDay(range.to), endOfDay(presetRange.to!))\r\n      ) {\r\n        setSelectedPreset(preset.name);\r\n        return;\r\n      }\r\n    }\r\n    setSelectedPreset(undefined);\r\n  }, [range, getPresetRange]);\r\n\r\n  const resetValues = (): void => {\r\n    setRange({\r\n      from: getDateAdjustedForTimezone(initialDateFrom),\r\n      to: getDateAdjustedForTimezone(initialDateTo),\r\n    });\r\n    setSelectedPreset(undefined);\r\n    setCalendarMonths([new Date(), addMonths(new Date(), 1)]);\r\n  };\r\n\r\n  React.useEffect(() => {\r\n    checkPreset();\r\n  }, [checkPreset]);\r\n\r\n  const PresetButton = ({\r\n    preset,\r\n    label,\r\n    isSelected,\r\n  }: {\r\n    preset: string;\r\n    label: string;\r\n    isSelected: boolean;\r\n  }) => (\r\n    <Button\r\n      className={cn(\"justify-start\", isSelected && \"bg-muted\")}\r\n      variant=\"ghost\"\r\n      onClick={() => setPreset(preset)}\r\n    >\r\n      <CheckIcon\r\n        className={cn(\"mr-2 h-4 w-4\", isSelected ? \"opacity-100\" : \"opacity-0\")}\r\n      />\r\n      {label}\r\n    </Button>\r\n  );\r\n\r\n  const areRangesEqual = (a?: DateTimeRange, b?: DateTimeRange): boolean => {\r\n    if (!a || !b) return a === b;\r\n    return (\r\n      isEqual(a.from || new Date(), b.from || new Date()) &&\r\n      isEqual(a.to || new Date(), b.to || new Date())\r\n    );\r\n  };\r\n\r\n  React.useEffect(() => {\r\n    if (isOpen) {\r\n      openedRangeRef.current = range;\r\n    }\r\n  }, [isOpen, range]);\r\n\r\n  const handleFromDateTimeChange = (date: Date) => {\r\n    setRange((prev) => ({ ...prev, from: date }));\r\n  };\r\n\r\n  const handleToDateTimeChange = (date: Date) => {\r\n    setRange((prev) => ({ ...prev, to: date }));\r\n  };\r\n\r\n  return (\r\n    <Popover open={isOpen} onOpenChange={setIsOpen}>\r\n      <PopoverTrigger asChild>\r\n        <Button\r\n          variant=\"outline\"\r\n          className={cn(\r\n            \"w-full sm:w-[300px] justify-start text-left text-[11px] font-normal text-wrap\",\r\n            className,\r\n          )}\r\n        >\r\n          <CalendarIcon className=\"mr-2 h-4 w-4\" />\r\n          {formatDateTime(range.from, locale)}\r\n          {range.to && (\r\n            <>\r\n              <ChevronRightIcon className=\"mx-2 h-4 w-4\" />\r\n              {formatDateTime(range.to, locale)}\r\n            </>\r\n          )}\r\n        </Button>\r\n      </PopoverTrigger>\r\n      <PopoverContent className=\"w-auto p-0\" align={align} sideOffset={4}>\r\n        <div className=\"flex flex-col lg:flex-row gap-4\">\r\n          {/* Calendar Section */}\r\n          <div className=\"space-y-4 p-4\">\r\n            <div className=\"hidden lg:flex space-x-4\">\r\n              {/* Two calendars side by side for desktop */}\r\n              <Calendar\r\n                mode=\"range\"\r\n                selected={range}\r\n                onSelect={(newRange) =>\r\n                  newRange && setRange(newRange as DateTimeRange)\r\n                }\r\n                month={calendarMonths[0]}\r\n                onMonthChange={(month) =>\r\n                  setCalendarMonths([month, addMonths(month, 1)])\r\n                }\r\n                className=\"border rounded-md\"\r\n              />\r\n              <Calendar\r\n                mode=\"range\"\r\n                selected={range}\r\n                onSelect={(newRange) =>\r\n                  newRange && setRange(newRange as DateTimeRange)\r\n                }\r\n                month={calendarMonths[1]}\r\n                onMonthChange={(month) =>\r\n                  setCalendarMonths([subMonths(month, 1), month])\r\n                }\r\n                className=\"border rounded-md\"\r\n              />\r\n            </div>\r\n\r\n            {/* Single calendar for mobile */}\r\n            <div className=\"lg:hidden\">\r\n              <Calendar\r\n                mode=\"range\"\r\n                selected={range}\r\n                onSelect={(newRange) =>\r\n                  newRange && setRange(newRange as DateTimeRange)\r\n                }\r\n                className=\"border rounded-md\"\r\n              />\r\n            </div>\r\n\r\n            <div className=\"flex justify-between items-center\">\r\n              <DateTimeInput\r\n                value={range.from}\r\n                onChange={handleFromDateTimeChange}\r\n                label=\"Start\"\r\n              />\r\n              <ChevronRightIcon className=\"mx-2 h-4 w-4\" />\r\n              <DateTimeInput\r\n                value={range.to}\r\n                onChange={handleToDateTimeChange}\r\n                label=\"End\"\r\n              />\r\n            </div>\r\n          </div>\r\n\r\n          {/* Presets Section */}\r\n          <div className=\"lg:border-l lg:pl-4 space-y-2 p-4\">\r\n            <h3 className=\"font-medium text-sm\">Presets</h3>\r\n            <div className=\"grid grid-cols-2 lg:grid-cols-1 gap-1\">\r\n              {PRESETS.map((preset) => (\r\n                <PresetButton\r\n                  key={preset.name}\r\n                  preset={preset.name}\r\n                  label={preset.label}\r\n                  isSelected={selectedPreset === preset.name}\r\n                />\r\n              ))}\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Footer Actions */}\r\n        <div className=\"flex items-center justify-end gap-2 p-4 border-t\">\r\n          <Button\r\n            variant=\"ghost\"\r\n            onClick={() => {\r\n              setIsOpen(false);\r\n              resetValues();\r\n            }}\r\n          >\r\n            Cancel\r\n          </Button>\r\n          <Button\r\n            onClick={() => {\r\n              setIsOpen(false);\r\n              if (!areRangesEqual(range, openedRangeRef.current)) {\r\n                onUpdate?.({ range });\r\n              }\r\n            }}\r\n          >\r\n            Update\r\n          </Button>\r\n        </div>\r\n      </PopoverContent>\r\n    </Popover>\r\n  );\r\n};\r\n\r\nDateTimeRangePicker.displayName = \"DateTimeRangePicker\";\r\n", "type": "registry:component"}, {"path": "registry/default/date-time-range-picker/date-time-input.tsx", "content": "\"use client\";\r\n\r\nimport { cn } from \"@/lib/utils\";\r\nimport * as React from \"react\";\r\nimport { DateInput } from \"./date-input\";\r\nimport { TimeInput } from \"./time-input\";\r\n\r\ninterface DateTimeInputProps {\r\n  value?: Date;\r\n  onChange: (date: Date) => void;\r\n  disabled?: boolean;\r\n  className?: string;\r\n  label?: string;\r\n}\r\n\r\nexport const DateTimeInput: React.FC<DateTimeInputProps> = ({\r\n  value,\r\n  onChange,\r\n  disabled = false,\r\n  className,\r\n  label,\r\n}) => {\r\n  const [date, setDate] = React.useState<Date>(value || new Date());\r\n\r\n  React.useEffect(() => {\r\n    if (value) {\r\n      setDate(new Date(value));\r\n    }\r\n  }, [value]);\r\n\r\n  const handleDateChange = (newDate: Date) => {\r\n    if (disabled) return;\r\n\r\n    const updatedDate = new Date(newDate);\r\n    if (date) {\r\n      updatedDate.setHours(\r\n        date.getHours(),\r\n        date.getMinutes(),\r\n        date.getSeconds(),\r\n        date.getMilliseconds(),\r\n      );\r\n    }\r\n\r\n    setDate(updatedDate);\r\n    onChange(updatedDate);\r\n  };\r\n\r\n  const handleTimeChange = (newTime: Date) => {\r\n    if (disabled) return;\r\n\r\n    const updatedDate = new Date(date);\r\n    updatedDate.setHours(\r\n      newTime.getHours(),\r\n      newTime.getMinutes(),\r\n      newTime.getSeconds(),\r\n      newTime.getMilliseconds(),\r\n    );\r\n\r\n    setDate(updatedDate);\r\n    onChange(updatedDate);\r\n  };\r\n\r\n  return (\r\n    <div className={cn(\"flex flex-col space-y-1\", className)}>\r\n      {label && <span className=\"text-xs text-muted-foreground\">{label}</span>}\r\n      <div className=\"flex flex-col sm:flex-row gap-2\">\r\n        <DateInput\r\n          value={date}\r\n          onChange={handleDateChange}\r\n          disabled={disabled}\r\n        />\r\n        <TimeInput\r\n          value={date}\r\n          onChange={handleTimeChange}\r\n          disabled={disabled}\r\n        />\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nDateTimeInput.displayName = \"DateTimeInput\";\r\n", "type": "registry:component"}]}
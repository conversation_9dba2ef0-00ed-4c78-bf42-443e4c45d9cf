import type { NextConfig } from "next";
import createNextIntlPlugin from 'next-intl/plugin';

const withNextIntl = createNextIntlPlugin('./src/i18n.ts');

const nextConfig: NextConfig = {
  // 生产环境优化
  output: 'standalone',

  // 跳过类型检查以加快构建速度
  typescript: {
    ignoreBuildErrors: true,
  },

  // 跳过 ESLint 检查
  eslint: {
    ignoreDuringBuilds: true,
  },

  // 压缩和优化
  compress: true,
  poweredByHeader: false,

  // 图片优化
  images: {
    formats: ['image/webp', 'image/avif'],
    minimumCacheTTL: 60,
  },

  // 实验性功能
  experimental: {
    optimizePackageImports: ['lucide-react', '@radix-ui/react-icons'],
  },

  // 环境变量
  env: {
    CUSTOM_KEY: process.env.NODE_ENV,
  },
};

export default withNextIntl(nextConfig);

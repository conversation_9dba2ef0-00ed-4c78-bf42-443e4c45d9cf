'use client';

import { useState, useEffect, useCallback } from 'react';
import { User, UserPermissions, LoginCredentials } from '../types/user';
import { UserService } from '../services/userService';

export function useAuth() {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // 初始化时获取当前用户
  useEffect(() => {
    const initializeAuth = async () => {
      try {
        setIsLoading(true);
        const currentUser = UserService.getCurrentUser();
        setUser(currentUser);
        setError(null);
      } catch (err) {
        setError(err instanceof Error ? err.message : '获取用户信息失败');
      } finally {
        setIsLoading(false);
      }
    };

    initializeAuth();
  }, []);

  const login = useCallback(async (credentials: LoginCredentials) => {
    try {
      setIsLoading(true);
      setError(null);
      const loggedInUser = await UserService.login(credentials);
      setUser(loggedInUser);
    } catch (err) {
      setError(err instanceof Error ? err.message : '登录失败');
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const logout = useCallback(() => {
    UserService.logout();
    setUser(null);
    setError(null);
  }, []);

  const refreshUser = useCallback(async () => {
    try {
      setIsLoading(true);
      const refreshedUser = await UserService.refreshUser();
      setUser(refreshedUser);
      setError(null);
    } catch (err) {
      setError(err instanceof Error ? err.message : '刷新用户信息失败');
    } finally {
      setIsLoading(false);
    }
  }, []);

  const hasPermission = useCallback((permission: keyof UserPermissions): boolean => {
    return UserService.hasPermission(permission);
  }, []);

  const switchUser = useCallback((userId: string) => {
    const success = UserService.switchUser(userId);
    if (success) {
      const newUser = UserService.getCurrentUser();
      setUser(newUser);
      setError(null);
    }
    return success;
  }, []);

  return {
    // 状态
    user,
    isAuthenticated: UserService.isAuthenticated(),
    isLoading,
    error,

    // 方法
    login,
    logout,
    refreshUser,
    hasPermission,
    switchUser,

    // 便利方法
    getUserDisplayName: () => UserService.getUserDisplayName(user || undefined),
    getUserPermissions: () => UserService.getUserPermissions(),
  };
}

# Process模块架构设计文档

## 📋 文档信息

**文档版本**: v1.0  
**创建日期**: 2025-01-24  
**适用系统**: PDCS前端UI Process模块  
**技术栈**: Next.js (App Router) + Shadcn UI + Tailwind CSS v4 + next-intl  

## 🎯 模块概述

Process模块是PDCS系统的核心功能模块，负责电池测试工步流程的可视化编辑、配置管理和执行控制。基于step-specification.md规范，重新设计实现以下核心功能：

### 核心功能模块
1. **工步编辑器** - 工步的创建、编辑、删除和参数配置
2. **流程引导式对话框** - 分步骤的参数配置向导
3. **全屏工步视图表格** - 工步列表的展示和管理
4. **JSON导入导出** - 工步流程的数据交换
5. **工步发送界面** - 工步到设备通道的分发
6. **通道选择器** - 可复用的设备通道选择组件

## 🏗️ 目录结构设计

```
src/
├── app/[locale]/process/
│   ├── page.tsx                    # 主页面入口
│   ├── editor/
│   │   └── page.tsx               # 工步编辑器页面
│   ├── send/
│   │   └── page.tsx               # 工步发送页面
│   └── layout.tsx                 # Process模块布局
│
├── components/process/
│   ├── step-editor/               # 工步编辑器组件
│   │   ├── StepEditor.tsx         # 主编辑器组件
│   │   ├── StepForm.tsx           # 工步表单组件
│   │   ├── ParameterGroups.tsx    # 参数组配置
│   │   └── ValidationRules.tsx    # 验证规则组件
│   │
│   ├── step-wizard/               # 流程引导式对话框
│   │   ├── StepWizard.tsx         # 向导主组件
│   │   ├── BasicParametersStep.tsx # 基础参数步骤
│   │   ├── AuxiliaryParametersStep.tsx # 辅助参数步骤
│   │   └── ReviewStep.tsx         # 确认步骤
│   │
│   ├── step-table/                # 工步视图表格
│   │   ├── StepTable.tsx          # 表格主组件
│   │   ├── StepTableColumns.tsx   # 表格列定义
│   │   ├── StepTableActions.tsx   # 表格操作
│   │   └── StepTableFilters.tsx   # 表格筛选
│   │
│   ├── step-sender/               # 工步发送组件
│   │   ├── StepSender.tsx         # 发送主组件
│   │   ├── ChannelSelector.tsx    # 通道选择器
│   │   └── SendProgress.tsx       # 发送进度
│   │
│   ├── channel-selector/          # 可复用通道选择器
│   │   ├── ChannelSelector.tsx    # 选择器主组件
│   │   ├── ChannelGrid.tsx        # 通道网格布局
│   │   ├── ChannelCard.tsx        # 单个通道卡片
│   │   └── BatteryIcon.tsx        # 电池图标组件
│   │
│   ├── json-manager/              # JSON导入导出
│   │   ├── JsonImporter.tsx       # JSON导入组件
│   │   ├── JsonExporter.tsx       # JSON导出组件
│   │   └── JsonValidator.tsx      # JSON验证组件
│   │
│   └── shared/                    # 共享组件
│       ├── ProcessToolbar.tsx     # 工具栏组件
│       ├── ProcessStatusBar.tsx   # 状态栏组件
│       └── ProcessNotifications.tsx # 通知组件
│
├── lib/process/
│   ├── types/                     # 类型定义
│   │   ├── step.ts               # 工步类型
│   │   ├── process.ts            # 流程类型
│   │   ├── channel.ts            # 通道类型
│   │   └── validation.ts         # 验证类型
│   │
│   ├── stores/                    # 状态管理
│   │   ├── stepStore.ts          # 工步状态
│   │   ├── processStore.ts       # 流程状态
│   │   └── channelStore.ts       # 通道状态
│   │
│   ├── services/                  # 服务层
│   │   ├── stepService.ts        # 工步服务
│   │   ├── processService.ts     # 流程服务
│   │   ├── channelService.ts     # 通道服务
│   │   └── jsonService.ts        # JSON处理服务
│   │
│   ├── utils/                     # 工具函数
│   │   ├── stepValidation.ts     # 工步验证
│   │   ├── processValidation.ts  # 流程验证
│   │   ├── jsonUtils.ts          # JSON工具
│   │   └── formatters.ts         # 格式化工具
│   │
│   └── constants/                 # 常量定义
│       ├── stepConstants.ts      # 工步常量
│       ├── processConstants.ts   # 流程常量
│       └── validationConstants.ts # 验证常量
│
└── messages/process/              # 国际化文件
    ├── en.json                   # 英文翻译
    └── zh.json                   # 中文翻译
```

## 🔧 技术架构设计

### 1. 组件架构原则
- **Server Components优先**: 静态内容和初始数据获取使用RSC
- **Client Components**: 交互性强的组件使用RCC
- **组件复用**: 设计可复用的通道选择器等组件
- **类型安全**: 全面使用TypeScript类型定义

### 2. 状态管理策略
- **Zustand**: 轻量级全局状态管理
- **React Query**: 异步数据获取和缓存
- **React Hook Form + Zod**: 表单管理和验证

### 3. 数据流设计
```
用户操作 → 组件事件 → Store更新 → API调用 → 数据同步 → UI更新
    ↓         ↓         ↓         ↓         ↓         ↓
  表单输入   → 验证规则  → 状态管理 → 服务层   → 缓存更新 → 界面刷新
```

## 📊 核心数据结构

### 工步数据结构
```typescript
interface Step {
  id: string;
  number: number;
  name: string;
  status: StepStatus;
  mode: StepMode;
  parameters: StepParameters;
  auxiliaryParams: AuxiliaryParameters;
  createdAt: Date;
  updatedAt: Date;
}

interface StepParameters {
  electrical: ElectricalParams;
  time: TimeParams;
  termination: TerminationParams;
  sampling: SamplingParams;
}
```

### 通道数据结构
```typescript
interface Channel {
  id: string;
  deviceId: string;
  channelNumber: number;
  status: ChannelStatus;
  batteryInfo?: BatteryInfo;
  isSelected: boolean;
  capabilities: ChannelCapabilities;
}
```

## 🎨 UI设计规范

### 1. 布局规范
- **全屏宽度表格**: 使用完整视口宽度展示工步列表
- **响应式设计**: 支持不同屏幕尺寸的自适应布局
- **模态对话框**: 使用Shadcn UI的Dialog组件

### 2. 交互规范
- **流程引导**: 分步骤的参数配置向导
- **实时验证**: 输入时的即时验证反馈
- **批量操作**: 支持多选和批量处理

### 3. 视觉规范
- **电池图标**: 使用Lucide React图标库
- **高亮显示**: 选中状态的视觉反馈
- **状态指示**: 不同状态的颜色编码

## 🔄 开发实施计划

### Phase 1: 基础架构搭建
1. 创建目录结构和基础文件
2. 定义TypeScript类型和接口
3. 设置Zustand状态管理
4. 配置国际化支持

### Phase 2: 核心组件开发
1. 工步编辑器核心功能
2. 流程引导式对话框
3. 参数验证规则实现
4. JSON导入导出功能

### Phase 3: 视图和交互
1. 全屏工步视图表格
2. 通道选择器组件
3. 工步发送界面
4. 批量操作功能

### Phase 4: 集成和优化
1. 组件集成测试
2. 性能优化
3. 用户体验优化
4. 文档完善

## 📝 开发注意事项

1. **严格遵循规范**: 按照step-specification.md的详细要求实现
2. **组件复用**: 通道选择器等组件要设计为可复用
3. **类型安全**: 确保所有数据结构都有完整的TypeScript类型
4. **国际化**: 所有用户界面文本都要支持多语言
5. **性能考虑**: 大量工步数据的虚拟化渲染
6. **错误处理**: 完善的错误处理和用户反馈机制

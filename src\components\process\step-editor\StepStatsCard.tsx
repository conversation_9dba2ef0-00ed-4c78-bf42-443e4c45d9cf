'use client';

import React, { useState, useEffect, useMemo } from 'react';
import { useTranslations } from 'next-intl';
import { Clock, Zap, AlertCircle, BarChart3 } from 'lucide-react';

import { Step } from '@/lib/process/types/step';

interface StepStatsCardProps {
  steps: Step[];
  className?: string;
}

export function StepStatsCard({ steps, className }: StepStatsCardProps) {
  const t = useTranslations('process.stepEditor');
  const [isExpanded, setIsExpanded] = useState(false);
  const [isHovered, setIsHovered] = useState(false);

  // 使用useMemo优化计算性能
  const statsData = useMemo(() => {
    const totalSteps = steps.length;
    const chargeSteps = steps.filter(step => step.status === 'charge').length;
    const dischargeSteps = steps.filter(step => step.status === 'discharge').length;
    const restSteps = steps.filter(step => step.status === 'rest').length;

    // 计算总预计时间
    const totalEstimatedTime = steps.reduce((total, step) => {
      const timeMatch = step.parameters.time.duration.value.match(/^(\d{1,2}):(\d{1,2}):(\d{1,2})(\.\d{1,3})?$/);
      if (!timeMatch) return total;

      const hours = parseInt(timeMatch[1], 10);
      const minutes = parseInt(timeMatch[2], 10);
      const seconds = parseInt(timeMatch[3], 10);
      const milliseconds = timeMatch[4] ? parseFloat(timeMatch[4]) * 1000 : 0;

      const stepDuration = hours * 3600 + minutes * 60 + seconds + milliseconds / 1000;
      return total + stepDuration;
    }, 0);

    // 计算平均电流
    const averageCurrent = totalSteps > 0
      ? steps.reduce((sum, step) => sum + step.parameters.electrical.current.value, 0) / totalSteps
      : 0;

    // 计算电压范围
    const voltageRanges = steps.map(step => ({
      upper: step.parameters.electrical.upperVoltage.value,
      lower: step.parameters.electrical.lowerVoltage.value
    }));

    const maxVoltage = voltageRanges.length > 0
      ? Math.max(...voltageRanges.map(v => v.upper))
      : 0;

    const minVoltage = voltageRanges.length > 0
      ? Math.min(...voltageRanges.map(v => v.lower))
      : 0;

    return {
      totalSteps,
      chargeSteps,
      dischargeSteps,
      restSteps,
      totalEstimatedTime,
      averageCurrent,
      maxVoltage,
      minVoltage
    };
  }, [steps]);

  // 格式化时间
  const formatDuration = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);
    return `${hours}h ${minutes}m ${secs}s`;
  };

  // 处理鼠标移出延迟收缩
  useEffect(() => {
    let timer: NodeJS.Timeout;
    if (!isHovered && isExpanded) {
      timer = setTimeout(() => {
        setIsExpanded(false);
      }, 1000); // 1秒后收缩
    }
    return () => {
      if (timer) clearTimeout(timer);
    };
  }, [isHovered, isExpanded]);

  const handleMouseEnter = () => {
    setIsHovered(true);
    setIsExpanded(true); // hover时展开
  };

  const handleMouseLeave = () => {
    setIsHovered(false);
  };

  return (
    <div
      className={`fixed bottom-4 left-4 z-50 transition-all duration-300 ease-in-out cursor-pointer ${
        isExpanded ? 'w-80' : 'w-16'
      } ${className}`}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      <div className={`
        backdrop-blur-md bg-background/90 border border-border/50 rounded-lg shadow-xl
        transition-all duration-300 ease-in-out
        ${isExpanded ? 'p-4' : 'p-3'}
        supports-[backdrop-filter]:bg-background/60
      `}>
        {/* 紧凑状态 */}
        {!isExpanded && (
          <div className="flex flex-col items-center space-y-1">
            <BarChart3 className="h-6 w-6 text-foreground" />
            <div className="text-xs text-muted-foreground font-medium">{statsData.totalSteps}</div>
          </div>
        )}

        {/* 展开状态 */}
        {isExpanded && (
          <div className="space-y-4">
            {/* 标题 */}
            <div className="flex items-center space-x-2">
              <BarChart3 className="h-5 w-5 text-foreground" />
              <span className="text-foreground font-medium">工步统计</span>
            </div>

            {/* 基本统计 */}
            <div className="grid grid-cols-2 gap-3">
              <div className="text-center p-3 bg-muted/50 rounded-md backdrop-blur-sm">
                <div className="text-xl font-bold text-foreground">{statsData.totalSteps}</div>
                <div className="text-xs text-muted-foreground">总工步数</div>
              </div>
              <div className="text-center p-3 bg-muted/50 rounded-md backdrop-blur-sm">
                <div className="text-sm font-bold text-foreground">
                  {formatDuration(statsData.totalEstimatedTime)}
                </div>
                <div className="text-xs text-muted-foreground">预计时长</div>
              </div>
            </div>

            {/* 工步类型分布 */}
            <div className="space-y-2">
              <h4 className="font-medium text-sm text-foreground">工步类型分布</h4>
              <div className="flex flex-wrap gap-1">
                {statsData.chargeSteps > 0 && (
                  <div className="px-2 py-1 bg-blue-500/20 rounded text-xs text-foreground">
                    充电: {statsData.chargeSteps}
                  </div>
                )}
                {statsData.dischargeSteps > 0 && (
                  <div className="px-2 py-1 bg-green-500/20 rounded text-xs text-foreground">
                    放电: {statsData.dischargeSteps}
                  </div>
                )}
                {statsData.restSteps > 0 && (
                  <div className="px-2 py-1 bg-gray-500/20 rounded text-xs text-foreground">
                    静置: {statsData.restSteps}
                  </div>
                )}
              </div>
            </div>

            {/* 参数范围 */}
            <div className="space-y-2">
              <h4 className="font-medium text-sm text-foreground">参数范围</h4>
              <div className="space-y-1 text-xs">
                <div className="flex items-center justify-between">
                  <span className="text-muted-foreground">平均电流:</span>
                  <span className="text-foreground">{statsData.averageCurrent.toFixed(1)} mA</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-muted-foreground">电压范围:</span>
                  <span className="text-foreground">
                    {statsData.minVoltage} - {statsData.maxVoltage} mV
                  </span>
                </div>
              </div>
            </div>

            {/* 警告信息 */}
            {statsData.totalSteps === 0 && (
              <div className="flex items-center space-x-2 p-2 bg-yellow-500/10 rounded-md">
                <AlertCircle className="h-3 w-3 text-yellow-600 dark:text-yellow-400" />
                <span className="text-xs text-yellow-600 dark:text-yellow-400">
                  暂无工步数据
                </span>
              </div>
            )}

            {statsData.totalSteps > 0 && statsData.totalEstimatedTime > 86400 && (
              <div className="flex items-center space-x-2 p-2 bg-orange-500/10 rounded-md">
                <Clock className="h-3 w-3 text-orange-600 dark:text-orange-400" />
                <span className="text-xs text-orange-600 dark:text-orange-400">
                  总时长超过24小时
                </span>
              </div>
            )}

            {statsData.averageCurrent > 5000 && (
              <div className="flex items-center space-x-2 p-2 bg-red-500/10 rounded-md">
                <Zap className="h-3 w-3 text-red-600 dark:text-red-400" />
                <span className="text-xs text-red-600 dark:text-red-400">
                  平均电流较高
                </span>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}

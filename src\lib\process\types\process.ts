/**
 * 工艺流程相关类型定义
 */

import { Step } from './step';
import { GlobalParameters } from './globalParameters';

/**
 * 工艺流程状态
 */
export type ProcessStatus = 
  | 'draft'      // 草稿
  | 'active'     // 活跃
  | 'paused'     // 暂停
  | 'completed'  // 完成
  | 'cancelled'; // 取消

/**
 * 工艺流程执行状态
 */
export type ProcessExecutionStatus = 
  | 'pending'    // 等待中
  | 'running'    // 运行中
  | 'paused'     // 暂停
  | 'completed'  // 完成
  | 'failed'     // 失败
  | 'cancelled'; // 取消

/**
 * 工艺流程基本信息
 */
export interface ProcessInfo {
  id: string;
  name: string;
  description?: string;
  creatorName: string;
  creatorId?: string;
  createdAt: Date;
  updatedAt: Date;
  status: ProcessStatus;
  totalSteps: number;
  currentStep: number;
  estimatedDuration?: number; // 预计时长（秒）
  actualDuration?: number;    // 实际时长（秒）
  startTime?: Date;
  endTime?: Date;
  metadata?: Record<string, any>; // 额外元数据
}

/**
 * 工艺流程工步
 */
export interface ProcessStep {
  id: string;
  processId: string;
  stepId: string;
  stepOrder: number;
  stepData: Step; // 完整的工步数据
  createdAt: Date;
}

/**
 * 完整的工艺流程
 */
export interface Process extends ProcessInfo {
  steps: ProcessStep[];
}

/**
 * 工艺流程执行记录
 */
export interface ProcessExecution {
  id: string;
  processId: string;
  deviceId: string;
  channelIds: string[]; // 通道ID列表
  status: ProcessExecutionStatus;
  currentStep: number;
  startTime?: Date;
  endTime?: Date;
  progress: number; // 进度百分比 (0.0-1.0)
  errorMessage?: string;
  executionData?: Record<string, any>; // 执行数据
  createdAt: Date;
  updatedAt: Date;
}

/**
 * 工艺流程模板
 */
export interface ProcessTemplate {
  id: string;
  name: string;
  description?: string;
  category?: string;
  tags: string[];
  templateData: Process; // 模板数据
  creatorName: string;
  isPublic: boolean;
  usageCount: number;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * 工艺流程创建数据
 */
export interface CreateProcessData {
  name: string;
  description?: string;
  creatorName: string;
  creatorId?: string;
  steps: Step[];
  globalParameters?: GlobalParameters;
  metadata?: Record<string, any>;
}

/**
 * 工艺流程更新数据
 */
export interface UpdateProcessData {
  name?: string;
  description?: string;
  status?: ProcessStatus;
  steps?: Step[];
  metadata?: Record<string, any>;
}

/**
 * 工艺流程查询参数
 */
export interface ProcessQueryParams {
  status?: ProcessStatus[];
  creatorId?: string;
  createdAfter?: Date;
  createdBefore?: Date;
  search?: string;
  limit?: number;
  offset?: number;
  sortBy?: 'name' | 'createdAt' | 'updatedAt' | 'status';
  sortOrder?: 'asc' | 'desc';
}

/**
 * 工艺流程执行查询参数
 */
export interface ProcessExecutionQueryParams {
  processId?: string;
  deviceId?: string;
  status?: ProcessExecutionStatus[];
  startedAfter?: Date;
  startedBefore?: Date;
  limit?: number;
  offset?: number;
  sortBy?: 'startTime' | 'createdAt' | 'progress';
  sortOrder?: 'asc' | 'desc';
}

/**
 * 工艺流程统计信息
 */
export interface ProcessStatistics {
  totalProcesses: number;
  activeProcesses: number;
  completedProcesses: number;
  failedProcesses: number;
  averageExecutionTime: number;
  totalExecutionTime: number;
  successRate: number;
}

/**
 * 工艺流程执行结果
 */
export interface ProcessExecutionResult {
  success: boolean;
  executionId?: string;
  error?: string;
  message?: string;
}

/**
 * 工艺流程验证结果
 */
export interface ProcessValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

/**
 * 工艺流程导入导出格式
 */
export interface ProcessExportData {
  process: Process;
  exportedAt: Date;
  exportedBy: string;
  version: string;
}

/**
 * 工艺流程导入数据
 */
export interface ProcessImportData {
  name: string;
  description?: string;
  steps: Step[];
  metadata?: Record<string, any>;
}

/**
 * 工艺流程事件类型
 */
export type ProcessEventType = 
  | 'created'
  | 'updated'
  | 'deleted'
  | 'started'
  | 'paused'
  | 'resumed'
  | 'completed'
  | 'failed'
  | 'cancelled';

/**
 * 工艺流程事件
 */
export interface ProcessEvent {
  type: ProcessEventType;
  processId: string;
  executionId?: string;
  timestamp: Date;
  data?: Record<string, any>;
  userId?: string;
  userName?: string;
}

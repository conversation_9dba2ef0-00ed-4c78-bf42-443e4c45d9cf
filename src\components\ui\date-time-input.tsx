'use client';

import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { format, isValid, parse } from 'date-fns';
import { zhCN } from 'date-fns/locale';
import * as React from 'react';

interface DateTimeInputProps {
  value?: Date;
  onChange?: (date: Date) => void;
  label?: string;
  className?: string;
}

export const DateTimeInput: React.FC<DateTimeInputProps> = ({
  value,
  onChange,
  label,
  className,
}) => {
  const [inputValue, setInputValue] = React.useState('');

  React.useEffect(() => {
    if (value && isValid(value)) {
      setInputValue(format(value, 'yyyy-MM-dd HH:mm', { locale: zhCN }));
    } else {
      setInputValue('');
    }
  }, [value]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setInputValue(newValue);

    // Try to parse the input value
    const parsedDate = parse(newValue, 'yyyy-MM-dd HH:mm', new Date());
    if (isValid(parsedDate)) {
      onChange?.(parsedDate);
    }
  };

  const handleBlur = () => {
    if (value && isValid(value)) {
      setInputValue(format(value, 'yyyy-MM-dd HH:mm', { locale: zhCN }));
    }
  };

  return (
    <div className={className}>
      {label && (
        <Label className="text-xs text-muted-foreground mb-1 block">
          {label}
        </Label>
      )}
      <Input
        type="text"
        value={inputValue}
        onChange={handleInputChange}
        onBlur={handleBlur}
        placeholder="yyyy-MM-dd HH:mm"
        className="h-8 text-xs"
      />
    </div>
  );
};

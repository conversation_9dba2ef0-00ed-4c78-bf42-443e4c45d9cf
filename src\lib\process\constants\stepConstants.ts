/**
 * 工步相关常量定义
 * 基于step-specification.md规范
 */

import { StepStatus, StepMode } from '../types/step';

// 工步状态选项
export const STEP_STATUS_OPTIONS = [
  { value: StepStatus.DISCHARGE, label: '放电', color: '#F44336' },
  { value: StepStatus.CHARGE, label: '充电', color: '#4CAF50' },
  { value: StepStatus.REST, label: '搁置', color: '#FF9800' },
  { value: StepStatus.SUSPEND, label: '挂起', color: '#9E9E9E' },
  { value: StepStatus.CYCLE, label: '循环', color: '#2196F3' }
] as const;

// 工步模式选项
export const STEP_MODE_OPTIONS = [
  { value: StepMode.CC, label: '恒流', description: '恒定电流模式' },
  { value: StepMode.CCCV, label: '恒流恒压', description: '先恒流后恒压' },
  { value: StepMode.CP, label: '恒功率', description: '恒定功率模式' },
  { value: StepMode.CR, label: '恒阻', description: '恒定电阻模式' },
  { value: StepMode.DCIR, label: '直流内阻', description: '直流内阻测试' },
  { value: StepMode.PULSE, label: '脉冲', description: '脉冲充放电' },
  { value: StepMode.CV, label: '恒压', description: '恒定电压模式' },
  { value: StepMode.RATE_CC, label: '倍率恒流', description: '基于倍率的恒流' },
  { value: StepMode.RATE_CV, label: '倍率恒压', description: '基于倍率的恒压' }
] as const;

// 参数默认值
export const DEFAULT_STEP_PARAMETERS = {
  electrical: {
    current: { value: 1000, unit: 'mA' as const, range: [0.1, 10000] as [number, number] },
    upperVoltage: { value: 4200, unit: 'mV' as const, range: [0, 5000] as [number, number] },
    lowerVoltage: { value: 3000, unit: 'mV' as const, range: [0, 5000] as [number, number] }
  },
  time: {
    duration: { value: '00:01:00.000', format: 'hh:mm:ss.ms' as const }
  },
  termination: {
    capacity: { value: 5000, unit: 'mAh' as const, range: [0, 50000] as [number, number] },
    power: { value: 1000, unit: 'mW' as const, range: [0, 10000] as [number, number] },
    upperTemperature: { value: 60, unit: '℃' as const, range: [-40, 100] as [number, number] },
    lowerTemperature: { value: 0, unit: '℃' as const, range: [-40, 100] as [number, number] }
  },
  sampling: {
    samplingInterval: { value: 2, unit: 's' as const, range: [1, 3600] as [number, number] }
  }
} as const;

// 辅助参数默认值
export const DEFAULT_AUXILIARY_PARAMETERS = {
  voltage: {
    enabled: false,
    checkTime: 0,
    upperLimit: 0,
    lowerLimit: 0,
    upperRange: 12,
    lowerRange: 121,
    deviation: 0,
    absoluteValue: 0,
    checkInterval: 0,
    fluctuationValue: 0,
    continuousCount: 0,
    totalCount: 0,
    rateValue: 0,
    comparator: '>' as const,
    timeInterval: 0
  },
  current: {
    enabled: false,
    startTime: 0,
    upperCurrent: 0,
    lowerCurrent: 0,
    currentValue: 0,
    checkInterval: 0,
    dropValue: 0
  },
  capacity: {
    enabled: false,
    ccCapacityLimit: { value: 0, enabled: true },
    cvCapacityLimit: { value: 0, enabled: false }
  },
  samplingInterval: {
    enabled: false,
    voltageDiff: 5000,
    currentDiff: 100000,
    temperatureDiff: 2,
    capacityDiff: 100000,
    powerDiff: 100000
  },
  stepProtection: {
    enabled: false,
    impedanceUpperLimit: 0,
    impedanceLowerLimit: 0
  }
} as const;

// 比较运算符选项
export const COMPARATOR_OPTIONS = [
  { value: '>', label: '大于' },
  { value: '<', label: '小于' },
  { value: '>=', label: '大于等于' },
  { value: '<=', label: '小于等于' },
  { value: '=', label: '等于' }
] as const;

// 参数验证规则
export const VALIDATION_RULES = {
  current: { min: 0.1, max: 10000, precision: 1 },
  voltage: { min: 0, max: 5000, precision: 1 },
  capacity: { min: 0, max: 50000, precision: 1 },
  power: { min: 0, max: 10000, precision: 1 },
  temperature: { min: -40, max: 100, precision: 1 },
  samplingInterval: { min: 1, max: 3600, precision: 0 },
  time: {
    format: /^(\d{1,2}):(\d{1,2}):(\d{1,2})(\.\d{1,3})?$/,
    hours: [0, 99],
    minutes: [0, 59],
    seconds: [0, 59],
    milliseconds: [0, 999]
  }
} as const;

// 错误消息
export const ERROR_MESSAGES = {
  REQUIRED_FIELD: '此字段为必填项',
  INVALID_NUMBER: '请输入有效的数字',
  INVALID_TIME_FORMAT: '时间格式应为 hh:mm:ss.ms',
  VALUE_OUT_OF_RANGE: '数值应在 {min} - {max} 范围内',
  UPPER_LOWER_VOLTAGE: '上限电压应大于等于下限电压',
  UPPER_LOWER_TEMPERATURE: '温度上限应大于温度下限',
  INVALID_FORMULA_SYNTAX: '公式语法错误，请检查变量和运算符'
} as const;

// 工步表格列配置
export const STEP_TABLE_COLUMNS = [
  { key: 'number', label: '序号', width: 80, sortable: true },
  { key: 'name', label: '工步名称', width: 200, sortable: true },
  { key: 'status', label: '状态', width: 100, sortable: true },
  { key: 'mode', label: '模式', width: 120, sortable: true },
  { key: 'current', label: '电流(mA)', width: 120, sortable: false },
  { key: 'voltage', label: '电压范围(mV)', width: 150, sortable: false },
  { key: 'duration', label: '持续时间', width: 120, sortable: false },
  { key: 'capacity', label: '容量(mAh)', width: 120, sortable: false },
  { key: 'createdAt', label: '创建时间', width: 150, sortable: true },
  { key: 'actions', label: '操作', width: 120, sortable: false }
] as const;

// 批量操作选项
export const BATCH_OPERATION_OPTIONS = [
  { value: 'delete', label: '删除选中', icon: 'Trash2', color: 'destructive' },
  { value: 'duplicate', label: '复制选中', icon: 'Copy', color: 'default' },
  { value: 'export', label: '导出选中', icon: 'Download', color: 'default' },
  { value: 'move', label: '移动选中', icon: 'Move', color: 'default' }
] as const;

// 工步导出格式版本
export const EXPORT_FORMAT_VERSION = '1.0.0';

// 分页配置
export const PAGINATION_CONFIG = {
  defaultPageSize: 20,
  pageSizeOptions: [10, 20, 50, 100],
  maxPageSize: 100
} as const;

// 搜索配置
export const SEARCH_CONFIG = {
  debounceMs: 300,
  minSearchLength: 2,
  searchFields: ['name', 'notes'] as const
} as const;



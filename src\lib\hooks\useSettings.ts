'use client';

import { useState, useEffect, useCallback } from 'react';
import { SystemSettings } from '@/lib/types';

const SETTINGS_STORAGE_KEY = 'pdcs-system-settings';

// 默认设置
const defaultSettings: SystemSettings = {
  display: {
    theme: 'system',
    language: 'zh',
    compactMode: false,
    showAnimations: true,
    fontSize: 'medium'
  },
  units: {
    voltage: 'V',
    current: 'A',
    power: 'W',
    capacity: 'Ah',
    energy: 'Wh',
    temperature: '°C',
    resistance: 'Ω',
    time: 's',
    decimalPlaces: 2
  },
  notifications: {
    enabled: true,
    sound: true,
    desktop: true,
    email: false
  },
  monitoring: {
    refreshInterval: 5,
    dataRetention: 30,
    alertThresholds: {
      voltage: { min: 2.5, max: 4.5 },
      current: { min: -10, max: 10 },
      temperature: { min: -10, max: 60 }
    }
  }
};

export function useSettings() {
  const [settings, setSettings] = useState<SystemSettings>(defaultSettings);
  const [isLoading, setIsLoading] = useState(true);

  // 从localStorage加载设置
  const loadSettings = useCallback(() => {
    try {
      const stored = localStorage.getItem(SETTINGS_STORAGE_KEY);
      if (stored) {
        const parsedSettings = JSON.parse(stored);
        // 合并默认设置和存储的设置，确保新增的设置项有默认值
        const mergedSettings = mergeSettings(defaultSettings, parsedSettings);
        setSettings(mergedSettings);
      }
    } catch (error) {
      console.error('Failed to load settings:', error);
      setSettings(defaultSettings);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // 保存设置到localStorage
  const saveSettings = useCallback((newSettings: SystemSettings) => {
    try {
      localStorage.setItem(SETTINGS_STORAGE_KEY, JSON.stringify(newSettings));
      setSettings(newSettings);
      
      // 触发设置变更事件
      window.dispatchEvent(new CustomEvent('settings-changed', {
        detail: newSettings
      }));
    } catch (error) {
      console.error('Failed to save settings:', error);
    }
  }, []);

  // 重置为默认设置
  const resetSettings = useCallback(() => {
    saveSettings(defaultSettings);
  }, [saveSettings]);

  // 更新特定设置
  const updateSettings = useCallback((updates: Partial<SystemSettings>) => {
    const newSettings = { ...settings, ...updates };
    saveSettings(newSettings);
  }, [settings, saveSettings]);

  // 获取格式化的数值
  const formatValue = useCallback((value: number, unit?: keyof SystemSettings['units']) => {
    const decimals = settings.units.decimalPlaces;
    const formatted = value.toFixed(decimals);
    
    if (unit && settings.units[unit]) {
      return `${formatted} ${settings.units[unit]}`;
    }
    
    return formatted;
  }, [settings.units]);

  // 单位转换函数
  const convertUnit = useCallback((
    value: number,
    fromUnit: string,
    toUnit: string
  ): number => {
    // 电压转换
    if (fromUnit === 'V' && toUnit === 'mV') return value * 1000;
    if (fromUnit === 'mV' && toUnit === 'V') return value / 1000;
    
    // 电流转换
    if (fromUnit === 'A' && toUnit === 'mA') return value * 1000;
    if (fromUnit === 'mA' && toUnit === 'A') return value / 1000;
    
    // 功率转换
    if (fromUnit === 'W' && toUnit === 'mW') return value * 1000;
    if (fromUnit === 'mW' && toUnit === 'W') return value / 1000;
    
    // 容量转换
    if (fromUnit === 'Ah' && toUnit === 'mAh') return value * 1000;
    if (fromUnit === 'mAh' && toUnit === 'Ah') return value / 1000;
    
    // 能量转换
    if (fromUnit === 'Wh' && toUnit === 'mWh') return value * 1000;
    if (fromUnit === 'mWh' && toUnit === 'Wh') return value / 1000;
    
    // 电阻转换
    if (fromUnit === 'Ω' && toUnit === 'mΩ') return value * 1000;
    if (fromUnit === 'mΩ' && toUnit === 'Ω') return value / 1000;
    if (fromUnit === 'Ω' && toUnit === 'kΩ') return value / 1000;
    if (fromUnit === 'kΩ' && toUnit === 'Ω') return value * 1000;
    if (fromUnit === 'mΩ' && toUnit === 'kΩ') return value / 1000000;
    if (fromUnit === 'kΩ' && toUnit === 'mΩ') return value * 1000000;
    
    // 温度转换
    if (fromUnit === '°C' && toUnit === '°F') return (value * 9/5) + 32;
    if (fromUnit === '°F' && toUnit === '°C') return (value - 32) * 5/9;
    if (fromUnit === '°C' && toUnit === 'K') return value + 273.15;
    if (fromUnit === 'K' && toUnit === '°C') return value - 273.15;
    if (fromUnit === '°F' && toUnit === 'K') return ((value - 32) * 5/9) + 273.15;
    if (fromUnit === 'K' && toUnit === '°F') return ((value - 273.15) * 9/5) + 32;
    
    // 时间转换
    if (fromUnit === 's' && toUnit === 'min') return value / 60;
    if (fromUnit === 'min' && toUnit === 's') return value * 60;
    if (fromUnit === 's' && toUnit === 'h') return value / 3600;
    if (fromUnit === 'h' && toUnit === 's') return value * 3600;
    if (fromUnit === 'min' && toUnit === 'h') return value / 60;
    if (fromUnit === 'h' && toUnit === 'min') return value * 60;
    
    // 如果没有匹配的转换，返回原值
    return value;
  }, []);

  // 初始化时加载设置
  useEffect(() => {
    loadSettings();
  }, [loadSettings]);

  return {
    settings,
    isLoading,
    saveSettings,
    updateSettings,
    resetSettings,
    formatValue,
    convertUnit
  };
}

// 深度合并设置对象
function mergeSettings(defaultSettings: SystemSettings, userSettings: any): SystemSettings {
  const merged = { ...defaultSettings };
  
  if (userSettings.display) {
    merged.display = { ...defaultSettings.display, ...userSettings.display };
  }
  
  if (userSettings.units) {
    merged.units = { ...defaultSettings.units, ...userSettings.units };
  }
  
  if (userSettings.notifications) {
    merged.notifications = { ...defaultSettings.notifications, ...userSettings.notifications };
  }
  
  if (userSettings.monitoring) {
    merged.monitoring = { ...defaultSettings.monitoring, ...userSettings.monitoring };
    
    if (userSettings.monitoring.alertThresholds) {
      merged.monitoring.alertThresholds = {
        ...defaultSettings.monitoring.alertThresholds,
        ...userSettings.monitoring.alertThresholds
      };
      
      // 确保每个阈值都有min和max
      Object.keys(merged.monitoring.alertThresholds).forEach(key => {
        const threshold = merged.monitoring.alertThresholds[key as keyof typeof merged.monitoring.alertThresholds];
        if (userSettings.monitoring.alertThresholds[key]) {
          Object.assign(threshold, userSettings.monitoring.alertThresholds[key]);
        }
      });
    }
  }
  
  return merged;
}

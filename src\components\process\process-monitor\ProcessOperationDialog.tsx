'use client';

import React, { useState } from 'react';
import { useTranslations } from 'next-intl';
import { 
  RotateCcw, 
  Pause, 
  Play, 
  Target, 
  Square, 
  Link, 
  SkipForward, 
  SkipBack, 
  Save,
  AlertTriangle,
  CheckCircle,
  X
} from 'lucide-react';

import { <PERSON><PERSON> } from '@/components/ui/button';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';

import { ChannelSelector } from '../channel-selector/ChannelSelector';
import { useDeviceStore, useChannels } from '@/lib/device/stores/deviceStore';
import { ChannelSelectionResult } from '@/lib/process/types/channel';

export type ProcessOperation = 
  | 'reset' 
  | 'pause' 
  | 'current_step_pause' 
  | 'specific_step_pause' 
  | 'cancel_pause' 
  | 'continue' 
  | 'jump_next' 
  | 'jump_specific' 
  | 'save';

interface ProcessOperationDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  operation: ProcessOperation | null;
  processName?: string;
  onConfirm: (selectedChannels: string[], operation: ProcessOperation) => void;
}

const OPERATION_CONFIG = {
  reset: {
    title: '复位',
    description: '将选中通道的工艺流程复位到初始状态',
    icon: RotateCcw,
    color: 'text-blue-500',
    variant: 'default' as const,
    requiresConfirmation: true
  },
  pause: {
    title: '暂停',
    description: '暂停选中通道的工艺流程执行',
    icon: Pause,
    color: 'text-yellow-500',
    variant: 'secondary' as const,
    requiresConfirmation: false
  },
  current_step_pause: {
    title: '当前工步预约暂停',
    description: '在当前工步完成后暂停工艺流程',
    icon: Pause,
    color: 'text-orange-500',
    variant: 'secondary' as const,
    requiresConfirmation: false
  },
  specific_step_pause: {
    title: '指定工步预约暂停',
    description: '在指定工步完成后暂停工艺流程',
    icon: Target,
    color: 'text-purple-500',
    variant: 'secondary' as const,
    requiresConfirmation: false
  },
  cancel_pause: {
    title: '取消预约暂停',
    description: '取消之前设置的预约暂停',
    icon: X,
    color: 'text-gray-500',
    variant: 'outline' as const,
    requiresConfirmation: false
  },
  continue: {
    title: '继续',
    description: '继续执行暂停的工艺流程',
    icon: Play,
    color: 'text-green-500',
    variant: 'default' as const,
    requiresConfirmation: false
  },
  jump_next: {
    title: '跳转到下一个工步',
    description: '跳过当前工步，直接执行下一个工步',
    icon: SkipForward,
    color: 'text-indigo-500',
    variant: 'secondary' as const,
    requiresConfirmation: true
  },
  jump_specific: {
    title: '跳转到指定工步',
    description: '跳转到指定的工步继续执行',
    icon: SkipBack,
    color: 'text-cyan-500',
    variant: 'secondary' as const,
    requiresConfirmation: true
  },
  save: {
    title: '寄存',
    description: '保存当前工艺流程状态到寄存区',
    icon: Save,
    color: 'text-teal-500',
    variant: 'outline' as const,
    requiresConfirmation: false
  }
};

export function ProcessOperationDialog({
  open,
  onOpenChange,
  operation,
  processName,
  onConfirm
}: ProcessOperationDialogProps) {
  const t = useTranslations('process.monitor');
  const channels = useChannels();
  const [selectedChannels, setSelectedChannels] = useState<string[]>([]);
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [isExecuting, setIsExecuting] = useState(false);
  const [operationResult, setOperationResult] = useState<{
    success: boolean;
    message: string;
    channels: string[];
  } | null>(null);

  const config = operation ? OPERATION_CONFIG[operation] : null;
  const IconComponent = config?.icon;

  const handleChannelSelection = (result: ChannelSelectionResult) => {
    setSelectedChannels(result.selectedChannelIds);
  };

  const handleNext = () => {
    if (selectedChannels.length === 0) {
      alert('请至少选择一个通道');
      return;
    }

    if (config?.requiresConfirmation) {
      setShowConfirmation(true);
    } else {
      handleExecute();
    }
  };

  const handleExecute = async () => {
    if (!operation) return;

    setIsExecuting(true);
    
    try {
      // 模拟操作执行
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // 模拟操作结果
      const success = Math.random() > 0.1; // 90% 成功率
      
      setOperationResult({
        success,
        message: success 
          ? `${config?.title}操作已成功执行`
          : `${config?.title}操作执行失败，请检查设备连接状态`,
        channels: selectedChannels
      });

      if (success) {
        onConfirm(selectedChannels, operation);
      }
    } catch (error) {
      setOperationResult({
        success: false,
        message: `操作执行出错: ${error}`,
        channels: selectedChannels
      });
    } finally {
      setIsExecuting(false);
      setShowConfirmation(false);
    }
  };

  const handleClose = () => {
    setSelectedChannels([]);
    setShowConfirmation(false);
    setOperationResult(null);
    setIsExecuting(false);
    onOpenChange(false);
  };

  if (!operation || !config) return null;

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="max-w-4xl w-full max-h-[90vh] overflow-hidden flex flex-col">
        <DialogHeader className="flex-shrink-0">
          <DialogTitle className="flex items-center space-x-2">
            {IconComponent && <IconComponent className={`h-5 w-5 ${config.color}`} />}
            <span>{config.title}</span>
            {processName && (
              <Badge variant="outline" className="ml-2">
                {processName}
              </Badge>
            )}
          </DialogTitle>
          <DialogDescription>
            {config.description}
          </DialogDescription>
        </DialogHeader>

        <div className="flex-1 overflow-hidden">
          {operationResult ? (
            // 显示操作结果
            <div className="space-y-4 p-4 min-w-0">
              <Alert variant={operationResult.success ? "default" : "destructive"}>
                <div className="flex items-center space-x-2 min-w-0">
                  {operationResult.success ? (
                    <CheckCircle className="h-4 w-4 text-green-500 flex-shrink-0" />
                  ) : (
                    <AlertTriangle className="h-4 w-4 flex-shrink-0" />
                  )}
                  <AlertDescription className="flex-1 min-w-0">{operationResult.message}</AlertDescription>
                </div>
              </Alert>

              <div className="space-y-2">
                <h4 className="font-medium">操作通道:</h4>
                <div className="flex flex-wrap gap-2">
                  {operationResult.channels.map(channelId => {
                    const channel = channels.find(c => c.id === channelId);
                    return (
                      <Badge key={channelId} variant="secondary">
                        {channel?.deviceId}-{channel?.channelNumber}
                      </Badge>
                    );
                  })}
                </div>
              </div>

              <div className="flex justify-end space-x-2 pt-4">
                <Button onClick={handleClose}>
                  关闭
                </Button>
              </div>
            </div>
          ) : showConfirmation ? (
            // 显示确认界面
            <div className="space-y-4 p-4">
              <Alert>
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  确定要对以下通道执行 <strong>{config.title}</strong> 操作吗？此操作可能会影响正在进行的测试。
                </AlertDescription>
              </Alert>

              <div className="space-y-2">
                <h4 className="font-medium">目标通道:</h4>
                <div className="flex flex-wrap gap-2">
                  {selectedChannels.map(channelId => {
                    const channel = channels.find(c => c.id === channelId);
                    return (
                      <Badge key={channelId} variant="secondary">
                        {channel?.deviceId}-{channel?.channelNumber}
                      </Badge>
                    );
                  })}
                </div>
              </div>

              <div className="flex justify-end space-x-2 pt-4">
                <Button 
                  variant="outline" 
                  onClick={() => setShowConfirmation(false)}
                  disabled={isExecuting}
                >
                  取消
                </Button>
                <Button 
                  onClick={handleExecute}
                  disabled={isExecuting}
                  variant={config.variant}
                >
                  {isExecuting ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2" />
                      执行中...
                    </>
                  ) : (
                    `确认${config.title}`
                  )}
                </Button>
              </div>
            </div>
          ) : (
            // 显示通道选择器
            <div className="flex-1 overflow-hidden">
              <ChannelSelector
                selectedChannelIds={selectedChannels}
                onSelectionChange={handleChannelSelection}
                title="选择目标通道"
                description={`选择要执行${config.title}操作的通道`}
                config={{
                  multiSelect: true,
                  showBatteryInfo: true,
                  showRealTimeData: false,
                  groupByDevice: true,
                  compactMode: false
                }}
              />

              <Separator className="my-4" />

              <div className="flex justify-end space-x-2 p-4">
                <Button variant="outline" onClick={handleClose}>
                  取消
                </Button>
                <Button 
                  onClick={handleNext}
                  disabled={selectedChannels.length === 0}
                  variant={config.variant}
                >
                  下一步
                </Button>
              </div>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}

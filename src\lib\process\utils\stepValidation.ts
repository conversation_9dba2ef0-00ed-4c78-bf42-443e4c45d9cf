/**
 * 工步验证工具函数
 * 基于step-specification.md规范
 */

import { z } from 'zod';
import { StepFormData, StepValidationError, StepStatus, StepMode } from '../types/step';
import { VALIDATION_RULES, ERROR_MESSAGES } from '../constants/stepConstants';

// 时间格式验证
const timeSchema = z.string().regex(
  VALIDATION_RULES.time.format,
  ERROR_MESSAGES.INVALID_TIME_FORMAT
);

// 数值范围验证函数
const createNumberSchema = (min: number, max: number, precision: number = 2) => {
  return z.number()
    .min(min, `数值应大于等于 ${min}`)
    .max(max, `数值应小于等于 ${max}`)
    .refine(
      (val) => Number(val.toFixed(precision)) === val,
      `精度应为 ${precision} 位小数`
    );
};

// 电气参数验证
const electricalParamsSchema = z.object({
  current: z.object({
    value: createNumberSchema(
      VALIDATION_RULES.current.min,
      VALIDATION_RULES.current.max,
      VALIDATION_RULES.current.precision
    ),
    unit: z.literal('mA'),
    range: z.tuple([z.number(), z.number()])
  }),
  upperVoltage: z.object({
    value: createNumberSchema(
      VALIDATION_RULES.voltage.min,
      VALIDATION_RULES.voltage.max,
      VALIDATION_RULES.voltage.precision
    ),
    unit: z.literal('mV'),
    range: z.tuple([z.number(), z.number()])
  }),
  lowerVoltage: z.object({
    value: createNumberSchema(
      VALIDATION_RULES.voltage.min,
      VALIDATION_RULES.voltage.max,
      VALIDATION_RULES.voltage.precision
    ),
    unit: z.literal('mV'),
    range: z.tuple([z.number(), z.number()])
  })
});

// 时间参数验证
const timeParamsSchema = z.object({
  duration: z.object({
    value: timeSchema,
    format: z.literal('hh:mm:ss.ms')
  })
});

// 终止条件参数验证
const terminationParamsSchema = z.object({
  capacity: z.object({
    value: createNumberSchema(
      VALIDATION_RULES.capacity.min,
      VALIDATION_RULES.capacity.max,
      VALIDATION_RULES.capacity.precision
    ),
    unit: z.literal('mAh'),
    range: z.tuple([z.number(), z.number()])
  }),
  power: z.object({
    value: createNumberSchema(
      VALIDATION_RULES.power.min,
      VALIDATION_RULES.power.max,
      VALIDATION_RULES.power.precision
    ),
    unit: z.literal('mW'),
    range: z.tuple([z.number(), z.number()])
  }),
  upperTemperature: z.object({
    value: createNumberSchema(
      VALIDATION_RULES.temperature.min,
      VALIDATION_RULES.temperature.max,
      VALIDATION_RULES.temperature.precision
    ),
    unit: z.literal('℃'),
    range: z.tuple([z.number(), z.number()])
  }),
  lowerTemperature: z.object({
    value: createNumberSchema(
      VALIDATION_RULES.temperature.min,
      VALIDATION_RULES.temperature.max,
      VALIDATION_RULES.temperature.precision
    ),
    unit: z.literal('℃'),
    range: z.tuple([z.number(), z.number()])
  })
});

// 采样参数验证
const samplingParamsSchema = z.object({
  samplingInterval: z.object({
    value: createNumberSchema(
      VALIDATION_RULES.samplingInterval.min,
      VALIDATION_RULES.samplingInterval.max,
      VALIDATION_RULES.samplingInterval.precision
    ),
    unit: z.literal('s'),
    range: z.tuple([z.number(), z.number()])
  })
});

// 主要参数验证
const stepParametersSchema = z.object({
  electrical: electricalParamsSchema,
  time: timeParamsSchema,
  termination: terminationParamsSchema,
  sampling: samplingParamsSchema
});

// 辅助参数验证
const auxiliaryParametersSchema = z.object({
  voltage: z.object({
    enabled: z.boolean(),
    checkTime: z.number().min(0).max(3600),
    upperLimit: z.number().min(0).max(5000),
    lowerLimit: z.number().min(0).max(5000),
    upperRange: z.number().min(0).max(5000),
    lowerRange: z.number().min(0).max(5000),
    deviation: z.number().min(0).max(1000),
    absoluteValue: z.number().min(0).max(1000),
    checkInterval: z.number().min(0).max(60),
    fluctuationValue: z.number().min(0).max(1000),
    continuousCount: z.number().min(0).max(100),
    totalCount: z.number().min(0).max(1000),
    rateValue: z.number().min(0).max(1000),
    comparator: z.enum(['>', '<', '>=', '<=', '=']),
    timeInterval: z.number().min(0).max(60)
  }),
  current: z.object({
    enabled: z.boolean(),
    startTime: z.number().min(0).max(3600),
    upperCurrent: z.number().min(0).max(10000),
    lowerCurrent: z.number().min(0).max(10000),
    currentValue: z.number().min(0).max(10000),
    checkInterval: z.number().min(0).max(60),
    dropValue: z.number().min(0).max(10000)
  }),
  capacity: z.object({
    enabled: z.boolean(),
    ccCapacityLimit: z.object({
      value: z.number().min(0).max(50000),
      enabled: z.boolean()
    }),
    cvCapacityLimit: z.object({
      value: z.number().min(0).max(50000),
      enabled: z.boolean()
    })
  }),
  samplingInterval: z.object({
    enabled: z.boolean(),
    voltageDiff: z.number().min(0).max(10000),
    currentDiff: z.number().min(0).max(100000),
    temperatureDiff: z.number().min(0).max(100),
    capacityDiff: z.number().min(0).max(100000),
    powerDiff: z.number().min(0).max(100000)
  }),
  stepProtection: z.object({
    enabled: z.boolean(),
    impedanceUpperLimit: z.number().min(0).max(10000),
    impedanceLowerLimit: z.number().min(0).max(10000)
  })
});

// 工步表单数据验证
const stepFormDataSchema = z.object({
  name: z.string().min(1, ERROR_MESSAGES.REQUIRED_FIELD).max(100, '名称长度不能超过100字符'),
  status: z.nativeEnum(StepStatus),
  mode: z.nativeEnum(StepMode),
  parameters: stepParametersSchema,
  auxiliaryParams: auxiliaryParametersSchema,
  notes: z.string().max(500, '备注长度不能超过500字符').optional()
});

/**
 * 验证工步表单数据
 */
export function validateStepFormData(data: StepFormData): StepValidationError[] {
  const errors: StepValidationError[] = [];

  try {
    // 基础验证
    stepFormDataSchema.parse(data);

    // 业务逻辑验证
    const businessErrors = validateBusinessLogic(data);
    errors.push(...businessErrors);

  } catch (error) {
    if (error instanceof z.ZodError) {
      error.errors.forEach((err) => {
        errors.push({
          field: err.path.join('.'),
          message: err.message,
          code: err.code
        });
      });
    }
  }

  return errors;
}

/**
 * 业务逻辑验证
 */
function validateBusinessLogic(data: StepFormData): StepValidationError[] {
  const errors: StepValidationError[] = [];

  // 电压范围验证
  if (data.parameters.electrical.upperVoltage.value < data.parameters.electrical.lowerVoltage.value) {
    errors.push({
      field: 'parameters.electrical.upperVoltage',
      message: ERROR_MESSAGES.UPPER_LOWER_VOLTAGE,
      code: 'VOLTAGE_RANGE_ERROR'
    });
  }

  // 温度范围验证
  if (data.parameters.termination.upperTemperature.value <= data.parameters.termination.lowerTemperature.value) {
    errors.push({
      field: 'parameters.termination.upperTemperature',
      message: ERROR_MESSAGES.UPPER_LOWER_TEMPERATURE,
      code: 'TEMPERATURE_RANGE_ERROR'
    });
  }

  // 时间格式验证
  if (!validateTimeFormat(data.parameters.time.duration.value)) {
    errors.push({
      field: 'parameters.time.duration',
      message: ERROR_MESSAGES.INVALID_TIME_FORMAT,
      code: 'TIME_FORMAT_ERROR'
    });
  }

  return errors;
}

/**
 * 验证时间格式
 */
export function validateTimeFormat(timeString: string): boolean {
  const match = timeString.match(VALIDATION_RULES.time.format);
  if (!match) return false;

  const [, hours, minutes, seconds, milliseconds] = match;
  const h = parseInt(hours, 10);
  const m = parseInt(minutes, 10);
  const s = parseInt(seconds, 10);
  const ms = milliseconds ? parseInt(milliseconds.slice(1), 10) : 0;

  return (
    h >= VALIDATION_RULES.time.hours[0] && h <= VALIDATION_RULES.time.hours[1] &&
    m >= VALIDATION_RULES.time.minutes[0] && m <= VALIDATION_RULES.time.minutes[1] &&
    s >= VALIDATION_RULES.time.seconds[0] && s <= VALIDATION_RULES.time.seconds[1] &&
    ms >= VALIDATION_RULES.time.milliseconds[0] && ms <= VALIDATION_RULES.time.milliseconds[1]
  );
}

/**
 * 验证单个字段
 */
export function validateField(fieldPath: string, value: any, data: StepFormData): StepValidationError[] {
  const errors: StepValidationError[] = [];

  try {
    // 根据字段路径进行特定验证
    switch (fieldPath) {
      case 'parameters.electrical.current.value':
        createNumberSchema(
          VALIDATION_RULES.current.min,
          VALIDATION_RULES.current.max,
          VALIDATION_RULES.current.precision
        ).parse(value);
        break;
      
      case 'parameters.electrical.upperVoltage.value':
      case 'parameters.electrical.lowerVoltage.value':
        createNumberSchema(
          VALIDATION_RULES.voltage.min,
          VALIDATION_RULES.voltage.max,
          VALIDATION_RULES.voltage.precision
        ).parse(value);
        
        // 电压范围验证
        if (fieldPath === 'parameters.electrical.upperVoltage.value' && 
            value < data.parameters.electrical.lowerVoltage.value) {
          errors.push({
            field: fieldPath,
            message: ERROR_MESSAGES.UPPER_LOWER_VOLTAGE,
            code: 'VOLTAGE_RANGE_ERROR'
          });
        }
        break;

      case 'parameters.time.duration.value':
        if (!validateTimeFormat(value)) {
          errors.push({
            field: fieldPath,
            message: ERROR_MESSAGES.INVALID_TIME_FORMAT,
            code: 'TIME_FORMAT_ERROR'
          });
        }
        break;

      default:
        // 通用验证
        break;
    }
  } catch (error) {
    if (error instanceof z.ZodError) {
      error.errors.forEach((err) => {
        errors.push({
          field: fieldPath,
          message: err.message,
          code: err.code
        });
      });
    }
  }

  return errors;
}

/**
 * 检查工步参数完整性
 */
export function checkStepCompleteness(data: StepFormData): boolean {
  const requiredFields = [
    'name',
    'status',
    'mode',
    'parameters.electrical.current.value',
    'parameters.electrical.upperVoltage.value',
    'parameters.electrical.lowerVoltage.value',
    'parameters.time.duration.value'
  ];

  return requiredFields.every(field => {
    const value = getNestedValue(data, field);
    return value !== undefined && value !== null && value !== '';
  });
}

/**
 * 获取嵌套对象的值
 */
function getNestedValue(obj: any, path: string): any {
  return path.split('.').reduce((current, key) => current?.[key], obj);
}

'use client';

import React, { useMemo } from 'react';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  ReferenceLine,
  ComposedChart,
  Bar
} from 'recharts';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { BatteryDataRecord, StepType } from '@/lib/types';
import { useTranslations } from 'next-intl';

interface CapacityAnalysisChartProps {
  data: BatteryDataRecord[];
  height?: number;
}

interface CycleCapacityData {
  cycle: number;
  chargeCapacity: number;
  dischargeCapacity: number;
  efficiency: number;
  capacityRetention: number;
  energyEfficiency: number;
}

export function CapacityAnalysisChart({
  data,
  height = 400
}: CapacityAnalysisChartProps) {
  const t = useTranslations('batteryAnalysis.charts');

  // 处理循环容量数据
  const cycleData = useMemo(() => {
    // 按循环号分组
    const cycleGroups = data.reduce((groups, record) => {
      const cycle = record.cycleNumber;
      if (!groups[cycle]) {
        groups[cycle] = [];
      }
      groups[cycle].push(record);
      return groups;
    }, {} as Record<number, BatteryDataRecord[]>);

    // 计算每个循环的容量数据
    const cycleCapacityData: CycleCapacityData[] = [];
    let initialDischargeCapacity = 0;

    Object.entries(cycleGroups)
      .sort(([a], [b]) => parseInt(a) - parseInt(b))
      .forEach(([cycleStr, records]) => {
        const cycle = parseInt(cycleStr);
        
        // 找到该循环的最大充电和放电容量
        const chargeRecords = records.filter(r => r.stepType === StepType.CC_CHARGE);
        const dischargeRecords = records.filter(r => r.stepType === StepType.CC_DISCHARGE);
        
        const maxChargeCapacity = Math.max(...chargeRecords.map(r => r.chargeCapacity), 0);
        const maxDischargeCapacity = Math.max(...dischargeRecords.map(r => r.dischargeCapacity), 0);
        
        // 计算能量效率
        const maxChargeEnergy = Math.max(...chargeRecords.map(r => r.chargeEnergy), 0);
        const maxDischargeEnergy = Math.max(...dischargeRecords.map(r => r.dischargeEnergy), 0);
        const energyEfficiency = maxChargeEnergy > 0 ? (maxDischargeEnergy / maxChargeEnergy) * 100 : 0;
        
        // 设置初始容量（第一个循环的放电容量）
        if (cycle === 1 || initialDischargeCapacity === 0) {
          initialDischargeCapacity = maxDischargeCapacity;
        }
        
        // 计算容量保持率
        const capacityRetention = initialDischargeCapacity > 0 
          ? (maxDischargeCapacity / initialDischargeCapacity) * 100 
          : 100;
        
        // 计算库仑效率
        const efficiency = maxChargeCapacity > 0 
          ? (maxDischargeCapacity / maxChargeCapacity) * 100 
          : 0;

        cycleCapacityData.push({
          cycle,
          chargeCapacity: maxChargeCapacity,
          dischargeCapacity: maxDischargeCapacity,
          efficiency,
          capacityRetention,
          energyEfficiency
        });
      });

    return cycleCapacityData;
  }, [data]);

  // 计算统计信息
  const stats = useMemo(() => {
    if (cycleData.length === 0) return null;

    const dischargeCapacities = cycleData.map(d => d.dischargeCapacity);
    const efficiencies = cycleData.map(d => d.efficiency);
    const retentions = cycleData.map(d => d.capacityRetention);

    const initialCapacity = dischargeCapacities[0] || 0;
    const currentCapacity = dischargeCapacities[dischargeCapacities.length - 1] || 0;
    const avgEfficiency = efficiencies.reduce((sum, eff) => sum + eff, 0) / efficiencies.length;
    const currentRetention = retentions[retentions.length - 1] || 0;

    // 计算容量衰减率 (每循环)
    const capacityFadeRate = cycleData.length > 1 
      ? ((initialCapacity - currentCapacity) / initialCapacity / cycleData.length) * 100
      : 0;

    return {
      totalCycles: cycleData.length,
      initialCapacity,
      currentCapacity,
      capacityFadeRate,
      avgEfficiency,
      currentRetention
    };
  }, [cycleData]);

  // 自定义工具提示
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload as CycleCapacityData;
      return (
        <div className="bg-background border rounded-lg p-3 shadow-lg">
          <p className="font-medium mb-2">{t('capacity.tooltip.title')} {label}</p>
          <div className="space-y-1 text-sm">
            <div className="flex justify-between gap-4">
              <span className="text-muted-foreground">{t('capacity.chargeCapacity')}:</span>
              <span className="font-medium">{data.chargeCapacity.toFixed(4)} Ah</span>
            </div>
            <div className="flex justify-between gap-4">
              <span className="text-muted-foreground">{t('capacity.dischargeCapacity')}:</span>
              <span className="font-medium">{data.dischargeCapacity.toFixed(4)} Ah</span>
            </div>
            <div className="flex justify-between gap-4">
              <span className="text-muted-foreground">{t('capacity.efficiency')}:</span>
              <span className="font-medium">{data.efficiency.toFixed(2)}%</span>
            </div>
            <div className="flex justify-between gap-4">
              <span className="text-muted-foreground">{t('capacity.retention')}:</span>
              <span className="font-medium">{data.capacityRetention.toFixed(2)}%</span>
            </div>
            <div className="flex justify-between gap-4">
              <span className="text-muted-foreground">{t('capacity.energyEfficiency')}:</span>
              <span className="font-medium">{data.energyEfficiency.toFixed(2)}%</span>
            </div>
          </div>
        </div>
      );
    }
    return null;
  };

  if (cycleData.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>{t('capacity.title')}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-64 text-muted-foreground">
            {t('capacity.noData')}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle>{t('capacity.title')}</CardTitle>
          {stats && (
            <div className="flex gap-2">
              <Badge variant="outline">
                {t('capacity.totalCycles')}: {stats.totalCycles}
              </Badge>
              <Badge variant="outline">
                {t('capacity.currentRetention')}: {stats.currentRetention.toFixed(1)}%
              </Badge>
            </div>
          )}
        </div>
        {stats && (
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div>
              <span className="text-muted-foreground">{t('capacity.initialCapacity')}:</span>
              <div className="font-medium">{stats.initialCapacity.toFixed(4)} Ah</div>
            </div>
            <div>
              <span className="text-muted-foreground">{t('capacity.currentCapacity')}:</span>
              <div className="font-medium">{stats.currentCapacity.toFixed(4)} Ah</div>
            </div>
            <div>
              <span className="text-muted-foreground">{t('capacity.fadeRate')}:</span>
              <div className="font-medium">{stats.capacityFadeRate.toFixed(4)}%/cycle</div>
            </div>
            <div>
              <span className="text-muted-foreground">{t('capacity.avgEfficiency')}:</span>
              <div className="font-medium">{stats.avgEfficiency.toFixed(2)}%</div>
            </div>
          </div>
        )}
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="capacity" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="capacity">{t('capacity.tabs.capacity')}</TabsTrigger>
            <TabsTrigger value="efficiency">{t('capacity.tabs.efficiency')}</TabsTrigger>
            <TabsTrigger value="retention">{t('capacity.tabs.retention')}</TabsTrigger>
          </TabsList>
          
          <TabsContent value="capacity" className="mt-4">
            <ResponsiveContainer width="100%" height={height}>
              <ComposedChart data={cycleData} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
                <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
                <XAxis 
                  dataKey="cycle"
                  label={{ value: t('capacity.cycleAxis'), position: 'insideBottom', offset: -5 }}
                />
                <YAxis 
                  label={{ value: t('capacity.capacityAxis'), angle: -90, position: 'insideLeft' }}
                />
                <Tooltip content={<CustomTooltip />} />
                <Legend />
                
                <Bar 
                  dataKey="chargeCapacity" 
                  fill="#22c55e" 
                  name={t('capacity.chargeCapacity')}
                  opacity={0.7}
                />
                <Bar 
                  dataKey="dischargeCapacity" 
                  fill="#ef4444" 
                  name={t('capacity.dischargeCapacity')}
                  opacity={0.7}
                />
              </ComposedChart>
            </ResponsiveContainer>
          </TabsContent>
          
          <TabsContent value="efficiency" className="mt-4">
            <ResponsiveContainer width="100%" height={height}>
              <LineChart data={cycleData} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
                <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
                <XAxis 
                  dataKey="cycle"
                  label={{ value: t('capacity.cycleAxis'), position: 'insideBottom', offset: -5 }}
                />
                <YAxis 
                  domain={[90, 105]}
                  label={{ value: t('capacity.efficiencyAxis'), angle: -90, position: 'insideLeft' }}
                />
                <Tooltip content={<CustomTooltip />} />
                <Legend />
                
                <Line 
                  type="monotone" 
                  dataKey="efficiency" 
                  stroke="#3b82f6" 
                  strokeWidth={2}
                  name={t('capacity.coulombicEfficiency')}
                />
                <Line 
                  type="monotone" 
                  dataKey="energyEfficiency" 
                  stroke="#8b5cf6" 
                  strokeWidth={2}
                  name={t('capacity.energyEfficiency')}
                />
                
                <ReferenceLine y={100} stroke="#6b7280" strokeDasharray="5 5" />
              </LineChart>
            </ResponsiveContainer>
          </TabsContent>
          
          <TabsContent value="retention" className="mt-4">
            <ResponsiveContainer width="100%" height={height}>
              <LineChart data={cycleData} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
                <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
                <XAxis 
                  dataKey="cycle"
                  label={{ value: t('capacity.cycleAxis'), position: 'insideBottom', offset: -5 }}
                />
                <YAxis 
                  domain={['dataMin - 5', 105]}
                  label={{ value: t('capacity.retentionAxis'), angle: -90, position: 'insideLeft' }}
                />
                <Tooltip content={<CustomTooltip />} />
                <Legend />
                
                <Line 
                  type="monotone" 
                  dataKey="capacityRetention" 
                  stroke="#f59e0b" 
                  strokeWidth={2}
                  name={t('capacity.capacityRetention')}
                />
                
                <ReferenceLine y={80} stroke="#ef4444" strokeDasharray="5 5" label="80% EOL" />
                <ReferenceLine y={100} stroke="#6b7280" strokeDasharray="5 5" />
              </LineChart>
            </ResponsiveContainer>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}

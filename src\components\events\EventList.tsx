'use client';

import { useState } from 'react';
import { useTranslations } from 'next-intl';
import { useEventStore } from '@/stores/eventStore';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { 
  Clock, 
  AlertTriangle, 
  Info, 
  XCircle, 
  CheckCircle,
  User,
  Settings,
  Monitor,
  ChevronRight,
  MoreHorizontal,
} from 'lucide-react';
import { Event, EventLevel, EventCategory, EventViewMode } from '@/types/events';
import { cn } from '@/lib/utils';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

interface EventListProps {
  className?: string;
}

export function EventList({ className }: EventListProps) {
  const t = useTranslations('events');
  const [selectedEvents, setSelectedEvents] = useState<string[]>([]);
  
  const {
    filteredEvents,
    pagination,
    viewMode,
    isLoading,
    selectEvent,
    toggleEventSelection,
    selectedEventIds,
    acknowledgeEvent,
    setPage,
  } = useEventStore();

  // 获取级别颜色
  const getLevelColor = (level: EventLevel) => {
    switch (level) {
      case EventLevel.CRITICAL:
        return 'bg-red-500 text-white';
      case EventLevel.ERROR:
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      case EventLevel.WARNING:
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      case EventLevel.INFO:
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  // 获取级别图标
  const getLevelIcon = (level: EventLevel) => {
    switch (level) {
      case EventLevel.CRITICAL:
        return <XCircle className="h-4 w-4" />;
      case EventLevel.ERROR:
        return <AlertTriangle className="h-4 w-4" />;
      case EventLevel.WARNING:
        return <AlertTriangle className="h-4 w-4" />;
      case EventLevel.INFO:
        return <Info className="h-4 w-4" />;
      default:
        return <Info className="h-4 w-4" />;
    }
  };

  // 获取类别图标
  const getCategoryIcon = (category: EventCategory) => {
    switch (category) {
      case EventCategory.DEVICE:
        return <Monitor className="h-4 w-4" />;
      case EventCategory.CHANNEL:
        return <Settings className="h-4 w-4" />;
      case EventCategory.USER:
        return <User className="h-4 w-4" />;
      case EventCategory.SYSTEM:
        return <Settings className="h-4 w-4" />;
      default:
        return <Info className="h-4 w-4" />;
    }
  };

  // 格式化时间
  const formatTime = (timestamp: number) => {
    return new Date(timestamp).toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
    });
  };

  // 格式化相对时间
  const formatRelativeTime = (timestamp: number) => {
    const now = Date.now();
    const diff = now - timestamp;
    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));

    if (minutes < 1) return '刚刚';
    if (minutes < 60) return `${minutes}分钟前`;
    if (hours < 24) return `${hours}小时前`;
    if (days < 7) return `${days}天前`;
    return formatTime(timestamp);
  };

  // 处理事件点击
  const handleEventClick = (event: Event) => {
    selectEvent(event);
  };

  // 处理事件确认
  const handleAcknowledge = (eventId: string) => {
    acknowledgeEvent(eventId, '当前用户'); // 实际应用中应该从用户上下文获取
  };

  // 分页的事件
  const paginatedEvents = filteredEvents.slice(
    (pagination.page - 1) * pagination.pageSize,
    pagination.page * pagination.pageSize
  );

  if (isLoading) {
    return (
      <Card className={className}>
        <CardContent className="flex items-center justify-center py-8">
          <div className="flex items-center gap-2">
            <div className="h-4 w-4 animate-spin rounded-full border-2 border-primary border-t-transparent" />
            <span>{t('loading')}</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (paginatedEvents.length === 0) {
    return (
      <Card className={className}>
        <CardContent className="flex items-center justify-center py-8">
          <div className="text-center">
            <Info className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <p className="text-muted-foreground">{t('noEvents')}</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle>{t('eventList')}</CardTitle>
        <CardDescription>
          {t('eventListDescription')}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {paginatedEvents.map((event) => (
            <div
              key={event.id}
              className={cn(
                "flex items-start gap-4 p-4 border rounded-lg transition-colors cursor-pointer",
                "hover:bg-accent/50",
                selectedEventIds.includes(event.id) && "bg-accent",
                event.acknowledged && "opacity-75"
              )}
              onClick={() => handleEventClick(event)}
            >
              {/* 选择框 */}
              <Checkbox
                checked={selectedEventIds.includes(event.id)}
                onCheckedChange={() => toggleEventSelection(event.id)}
                onClick={(e) => e.stopPropagation()}
              />

              {/* 事件内容 */}
              <div className="flex items-start gap-3 min-w-0 flex-1">
                {/* 级别和类别图标 */}
                <div className="flex items-center gap-2">
                  {getLevelIcon(event.level)}
                  {getCategoryIcon(event.category)}
                </div>

                {/* 事件信息 */}
                <div className="min-w-0 flex-1">
                  <div className="flex items-center gap-2 mb-1">
                    <Badge className={getLevelColor(event.level)}>
                      {t(`levels.${event.level}`)}
                    </Badge>
                    <Badge variant="outline">
                      {t(`categories.${event.category}`)}
                    </Badge>
                    {event.acknowledged && (
                      <Badge variant="secondary">
                        <CheckCircle className="h-3 w-3 mr-1" />
                        已确认
                      </Badge>
                    )}
                  </div>
                  
                  <div className="font-medium mb-1">{event.title}</div>
                  <div className="text-sm text-muted-foreground mb-2">
                    {event.description}
                  </div>
                  
                  {/* 事件源信息 */}
                  <div className="flex items-center gap-4 text-xs text-muted-foreground">
                    {event.source.deviceName && (
                      <span>设备: {event.source.deviceName}</span>
                    )}
                    {event.source.channelNumber && (
                      <span>通道: {event.source.channelNumber}</span>
                    )}
                    {event.source.userName && (
                      <span>用户: {event.source.userName}</span>
                    )}
                  </div>
                  
                  {/* 数据信息 */}
                  {event.data && (
                    <div className="mt-2 text-xs text-muted-foreground">
                      {event.data.currentValue !== undefined && (
                        <span>当前值: {event.data.currentValue}</span>
                      )}
                      {event.data.threshold !== undefined && (
                        <span className="ml-4">阈值: {event.data.threshold}</span>
                      )}
                    </div>
                  )}
                </div>
              </div>

              {/* 时间和操作 */}
              <div className="flex items-center gap-2">
                <div className="text-right">
                  <div className="flex items-center gap-1 text-sm text-muted-foreground">
                    <Clock className="h-3 w-3" />
                    {formatRelativeTime(event.timestamp)}
                  </div>
                  <div className="text-xs text-muted-foreground">
                    {formatTime(event.timestamp)}
                  </div>
                </div>

                {/* 操作菜单 */}
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={(e) => e.stopPropagation()}
                    >
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    {!event.acknowledged && (
                      <DropdownMenuItem
                        onClick={(e) => {
                          e.stopPropagation();
                          handleAcknowledge(event.id);
                        }}
                      >
                        <CheckCircle className="h-4 w-4 mr-2" />
                        确认事件
                      </DropdownMenuItem>
                    )}
                    <DropdownMenuItem
                      onClick={(e) => {
                        e.stopPropagation();
                        selectEvent(event);
                      }}
                    >
                      <ChevronRight className="h-4 w-4 mr-2" />
                      查看详情
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </div>
          ))}
        </div>

        {/* 分页控制 */}
        {pagination.totalPages > 1 && (
          <div className="flex items-center justify-between mt-6">
            <div className="text-sm text-muted-foreground">
              显示第 {(pagination.page - 1) * pagination.pageSize + 1} - {Math.min(pagination.page * pagination.pageSize, pagination.total)} 项，共 {pagination.total} 项
            </div>
            
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                disabled={pagination.page === 1}
                onClick={() => setPage(pagination.page - 1)}
              >
                {t('previousPage')}
              </Button>
              
              <span className="text-sm text-muted-foreground">
                第 {pagination.page} 页，共 {pagination.totalPages} 页
              </span>
              
              <Button
                variant="outline"
                size="sm"
                disabled={pagination.page === pagination.totalPages}
                onClick={() => setPage(pagination.page + 1)}
              >
                {t('nextPage')}
              </Button>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

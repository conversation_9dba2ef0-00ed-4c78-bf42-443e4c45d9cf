// 平滑滚动动画工具函数

export interface ScrollToOptions {
  duration?: number;
  easing?: (t: number) => number;
  offset?: number;
}

// 缓动函数
export const easingFunctions = {
  // 线性
  linear: (t: number) => t,
  
  // 缓入
  easeIn: (t: number) => t * t,
  
  // 缓出
  easeOut: (t: number) => t * (2 - t),
  
  // 缓入缓出
  easeInOut: (t: number) => t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t,
  
  // 弹性缓出
  easeOutBack: (t: number) => {
    const c1 = 1.70158;
    const c3 = c1 + 1;
    return 1 + c3 * Math.pow(t - 1, 3) + c1 * Math.pow(t - 1, 2);
  }
};

/**
 * 将元素滚动到容器顶部
 * @param element 目标元素
 * @param container 滚动容器
 * @param options 滚动选项
 */
export function scrollElementToTop(
  element: HTMLElement,
  container: HTMLElement,
  options: ScrollToOptions = {}
): Promise<void> {
  const {
    duration = 500,
    easing = easingFunctions.easeOutBack,
    offset = 0
  } = options;

  return new Promise((resolve) => {
    const startScrollTop = container.scrollTop;
    const elementTop = element.offsetTop;
    const targetScrollTop = Math.max(0, elementTop - offset);
    const scrollDistance = targetScrollTop - startScrollTop;

    // 如果已经在目标位置，直接返回
    if (Math.abs(scrollDistance) < 1) {
      resolve();
      return;
    }

    const startTime = performance.now();

    function animateScroll(currentTime: number) {
      const elapsed = currentTime - startTime;
      const progress = Math.min(elapsed / duration, 1);
      const easedProgress = easing(progress);
      
      const currentScrollTop = startScrollTop + scrollDistance * easedProgress;
      container.scrollTop = currentScrollTop;

      if (progress < 1) {
        requestAnimationFrame(animateScroll);
      } else {
        resolve();
      }
    }

    requestAnimationFrame(animateScroll);
  });
}

/**
 * 将元素滚动到容器中心
 * @param element 目标元素
 * @param container 滚动容器
 * @param options 滚动选项
 */
export function scrollElementToCenter(
  element: HTMLElement,
  container: HTMLElement,
  options: ScrollToOptions = {}
): Promise<void> {
  const {
    duration = 500,
    easing = easingFunctions.easeOutBack,
    offset = 0
  } = options;

  return new Promise((resolve) => {
    const startScrollTop = container.scrollTop;
    const containerHeight = container.clientHeight;
    const elementTop = element.offsetTop;
    const elementHeight = element.offsetHeight;
    
    // 计算将元素居中的滚动位置
    const targetScrollTop = Math.max(0, 
      elementTop - (containerHeight / 2) + (elementHeight / 2) + offset
    );
    const scrollDistance = targetScrollTop - startScrollTop;

    // 如果已经在目标位置，直接返回
    if (Math.abs(scrollDistance) < 1) {
      resolve();
      return;
    }

    const startTime = performance.now();

    function animateScroll(currentTime: number) {
      const elapsed = currentTime - startTime;
      const progress = Math.min(elapsed / duration, 1);
      const easedProgress = easing(progress);
      
      const currentScrollTop = startScrollTop + scrollDistance * easedProgress;
      container.scrollTop = currentScrollTop;

      if (progress < 1) {
        requestAnimationFrame(animateScroll);
      } else {
        resolve();
      }
    }

    requestAnimationFrame(animateScroll);
  });
}

/**
 * 高亮元素动画
 * @param element 目标元素
 * @param duration 动画持续时间
 */
export function highlightElement(
  element: HTMLElement,
  duration: number = 1000
): Promise<void> {
  return new Promise((resolve) => {
    // 添加高亮类
    element.classList.add('device-highlight');

    // 动画结束后移除高亮类
    setTimeout(() => {
      element.classList.remove('device-highlight');
      resolve();
    }, duration);
  });
}

/**
 * 设备选择动画组合
 * @param element 目标设备元素
 * @param container 滚动容器
 * @param options 动画选项
 */
export async function animateDeviceSelection(
  element: HTMLElement,
  container: HTMLElement,
  options: ScrollToOptions & { highlightDuration?: number } = {}
): Promise<void> {
  const {
    duration = 500,
    easing = easingFunctions.easeOutBack,
    offset = 16,
    highlightDuration = 1200
  } = options;

  try {
    // 第一步：滚动到目标位置
    await scrollElementToTop(element, container, {
      duration,
      easing,
      offset
    });

    // 第二步：等待一小段时间让滚动完全停止
    await new Promise(resolve => setTimeout(resolve, 50));

    // 第三步：高亮显示
    await highlightElement(element, highlightDuration);
  } catch (error) {
    console.warn('设备选择动画失败:', error);
    throw error;
  }
}

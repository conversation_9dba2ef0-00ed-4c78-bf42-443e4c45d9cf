'use client';

import React, { useState } from 'react';
import { useTranslations } from 'next-intl';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import {
  BarChart3,
  FileText,
  Download,
  Settings,
  TrendingUp,
  Battery,
  Thermometer,
  Zap
} from 'lucide-react';

import { FileUpload } from '@/components/battery-analysis/FileUpload';
import { VoltageTimeChart } from '@/components/battery-analysis/charts/VoltageTimeChart';
import { CapacityAnalysisChart } from '@/components/battery-analysis/charts/CapacityAnalysisChart';
import { TemperatureChart } from '@/components/battery-analysis/charts/TemperatureChart';
import { DifferentialAnalysisChart } from '@/components/battery-analysis/charts/DifferentialAnalysisChart';
import { BatteryStatsPanel } from '@/components/battery-analysis/BatteryStatsPanel';
import { useBatteryDataExport } from '@/lib/hooks/useBatteryDataImport';
import { BatteryDataSet, StepType } from '@/lib/types';

export default function AnalyticsPage() {
  const t = useTranslations('analytics');
  const [currentDataSet, setCurrentDataSet] = useState<BatteryDataSet | null>(null);
  const [selectedCycles, setSelectedCycles] = useState<number[]>([]);
  const [showStepTypes, setShowStepTypes] = useState<StepType[]>([
    StepType.CC_CHARGE,
    StepType.CC_DISCHARGE,
    StepType.REST
  ]);

  const { isExporting, exportToCSV, exportToJSON } = useBatteryDataExport();

  const handleUploadSuccess = (dataSet: BatteryDataSet) => {
    setCurrentDataSet(dataSet);
    // 默认选择前5个循环
    const cycles = [...new Set(dataSet.data.map(d => d.cycleNumber))].sort((a, b) => a - b);
    setSelectedCycles(cycles.slice(0, 5));
  };

  const handleExportCSV = async () => {
    if (currentDataSet) {
      try {
        await exportToCSV(currentDataSet);
      } catch (error) {
        console.error('导出CSV失败:', error);
      }
    }
  };

  const handleExportJSON = async () => {
    if (currentDataSet) {
      try {
        await exportToJSON(currentDataSet);
      } catch (error) {
        console.error('导出JSON失败:', error);
      }
    }
  };

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold flex items-center gap-2">
            <BarChart3 className="h-8 w-8" />
            电池数据分析
          </h1>
          <p className="text-muted-foreground mt-2">电池测试数据的可视化分析和统计报告</p>
        </div>

        {currentDataSet && (
          <div className="flex items-center gap-2">
            <Badge variant="outline" className="text-sm">
              {currentDataSet.recordCount.toLocaleString()} 数据点
            </Badge>
            <Button
              variant="outline"
              size="sm"
              onClick={handleExportCSV}
              disabled={isExporting}
            >
              <Download className="h-4 w-4 mr-2" />
              导出CSV
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={handleExportJSON}
              disabled={isExporting}
            >
              <FileText className="h-4 w-4 mr-2" />
              导出JSON
            </Button>
          </div>
        )}
      </div>

      {/* 文件上传区域 */}
      {!currentDataSet && (
        <FileUpload
          onUploadSuccess={handleUploadSuccess}
          onUploadError={(error) => console.error('上传失败:', error)}
        />
      )}

      {/* 数据分析界面 */}
      {currentDataSet && (
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* 左侧：图表展示区域 */}
          <div className="lg:col-span-3 space-y-6">
            {/* 数据集信息 */}
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center gap-2">
                    <FileText className="h-5 w-5" />
                    {currentDataSet.name}
                  </CardTitle>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentDataSet(null)}
                  >
                    加载新文件
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                  <div>
                    <span className="text-muted-foreground">记录数量:</span>
                    <div className="font-medium">{currentDataSet.recordCount.toLocaleString()}</div>
                  </div>
                  <div>
                    <span className="text-muted-foreground">循环范围:</span>
                    <div className="font-medium">
                      {currentDataSet.metadata.cycleRange?.start} - {currentDataSet.metadata.cycleRange?.end}
                    </div>
                  </div>
                  <div>
                    <span className="text-muted-foreground">文件大小:</span>
                    <div className="font-medium">{(currentDataSet.fileSize / 1024).toFixed(1)} KB</div>
                  </div>
                  <div>
                    <span className="text-muted-foreground">上传时间:</span>
                    <div className="font-medium">{currentDataSet.uploadTime.toLocaleString()}</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* 图表标签页 */}
            <Tabs defaultValue="voltage" className="w-full">
              <TabsList className="grid w-full grid-cols-4">
                <TabsTrigger value="voltage" className="flex items-center gap-2">
                  <Zap className="h-4 w-4" />
                  电压分析
                </TabsTrigger>
                <TabsTrigger value="capacity" className="flex items-center gap-2">
                  <Battery className="h-4 w-4" />
                  容量分析
                </TabsTrigger>
                <TabsTrigger value="temperature" className="flex items-center gap-2">
                  <Thermometer className="h-4 w-4" />
                  温度监控
                </TabsTrigger>
                <TabsTrigger value="differential" className="flex items-center gap-2">
                  <TrendingUp className="h-4 w-4" />
                  微分分析
                </TabsTrigger>
              </TabsList>

              <TabsContent value="voltage" className="mt-6">
                <VoltageTimeChart
                  data={currentDataSet.data}
                  selectedCycles={selectedCycles}
                  showStepTypes={showStepTypes}
                  height={500}
                />
              </TabsContent>

              <TabsContent value="capacity" className="mt-6">
                <CapacityAnalysisChart
                  data={currentDataSet.data}
                  height={500}
                />
              </TabsContent>

              <TabsContent value="temperature" className="mt-6">
                <TemperatureChart
                  data={currentDataSet.data}
                  selectedCycles={selectedCycles}
                  height={500}
                />
              </TabsContent>

              <TabsContent value="differential" className="mt-6">
                <DifferentialAnalysisChart
                  data={currentDataSet.data}
                  selectedCycles={selectedCycles}
                  height={500}
                />
              </TabsContent>
            </Tabs>
          </div>

          {/* 右侧：统计信息面板 */}
          <div className="lg:col-span-1">
            <BatteryStatsPanel data={currentDataSet.data} />
          </div>
        </div>
      )}
    </div>
  );
}

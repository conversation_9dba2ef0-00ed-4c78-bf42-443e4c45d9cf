'use client';

import React, { useState, useEffect } from 'react';
import { useTranslations } from 'next-intl';
import {
  Play,
  Pause,
  Square,
  Clock,
  Zap,
  AlertCircle,
  CheckCircle,
  RotateCcw,
  Target,
  Link,
  SkipForward,
  SkipBack,
  Save,
  X
} from 'lucide-react';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Separator } from '@/components/ui/separator';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';

import { ProcessExecution, ProcessExecutionStatus } from '@/lib/process/types/process';
import { ProcessOperationDialog, ProcessOperation } from './ProcessOperationDialog';

interface ProcessMonitorProps {
  className?: string;
}

// 模拟数据
const mockExecutions: ProcessExecution[] = [
  {
    id: 'exec1',
    processId: 'proc1',
    deviceId: 'device1',
    channelIds: ['ch1', 'ch2', 'ch3', 'ch4'],
    status: 'running',
    currentStep: 3,
    startTime: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2小时前开始
    progress: 0.25,
    executionData: {
      processName: '锂电池充放电测试流程',
      totalSteps: 12,
      currentStepName: '恒流充电',
      estimatedTimeRemaining: 6 * 60 * 60 // 6小时
    },
    createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000),
    updatedAt: new Date()
  },
  {
    id: 'exec2',
    processId: 'proc2',
    deviceId: 'device1',
    channelIds: ['ch9', 'ch10', 'ch11', 'ch12'],
    status: 'running',
    currentStep: 7,
    startTime: new Date(Date.now() - 4 * 60 * 60 * 1000), // 4小时前开始
    progress: 0.70,
    executionData: {
      processName: '电池容量标定流程',
      totalSteps: 10,
      currentStepName: '恒压充电',
      estimatedTimeRemaining: 2 * 60 * 60 // 2小时
    },
    createdAt: new Date(Date.now() - 4 * 60 * 60 * 1000),
    updatedAt: new Date()
  },
  {
    id: 'exec3',
    processId: 'proc3',
    deviceId: 'device2',
    channelIds: ['ch17', 'ch18', 'ch19', 'ch20'],
    status: 'paused',
    currentStep: 156,
    startTime: new Date(Date.now() - 24 * 60 * 60 * 1000), // 1天前开始
    progress: 0.156,
    executionData: {
      processName: '循环寿命测试流程',
      totalSteps: 1000,
      currentStepName: '静置',
      estimatedTimeRemaining: 30 * 24 * 60 * 60 // 30天
    },
    createdAt: new Date(Date.now() - 24 * 60 * 60 * 1000),
    updatedAt: new Date()
  }
];

export function ProcessMonitor({ className }: ProcessMonitorProps) {
  const t = useTranslations('process.monitor');
  const [executions, setExecutions] = useState<ProcessExecution[]>(mockExecutions);

  // 操作对话框状态
  const [operationDialog, setOperationDialog] = useState<{
    open: boolean;
    operation: ProcessOperation | null;
    processName?: string;
  }>({
    open: false,
    operation: null,
    processName: undefined
  });

  // 操作处理函数
  const handleOperation = (operation: ProcessOperation, processName: string) => {
    setOperationDialog({
      open: true,
      operation,
      processName
    });
  };

  const handleOperationConfirm = (selectedChannels: string[], operation: ProcessOperation) => {
    console.log(`执行操作: ${operation}, 通道: ${selectedChannels.join(', ')}`);
    // 这里可以调用实际的API来执行操作
  };

  const handleOperationClose = () => {
    setOperationDialog({
      open: false,
      operation: null,
      processName: undefined
    });
  };

  // 模拟实时更新
  useEffect(() => {
    const interval = setInterval(() => {
      setExecutions(prev => prev.map(exec => {
        if (exec.status === 'running') {
          const newProgress = Math.min(exec.progress + 0.001, 1.0);
          return {
            ...exec,
            progress: newProgress,
            updatedAt: new Date()
          };
        }
        return exec;
      }));
    }, 5000); // 每5秒更新一次

    return () => clearInterval(interval);
  }, []);

  const getStatusIcon = (status: ProcessExecutionStatus) => {
    switch (status) {
      case 'running':
        return <Play className="h-4 w-4 text-green-500" />;
      case 'paused':
        return <Pause className="h-4 w-4 text-yellow-500" />;
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-blue-500" />;
      case 'failed':
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      case 'cancelled':
        return <Square className="h-4 w-4 text-gray-500" />;
      default:
        return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusBadge = (status: ProcessExecutionStatus) => {
    const variants = {
      running: 'default',
      paused: 'secondary',
      completed: 'outline',
      failed: 'destructive',
      cancelled: 'secondary',
      pending: 'outline'
    } as const;

    const labels = {
      running: '运行中',
      paused: '暂停',
      completed: '完成',
      failed: '失败',
      cancelled: '取消',
      pending: '等待中'
    };

    return (
      <Badge variant={variants[status] || 'outline'}>
        {labels[status] || status}
      </Badge>
    );
  };

  const formatDuration = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;

    if (hours > 0) {
      return `${hours}h ${minutes}m`;
    } else if (minutes > 0) {
      return `${minutes}m ${secs}s`;
    } else {
      return `${secs}s`;
    }
  };

  const formatTimeRemaining = (seconds: number): string => {
    const days = Math.floor(seconds / (24 * 3600));
    const hours = Math.floor((seconds % (24 * 3600)) / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);

    if (days > 0) {
      return `${days}天 ${hours}小时`;
    } else if (hours > 0) {
      return `${hours}小时 ${minutes}分钟`;
    } else {
      return `${minutes}分钟`;
    }
  };

  const runningExecutions = executions.filter(e => e.status === 'running');
  const pausedExecutions = executions.filter(e => e.status === 'paused');
  const completedToday = 28; // 模拟数据
  const averageEfficiency = 96.8; // 模拟数据

  return (
    <div className={`space-y-6 ${className}`}>
      {/* 统计概览 */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">运行流程</CardTitle>
            <Play className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{runningExecutions.length}</div>
            <p className="text-xs text-muted-foreground">
              正在执行中
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">暂停流程</CardTitle>
            <Pause className="h-4 w-4 text-yellow-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{pausedExecutions.length}</div>
            <p className="text-xs text-muted-foreground">
              等待恢复
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">完成流程</CardTitle>
            <CheckCircle className="h-4 w-4 text-blue-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{completedToday}</div>
            <p className="text-xs text-muted-foreground">
              今日完成
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">流程效率</CardTitle>
            <RotateCcw className="h-4 w-4 text-purple-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{averageEfficiency}%</div>
            <p className="text-xs text-muted-foreground">
              平均效率
            </p>
          </CardContent>
        </Card>
      </div>

      {/* 正在执行的工艺流程 */}
      <Card>
        <CardHeader>
          <CardTitle>正在执行的工艺流程</CardTitle>
          <CardDescription>
            当前正在运行的工艺流程及其工步进度
          </CardDescription>
        </CardHeader>
        <CardContent>
          {executions.length === 0 ? (
            <div className="text-center py-8">
              <div className="text-muted-foreground">暂无正在执行的工艺流程</div>
            </div>
          ) : (
            <div className="space-y-4">
              {executions.map((execution) => (
                <div key={execution.id} className="border rounded-lg p-4">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center space-x-3">
                      {getStatusIcon(execution.status)}
                      <div>
                        <h4 className="font-medium">{execution.executionData?.processName}</h4>
                        <p className="text-sm text-muted-foreground">
                          设备: {execution.deviceId} | 通道: {execution.channelIds.join(', ')}
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      {getStatusBadge(execution.status)}
                    </div>
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center justify-between text-sm">
                      <span>当前工步: {execution.executionData?.currentStepName} ({execution.currentStep}/{execution.executionData?.totalSteps})</span>
                      <span>{Math.round(execution.progress * 100)}% 完成</span>
                    </div>
                    <Progress value={execution.progress * 100} className="h-2" />
                    <div className="flex items-center justify-between text-xs text-muted-foreground">
                      <span>
                        开始时间: {execution.startTime?.toLocaleString()}
                      </span>
                      {execution.status === 'running' && execution.executionData?.estimatedTimeRemaining && (
                        <span>
                          预计剩余: {formatTimeRemaining(execution.executionData.estimatedTimeRemaining)}
                        </span>
                      )}
                    </div>
                  </div>

                  {/* 操作按钮组 */}
                  <div className="flex flex-wrap gap-2 mt-4">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleOperation('reset', execution.executionData?.processName || '')}
                      className="text-blue-600 hover:text-blue-700"
                    >
                      <RotateCcw className="h-3 w-3 mr-1" />
                      复位
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleOperation('pause', execution.executionData?.processName || '')}
                      className="text-yellow-600 hover:text-yellow-700"
                    >
                      <Pause className="h-3 w-3 mr-1" />
                      暂停
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleOperation('current_step_pause', execution.executionData?.processName || '')}
                      className="text-orange-600 hover:text-orange-700"
                    >
                      <Clock className="h-3 w-3 mr-1" />
                      当前工步预约暂停
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleOperation('specific_step_pause', execution.executionData?.processName || '')}
                      className="text-purple-600 hover:text-purple-700"
                    >
                      <Target className="h-3 w-3 mr-1" />
                      指定工步预约暂停
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleOperation('cancel_pause', execution.executionData?.processName || '')}
                      className="text-gray-600 hover:text-gray-700"
                    >
                      <X className="h-3 w-3 mr-1" />
                      取消预约暂停
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleOperation('continue', execution.executionData?.processName || '')}
                      className="text-green-600 hover:text-green-700"
                    >
                      <Play className="h-3 w-3 mr-1" />
                      继续
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleOperation('jump_next', execution.executionData?.processName || '')}
                      className="text-indigo-600 hover:text-indigo-700"
                    >
                      <SkipForward className="h-3 w-3 mr-1" />
                      跳转到下一个工步
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleOperation('jump_specific', execution.executionData?.processName || '')}
                      className="text-cyan-600 hover:text-cyan-700"
                    >
                      <SkipBack className="h-3 w-3 mr-1" />
                      跳转到指定工步
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleOperation('save', execution.executionData?.processName || '')}
                      className="text-teal-600 hover:text-teal-700"
                    >
                      <Save className="h-3 w-3 mr-1" />
                      寄存
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* 操作确认对话框 */}
      <ProcessOperationDialog
        open={operationDialog.open}
        onOpenChange={handleOperationClose}
        operation={operationDialog.operation}
        processName={operationDialog.processName}
        onConfirm={handleOperationConfirm}
      />
    </div>
  );
}

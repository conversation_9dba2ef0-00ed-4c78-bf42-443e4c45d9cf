/**
 * 电池分选数据服务
 */

import {
  TestSession,
  BatteryResult,
  SortingResult,
  SortingStatistics,
  BatteryTestData,
  TestResult
} from '@/lib/types/battery-sorting';

// 生成模拟测试结果
function generateTestResult(parameter: string, baseValue: number, tolerance: number): TestResult {
  const measuredValue = baseValue + (Math.random() - 0.5) * tolerance * 2;
  const deviation = ((measuredValue - baseValue) / baseValue) * 100;
  const result = Math.abs(deviation) <= tolerance ? 'pass' : 'fail';
  
  return {
    parameter,
    measuredValue,
    standardValue: baseValue,
    tolerance,
    unit: getUnit(parameter),
    result,
    deviation
  };
}

function getUnit(parameter: string): string {
  switch (parameter) {
    case 'capacity': return 'mAh';
    case 'voltage': return 'V';
    case 'internalResistance': return 'mΩ';
    case 'temperature': return '°C';
    default: return '';
  }
}

// 生成模拟电池测试数据
function generateBatteryTestData(batteryId: string, batchNumber: string): BatteryTestData {
  const testDate = new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000);
  
  return {
    batteryId,
    batchNumber,
    testDate,
    processId: `proc_${Math.floor(Math.random() * 10) + 1}`,
    channelId: `ch_${Math.floor(Math.random() * 16) + 1}`,
    deviceId: `device_${Math.floor(Math.random() * 2) + 1}`,
    
    batteryInfo: {
      model: 'LFP-3200',
      manufacturer: '朗天电池',
      nominalCapacity: 3200,
      nominalVoltage: 3.2,
      weight: 65.5 + Math.random() * 5
    },
    
    testResults: {
      capacity: generateTestResult('capacity', 3200, 5),
      voltage: generateTestResult('voltage', 3.2, 2),
      internalResistance: generateTestResult('internalResistance', 15, 10),
      temperature: generateTestResult('temperature', 25, 15)
    },
    
    stepExecutions: [
      {
        stepId: 'step1',
        stepName: '恒流充电',
        startTime: new Date(testDate.getTime() + 1000),
        endTime: new Date(testDate.getTime() + 3600000),
        status: 'completed',
        parameters: { current: 1.6, voltage: 3.65 },
        measurements: { capacity: 3180, time: 3600 }
      },
      {
        stepId: 'step2',
        stepName: '恒压充电',
        startTime: new Date(testDate.getTime() + 3600000),
        endTime: new Date(testDate.getTime() + 5400000),
        status: 'completed',
        parameters: { voltage: 3.65, current: 0.32 },
        measurements: { capacity: 3200, time: 1800 }
      },
      {
        stepId: 'step3',
        stepName: '放电测试',
        startTime: new Date(testDate.getTime() + 5400000),
        endTime: new Date(testDate.getTime() + 9000000),
        status: 'completed',
        parameters: { current: 1.6, cutoffVoltage: 2.5 },
        measurements: { capacity: 3150, time: 3600 }
      }
    ],
    
    testEnvironment: {
      temperature: 25 + Math.random() * 5,
      humidity: 45 + Math.random() * 10,
      pressure: 101.3
    }
  };
}

// 计算分选等级
function calculateGrade(scores: any): 'A+' | 'A' | 'B+' | 'B' | 'C' | 'D' | 'Reject' {
  const overall = scores.overall;
  
  if (overall >= 95) return 'A+';
  if (overall >= 90) return 'A';
  if (overall >= 85) return 'B+';
  if (overall >= 80) return 'B';
  if (overall >= 70) return 'C';
  if (overall >= 60) return 'D';
  return 'Reject';
}

// 生成模拟测试会话
function generateTestSession(index: number): TestSession {
  const processNames = [
    '锂电池标准充放电测试',
    '电池容量标定流程',
    '高温性能测试',
    '循环寿命测试',
    '安全性能测试'
  ];

  const startTime = new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000);
  const duration = 2 + Math.random() * 6; // 2-8小时
  const endTime = Math.random() > 0.2 ? new Date(startTime.getTime() + duration * 60 * 60 * 1000) : undefined;
  const status = endTime ? (Math.random() > 0.1 ? 'completed' : 'failed') : 'running';

  const totalBatteries = 8 + Math.floor(Math.random() * 8); // 8-16个电池
  const completedBatteries = status === 'running' ? Math.floor(totalBatteries * 0.7) : totalBatteries;
  const passedBatteries = Math.floor(completedBatteries * (0.7 + Math.random() * 0.25)); // 70-95%通过率
  const failedBatteries = completedBatteries - passedBatteries;

  const passRate = completedBatteries > 0 ? (passedBatteries / completedBatteries) * 100 : 0;
  const averageScore = 75 + Math.random() * 20; // 75-95分

  return {
    id: `session_${String(index + 1).padStart(4, '0')}`,
    processId: `proc_${Math.floor(Math.random() * 5) + 1}`,
    processName: processNames[Math.floor(Math.random() * processNames.length)],
    startTime,
    endTime,
    status,
    totalBatteries,
    completedBatteries,
    passedBatteries,
    failedBatteries,
    operator: `操作员${Math.floor(Math.random() * 5) + 1}`,
    description: Math.random() > 0.5 ? `批次${Math.floor(index / 3) + 1}测试` : undefined,
    statistics: {
      passRate,
      averageScore,
      gradeDistribution: {
        'A+': Math.floor(passedBatteries * 0.1),
        'A': Math.floor(passedBatteries * 0.2),
        'B+': Math.floor(passedBatteries * 0.3),
        'B': Math.floor(passedBatteries * 0.25),
        'C': Math.floor(passedBatteries * 0.1),
        'D': Math.floor(passedBatteries * 0.05)
      },
      deviceDistribution: {
        'device_1': Math.floor(totalBatteries * 0.6),
        'device_2': Math.floor(totalBatteries * 0.4)
      }
    }
  };
}

// 生成模拟电池结果
function generateBatteryResult(sessionId: string, index: number): BatteryResult {
  const batteryId = `BAT${String(index + 1).padStart(6, '0')}`;
  const batchNumber = `BATCH${Math.floor(index / 50) + 1}`;
  const testData = generateBatteryTestData(batteryId, batchNumber);

  const channelId = `CH${String((index % 16) + 1).padStart(2, '0')}`;
  const deviceId = `device_${Math.floor(index / 8) % 2 + 1}`;

  // 计算各项评分
  const scores = {
    capacity: Math.max(0, 100 - Math.abs(testData.testResults.capacity.deviation) * 2),
    voltage: Math.max(0, 100 - Math.abs(testData.testResults.voltage.deviation) * 3),
    resistance: Math.max(0, 100 - Math.abs(testData.testResults.internalResistance.deviation) * 1.5),
    temperature: Math.max(0, 100 - Math.abs(testData.testResults.temperature.deviation) * 1),
    overall: 0
  };

  scores.overall = (scores.capacity * 0.4 + scores.voltage * 0.3 + scores.resistance * 0.2 + scores.temperature * 0.1);

  const grade = calculateGrade(scores);
  const decision = grade === 'Reject' ? 'reject' : (scores.overall < 75 ? 'retest' : 'accept');

  const errors: string[] = [];
  if (testData.testResults.capacity.result === 'fail') errors.push('容量不达标');
  if (testData.testResults.voltage.result === 'fail') errors.push('电压异常');
  if (testData.testResults.internalResistance.result === 'fail') errors.push('内阻过高');
  if (testData.testResults.temperature.result === 'fail') errors.push('温度异常');

  const recommendedUse: string[] = [];
  if (grade === 'A+' || grade === 'A') recommendedUse.push('高端应用', '电动汽车');
  else if (grade === 'B+' || grade === 'B') recommendedUse.push('储能系统', '电动工具');
  else if (grade === 'C') recommendedUse.push('低功率应用');

  return {
    id: `result_${sessionId}_${batteryId}`,
    batteryId,
    sessionId,
    channelId,
    deviceId,
    testData,
    grade,
    overallScore: scores.overall,
    scores,
    decision,
    errors,
    recommendedUse,
    completedAt: new Date(testData.testDate.getTime() + 10800000), // 3小时后
    notes: Math.random() > 0.8 ? '需要进一步检查' : undefined
  };
}

// 生成模拟分选统计数据
function generateSortingStatistics(results: BatteryResult[]): SortingStatistics {
  const totalTested = results.length;
  const totalPassed = results.filter(r => r.decision === 'accept').length;
  const totalFailed = results.filter(r => r.decision === 'reject').length;
  const passRate = totalTested > 0 ? (totalPassed / totalTested) * 100 : 0;
  
  // 等级分布
  const gradeDistribution = ['A+', 'A', 'B+', 'B', 'C', 'D', 'Reject'].map(grade => {
    const count = results.filter(r => r.grade === grade).length;
    return {
      grade,
      count,
      percentage: totalTested > 0 ? (count / totalTested) * 100 : 0
    };
  }).filter(item => item.count > 0);
  
  // 失败原因分布
  const failureReasonsMap = new Map<string, number>();
  results.forEach(result => {
    result.errors.forEach(reason => {
      failureReasonsMap.set(reason, (failureReasonsMap.get(reason) || 0) + 1);
    });
  });
  
  const failureReasons = Array.from(failureReasonsMap.entries()).map(([reason, count]) => ({
    reason,
    count,
    percentage: totalTested > 0 ? (count / totalTested) * 100 : 0
  }));
  
  // 批次统计
  const batchMap = new Map<string, BatteryResult[]>();
  results.forEach(result => {
    const batch = result.testData.batchNumber;
    if (!batchMap.has(batch)) {
      batchMap.set(batch, []);
    }
    batchMap.get(batch)!.push(result);
  });
  
  const batchStatistics = Array.from(batchMap.entries()).map(([batchNumber, batchResults]) => {
    const totalCount = batchResults.length;
    const passCount = batchResults.filter(r => r.decision === 'accept').length;
    const passRate = totalCount > 0 ? (passCount / totalCount) * 100 : 0;
    const averageScore = batchResults.reduce((sum, r) => sum + r.overallScore, 0) / totalCount;
    
    return {
      batchNumber,
      totalCount,
      passCount,
      passRate,
      averageScore
    };
  });
  
  // 趋势数据（最近7天）
  const trends = [];
  for (let i = 6; i >= 0; i--) {
    const date = new Date();
    date.setDate(date.getDate() - i);
    const dateStr = date.toISOString().split('T')[0];
    
    const dayResults = results.filter(r =>
      r.completedAt.toISOString().split('T')[0] === dateStr
    );
    
    const dayTotalTested = dayResults.length;
    const dayPassRate = dayTotalTested > 0 ? 
      (dayResults.filter(r => r.decision === 'accept').length / dayTotalTested) * 100 : 0;
    const dayAverageScore = dayTotalTested > 0 ?
      dayResults.reduce((sum, r) => sum + r.overallScore, 0) / dayTotalTested : 0;
    
    trends.push({
      date: dateStr,
      totalTested: dayTotalTested,
      passRate: dayPassRate,
      averageScore: dayAverageScore
    });
  }
  
  return {
    totalTested,
    totalPassed,
    totalFailed,
    passRate,
    gradeDistribution,
    failureReasons,
    batchStatistics,
    trends
  };
}



// 获取测试会话列表
export async function getTestSessions(): Promise<TestSession[]> {
  // 模拟API延迟
  await new Promise(resolve => setTimeout(resolve, 600));

  const sessions: TestSession[] = [];
  for (let i = 0; i < 20; i++) {
    sessions.push(generateTestSession(i));
  }

  // 按开始时间倒序排列
  return sessions.sort((a, b) => b.startTime.getTime() - a.startTime.getTime());
}

// 获取指定会话的电池结果
export async function getBatteryResultsBySession(sessionId: string): Promise<BatteryResult[]> {
  // 模拟API延迟
  await new Promise(resolve => setTimeout(resolve, 400));

  const results: BatteryResult[] = [];
  const batteryCount = 8 + Math.floor(Math.random() * 8); // 8-16个电池

  for (let i = 0; i < batteryCount; i++) {
    results.push(generateBatteryResult(sessionId, i));
  }

  return results.sort((a, b) => a.channelId.localeCompare(b.channelId));
}

// 获取分选统计数据（基于所有会话的结果）
export async function getSortingStatistics(): Promise<SortingStatistics> {
  const sessions = await getTestSessions();
  const allResults: BatteryResult[] = [];

  for (const session of sessions.slice(0, 5)) { // 只取前5个会话的结果
    const sessionResults = await getBatteryResultsBySession(session.id);
    allResults.push(...sessionResults);
  }

  return generateSortingStatistics(allResults);
}

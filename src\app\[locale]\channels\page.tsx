'use client';

import { useState, useEffect, useRef, useCallback } from 'react';
import { useSearchParams } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { InteractiveCard, InteractiveListItem, ChannelCard } from '@/components/ui/interactive-card';
import { EnhancedChannelCard } from '@/components/channel/enhanced-channel-card';
import { useDeviceStore } from '@/stores/deviceStore';
import { useNavigation } from '@/hooks/useNavigation';
import { scrollElementToCenter } from '@/utils/scrollAnimation';

import { 
  Monitor, Activity, Thermometer, Gauge, Settings, List, LayoutGrid,
  RotateCcw, Pause, Play, ArrowRight, ArrowDown, Archive, 
  TrendingUp, FileText, Clock, AlertTriangle, CheckCircle
} from 'lucide-react';

export default function ChannelsPage() {
  // 导航参数管理
  const { getParam, clearParams } = useNavigation();
  const searchParams = useSearchParams();

  // 全局状态管理
  const {
    devices,
    channels,
    selectedDevice,
    setSelectedDevice,
    getChannelsByDevice,
    initializeData
  } = useDeviceStore();

  // 本地状态管理
  const [viewMode, setViewMode] = useState<'list' | 'cards'>('cards');

  // 引用管理
  const deviceListRef = useRef<HTMLDivElement>(null);
  const deviceRefs = useRef<Map<string, HTMLDivElement>>(new Map());

  // 获取当前设备的通道数据
  const currentChannels = selectedDevice ? getChannelsByDevice(selectedDevice) : [];

  // 设备选择处理函数
  const handleDeviceSelect = useCallback(async (deviceName: string, skipAnimation: boolean = false) => {
    if (deviceName === selectedDevice) return;

    setSelectedDevice(deviceName);

    // 如果跳过动画，直接返回
    if (skipAnimation) return;

    // 滚动动画
    const deviceElement = deviceRefs.current.get(deviceName);
    const listContainer = deviceListRef.current;

    if (deviceElement && listContainer) {
      try {
        // 滚动到中心位置
        await scrollElementToCenter(deviceElement, listContainer, {
          duration: 600,
          offset: 0
        });
      } catch (error) {
        console.warn('设备滚动动画失败:', error);
      }
    }
  }, [selectedDevice, setSelectedDevice]);

  // 初始化数据和处理参数
  useEffect(() => {
    // 初始化数据
    if (devices.length === 0) {
      initializeData();
    }

    // 处理URL参数
    const deviceParam = searchParams.get('device');
    const channelParam = searchParams.get('channel');

    if (deviceParam && devices.some(d => d.deviceName === deviceParam)) {
      // 延迟执行以确保DOM已渲染
      setTimeout(() => {
        handleDeviceSelect(deviceParam, false); // 执行动画

        // 如果有通道参数，滚动到对应通道
        if (channelParam) {
          const channelNumber = parseInt(channelParam);
          if (!isNaN(channelNumber)) {
            // 这里可以添加滚动到特定通道的逻辑
            console.log(`导航到设备 ${deviceParam} 的通道 ${channelNumber}`);
          }
        }
      }, 100);
    } else {
      // 处理隐式传递的设备选择参数
      const implicitDeviceParam = getParam('selectedDevice');
      if (implicitDeviceParam && devices.some(d => d.deviceName === implicitDeviceParam)) {
        // 延迟执行以确保DOM已渲染
        setTimeout(() => {
          handleDeviceSelect(implicitDeviceParam, false); // 执行动画
          // 清除参数，避免重复触发
          clearParams();
        }, 100);
      } else if (!selectedDevice && devices.length > 0) {
        // 如果没有参数，选择第一个设备（不执行动画）
        setSelectedDevice(devices[0].deviceName);
      }
    }
  }, [devices, selectedDevice, setSelectedDevice, initializeData, handleDeviceSelect, getParam, clearParams, searchParams]);

  // 手动设备切换处理函数（用于用户点击）
  const handleManualDeviceSelect = useCallback((deviceName: string) => {
    handleDeviceSelect(deviceName, false); // 执行完整动画
  }, [handleDeviceSelect]);

  // 获取状态颜色
  const getStatusColor = (status: string) => {
    const colors = {
      '充电': 'bg-green-500',
      '放电': 'bg-blue-500', 
      '搁置': 'bg-yellow-500',
      '暂停': 'bg-red-500',
      '完成': 'bg-gray-500',
      '复位': 'bg-purple-500',
      '运行中': 'bg-green-500',
      '待机': 'bg-yellow-500',
      '故障': 'bg-red-500'
    };
    return colors[status as keyof typeof colors] || 'bg-gray-500';
  };

  // 格式化IP地址
  const formatIpAddress = (ip: number[]) => ip.join('.');

  // 格式化时间
  const formatTime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  // 通道操作函数
  const channelOperations = {
    channel_reset: (channelNumber: number) => console.log(`通道复位: ${channelNumber}`),
    channel_pause: (channelNumber: number) => console.log(`通道暂停: ${channelNumber}`),
    schedule_pause_current: (channelNumber: number) => console.log(`当前工步预约暂停: ${channelNumber}`),
    schedule_pause_at: (channelNumber: number) => console.log(`指定工步预约暂停: ${channelNumber}`),
    cancel_scheduled_pause: (channelNumber: number) => console.log(`取消预约暂停: ${channelNumber}`),
    channel_resume: (channelNumber: number) => console.log(`通道接续: ${channelNumber}`),
    jump_to_next_step: (channelNumber: number) => console.log(`跳转到下一个工步: ${channelNumber}`),
    jump_to_step: (channelNumber: number) => console.log(`跳转到指定工步: ${channelNumber}`),
    channel_register: (channelNumber: number) => console.log(`通道寄存: ${channelNumber}`),
    show_curve: (channelNumber: number) => console.log(`查看曲线: ${channelNumber}`),
    show_recipe: (channelNumber: number) => console.log(`查看工艺: ${channelNumber}`),
    toggle_channel: (channelNumber: number, enabled: boolean) => {
      console.log(`通道${enabled ? '启用' : '禁用'}: ${channelNumber}`);

      // 使用store的updateChannelStatus方法，它会自动记录事件
      const { updateChannelStatus } = useDeviceStore.getState();
      if (selectedDevice) {
        updateChannelStatus(selectedDevice, channelNumber, { isEnabled: enabled });
      }
    }
  };

  // 通道控制条组件
  const ChannelControlBar = ({ channel, viewMode }: { channel: any, viewMode: 'list' | 'cards' }) => {
    const [isOpen, setIsOpen] = useState(false);

    return (
      <Popover open={isOpen} onOpenChange={setIsOpen}>
        <PopoverTrigger asChild>
          <div
            className="absolute inset-0 cursor-pointer"
            onClick={() => setIsOpen(!isOpen)}
          />
        </PopoverTrigger>
        <PopoverContent
          className="w-auto max-w-[90vw] p-0 border-0 bg-transparent shadow-none"
          side={viewMode === 'cards' ? 'bottom' : 'right'}
          align={viewMode === 'cards' ? 'center' : 'center'}
          sideOffset={viewMode === 'cards' ? -80 : -50}
          alignOffset={viewMode === 'cards' ? 0 : 0}
          onInteractOutside={() => setIsOpen(false)}
        >
          <div className="relative">
            {/* 毛玻璃胶囊容器 */}
            <div className="bg-black/20 backdrop-blur-md rounded-full px-4 py-3 shadow-2xl border border-white/20 animate-in slide-in-from-bottom-2 duration-300">
              <div className="flex items-center gap-1">
                {/* 复位 */}
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <button
                        className="w-10 h-10 rounded-full bg-white/10 hover:bg-white/20 transition-all duration-200 flex items-center justify-center group"
                        onClick={() => channelOperations.channel_reset(channel.channelNumber)}
                      >
                        <RotateCcw className="h-4 w-4 text-white group-hover:scale-110 transition-transform" />
                      </button>
                    </TooltipTrigger>
                    <TooltipContent side="top" className="bg-black/80 text-white border-white/20">
                      复位
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>

                {/* 暂停 */}
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <button
                        className="w-10 h-10 rounded-full bg-white/10 hover:bg-white/20 transition-all duration-200 flex items-center justify-center group"
                        onClick={() => channelOperations.channel_pause(channel.channelNumber)}
                      >
                        <Pause className="h-4 w-4 text-white group-hover:scale-110 transition-transform" />
                      </button>
                    </TooltipTrigger>
                    <TooltipContent side="top" className="bg-black/80 text-white border-white/20">
                      暂停
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>

                {/* 接续 */}
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <button
                        className="w-10 h-10 rounded-full bg-white/10 hover:bg-white/20 transition-all duration-200 flex items-center justify-center group"
                        onClick={() => channelOperations.channel_resume(channel.channelNumber)}
                      >
                        <Play className="h-4 w-4 text-white group-hover:scale-110 transition-transform" />
                      </button>
                    </TooltipTrigger>
                    <TooltipContent side="top" className="bg-black/80 text-white border-white/20">
                      接续
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>

                {/* 分隔线 */}
                <div className="w-px h-6 bg-white/20 mx-1"></div>

                {/* 当前工步预约暂停 */}
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <button
                        className="w-10 h-10 rounded-full bg-white/10 hover:bg-white/20 transition-all duration-200 flex items-center justify-center group"
                        onClick={() => channelOperations.schedule_pause_current(channel.channelNumber)}
                      >
                        <Clock className="h-4 w-4 text-white group-hover:scale-110 transition-transform" />
                      </button>
                    </TooltipTrigger>
                    <TooltipContent side="top" className="bg-black/80 text-white border-white/20">
                      当前工步预约暂停
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>

                {/* 指定工步预约暂停 */}
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <button
                        className="w-10 h-10 rounded-full bg-white/10 hover:bg-white/20 transition-all duration-200 flex items-center justify-center group"
                        onClick={() => channelOperations.schedule_pause_at(channel.channelNumber)}
                      >
                        <AlertTriangle className="h-4 w-4 text-white group-hover:scale-110 transition-transform" />
                      </button>
                    </TooltipTrigger>
                    <TooltipContent side="top" className="bg-black/80 text-white border-white/20">
                      指定工步预约暂停
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>

                {/* 取消预约暂停 */}
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <button
                        className="w-10 h-10 rounded-full bg-white/10 hover:bg-white/20 transition-all duration-200 flex items-center justify-center group"
                        onClick={() => channelOperations.cancel_scheduled_pause(channel.channelNumber)}
                      >
                        <CheckCircle className="h-4 w-4 text-white group-hover:scale-110 transition-transform" />
                      </button>
                    </TooltipTrigger>
                    <TooltipContent side="top" className="bg-black/80 text-white border-white/20">
                      取消预约暂停
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>

                {/* 跳转到下一个工步 */}
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <button
                        className="w-10 h-10 rounded-full bg-white/10 hover:bg-white/20 transition-all duration-200 flex items-center justify-center group"
                        onClick={() => channelOperations.jump_to_next_step(channel.channelNumber)}
                      >
                        <ArrowRight className="h-4 w-4 text-white group-hover:scale-110 transition-transform" />
                      </button>
                    </TooltipTrigger>
                    <TooltipContent side="top" className="bg-black/80 text-white border-white/20">
                      跳转到下一个工步
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>

                {/* 跳转到指定工步 */}
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <button
                        className="w-10 h-10 rounded-full bg-white/10 hover:bg-white/20 transition-all duration-200 flex items-center justify-center group"
                        onClick={() => channelOperations.jump_to_step(channel.channelNumber)}
                      >
                        <ArrowDown className="h-4 w-4 text-white group-hover:scale-110 transition-transform" />
                      </button>
                    </TooltipTrigger>
                    <TooltipContent side="top" className="bg-black/80 text-white border-white/20">
                      跳转到指定工步
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>

                {/* 寄存 */}
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <button
                        className="w-10 h-10 rounded-full bg-white/10 hover:bg-white/20 transition-all duration-200 flex items-center justify-center group"
                        onClick={() => channelOperations.channel_register(channel.channelNumber)}
                      >
                        <Archive className="h-4 w-4 text-white group-hover:scale-110 transition-transform" />
                      </button>
                    </TooltipTrigger>
                    <TooltipContent side="top" className="bg-black/80 text-white border-white/20">
                      寄存
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>

                {/* 分隔线 */}
                <div className="w-px h-6 bg-white/20 mx-1"></div>

                {/* 启用/禁用切换 */}
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <button
                        className={`w-10 h-10 rounded-full transition-all duration-200 flex items-center justify-center group border ${
                          channel.isEnabled
                            ? 'bg-green-500/20 hover:bg-green-500/40 border-green-400/30'
                            : 'bg-red-500/20 hover:bg-red-500/40 border-red-400/30'
                        }`}
                        onClick={() => channelOperations.toggle_channel(channel.channelNumber, !channel.isEnabled)}
                      >
                        {channel.isEnabled ? (
                          <CheckCircle className="h-4 w-4 text-green-300 group-hover:scale-110 transition-transform" />
                        ) : (
                          <AlertTriangle className="h-4 w-4 text-red-300 group-hover:scale-110 transition-transform" />
                        )}
                      </button>
                    </TooltipTrigger>
                    <TooltipContent side="top" className="bg-black/80 text-white border-white/20">
                      {channel.isEnabled ? '禁用通道' : '启用通道'}
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>

                {/* 分隔线 */}
                <div className="w-px h-6 bg-white/20 mx-1"></div>

                {/* 查看曲线 */}
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <button
                        className="w-10 h-10 rounded-full bg-green-500/20 hover:bg-green-500/40 transition-all duration-200 flex items-center justify-center group border border-green-400/30"
                        onClick={() => channelOperations.show_curve(channel.channelNumber)}
                      >
                        <TrendingUp className="h-4 w-4 text-green-300 group-hover:scale-110 transition-transform" />
                      </button>
                    </TooltipTrigger>
                    <TooltipContent side="top" className="bg-black/80 text-white border-white/20">
                      查看曲线
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>

                {/* 查看工艺 */}
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <button
                        className="w-10 h-10 rounded-full bg-blue-500/20 hover:bg-blue-500/40 transition-all duration-200 flex items-center justify-center group border border-blue-400/30"
                        onClick={() => channelOperations.show_recipe(channel.channelNumber)}
                      >
                        <FileText className="h-4 w-4 text-blue-300 group-hover:scale-110 transition-transform" />
                      </button>
                    </TooltipTrigger>
                    <TooltipContent side="top" className="bg-black/80 text-white border-white/20">
                      查看工艺
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
            </div>
          </div>
        </PopoverContent>
      </Popover>
    );
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">通道监控</h1>
          <p className="text-muted-foreground">实时监控设备通道状态和数据</p>
        </div>

        {/* 视图切换按钮 */}
        <div className="flex border rounded-lg">
          <Button
            variant={viewMode === 'list' ? 'default' : 'ghost'}
            size="sm"
            onClick={() => setViewMode('list')}
            className="rounded-r-none"
          >
            <List className="h-4 w-4" />
          </Button>
          <Button
            variant={viewMode === 'cards' ? 'default' : 'ghost'}
            size="sm"
            onClick={() => setViewMode('cards')}
            className="rounded-l-none"
          >
            <LayoutGrid className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="flex gap-6 h-[calc(100vh-12rem)]">
        {/* 左侧设备列表 */}
        <div className="w-80 border rounded-lg bg-muted/30 flex flex-col">
          <div className="p-4 border-b flex-shrink-0">
            <h2 className="font-semibold">设备列表</h2>
            <p className="text-sm text-muted-foreground">选择要监控的设备</p>
          </div>
          <div className="p-4 flex-1 overflow-hidden">
            <div
              ref={deviceListRef}
              className="h-full overflow-y-auto scrollbar-hide space-y-2 device-list-container"
            >
              {devices.map((device) => (
                <InteractiveCard
                  key={device.deviceName}
                  ref={(el) => {
                    if (el) {
                      deviceRefs.current.set(device.deviceName, el);
                    } else {
                      deviceRefs.current.delete(device.deviceName);
                    }
                  }}
                  isActive={selectedDevice === device.deviceName}
                  hoverEffect="border"
                  activeEffect="border"
                  onClick={() => handleManualDeviceSelect(device.deviceName)}
                >
                  <CardContent className="p-3">
                    <div className="flex items-center justify-between mb-2">
                      <span className="font-medium">{device.deviceName}</span>
                      <Badge className={getStatusColor(device.status)}>{device.status}</Badge>
                    </div>
                    <div className="text-sm text-muted-foreground space-y-1">
                      <div>IP: {formatIpAddress(device.ipAddress)}</div>
                      <div>活跃通道: {getChannelsByDevice(device.deviceName).filter(c => c.isEnabled).length}/64</div>
                    </div>
                  </CardContent>
                </InteractiveCard>
              ))}
            </div>
          </div>
        </div>

        {/* 右侧通道视图 */}
        <div className="flex-1 border rounded-lg bg-card flex flex-col">
          {selectedDevice ? (
            <>
              <div className="flex items-center justify-between p-4 border-b flex-shrink-0">
                <h2 className="text-lg font-semibold">{selectedDevice} - 通道监控</h2>
                <div className="text-sm text-muted-foreground">
                  共 {currentChannels.length} 个通道，{currentChannels.filter(c => c.isEnabled).length} 个启用
                </div>
              </div>

              <div className="p-4 flex-1 overflow-hidden">
                <div className="h-full overflow-y-auto scrollbar-hide">

                  {/* 通道卡片视图 */}
                  {viewMode === 'cards' && (
                    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5">
                      {currentChannels.map((channel) => (
                        <div key={channel.channelNumber} className="relative">
                          <EnhancedChannelCard
                            channel={channel}
                            onChannelClick={(channel) => console.log('Channel clicked:', channel.channelNumber)}
                          />

                          {/* 通道控制条 */}
                          <ChannelControlBar channel={channel} viewMode="cards" />
                        </div>
                      ))}
                    </div>
                  )}

                  {/* 通道列表视图 */}
                  {viewMode === 'list' && (
                    <div className="space-y-4">
                      {currentChannels.map((channel) => (
                        <div key={channel.channelNumber} className="relative">
                          <InteractiveListItem isDisabled={!channel.isEnabled}>
                            <div className="grid grid-cols-8 gap-4 items-center">
                              <div className="flex items-center space-x-3">
                                <div className={`w-3 h-3 rounded-full ${getStatusColor(channel.stepStatus)}`}></div>
                                <div>
                                  <p className="font-medium">通道 {channel.channelNumber}</p>
                                  <p className="text-sm text-muted-foreground">{channel.stepMode}</p>
                                </div>
                              </div>
                              <div>
                                <Badge className={getStatusColor(channel.stepStatus)}>{channel.stepStatus}</Badge>
                              </div>
                              <div>
                                <p className="text-sm">电流</p>
                                <p className="font-medium">{channel.current}A</p>
                              </div>
                              <div>
                                <p className="text-sm">电压</p>
                                <p className="font-medium">{channel.voltage}V</p>
                              </div>
                              <div>
                                <p className="text-sm">容量</p>
                                <p className="font-medium">{channel.capacity}mAh</p>
                              </div>
                              <div>
                                <p className="text-sm">温度</p>
                                <p className="font-medium">{channel.temperature}°C</p>
                              </div>
                              <div>
                                <p className="text-sm">工步</p>
                                <p className="font-medium">{channel.stepNumber}/{channel.totalStepNumber}</p>
                              </div>
                              <div>
                                <p className="text-sm">进程时间</p>
                                <p className="font-medium">{formatTime(channel.processTime)}</p>
                              </div>
                            </div>
                          </InteractiveListItem>

                          {/* 通道控制条 */}
                          <ChannelControlBar channel={channel} viewMode="list" />
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            </>
          ) : (
            <div className="flex items-center justify-center h-full text-muted-foreground">
              请选择一个设备查看通道信息
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

'use client';

import { useTranslations } from 'next-intl';
import { useEventStore } from '@/stores/eventStore';
import {
  Sheet,
  SheetContent,
  SheetDescription,
  Sheet<PERSON>eader,
  Sheet<PERSON><PERSON>le,
  SheetTrigger,
} from '@/components/ui/sheet';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import {
  Clock,
  AlertTriangle,
  Info,
  XCircle,
  CheckCircle,
  User,
  Settings,
  Monitor,
  Database,
  X,
  ExternalLink,
} from 'lucide-react';
import { Event, EventLevel, EventCategory } from '@/types/events';
import { cn } from '@/lib/utils';
import Link from 'next/link';
import { useParams } from 'next/navigation';

interface EventDetailsProps {
  className?: string;
}

export function EventDetails({ className }: EventDetailsProps) {
  const t = useTranslations('events');
  const params = useParams();
  const locale = params.locale as string;

  const {
    selectedEvent,
    selectEvent,
    acknowledgeEvent,
  } = useEventStore();

  if (!selectedEvent) {
    return null;
  }

  // 获取级别颜色
  const getLevelColor = (level: EventLevel) => {
    switch (level) {
      case EventLevel.CRITICAL:
        return 'bg-red-500 text-white';
      case EventLevel.ERROR:
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      case EventLevel.WARNING:
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      case EventLevel.INFO:
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  // 获取级别图标
  const getLevelIcon = (level: EventLevel) => {
    switch (level) {
      case EventLevel.CRITICAL:
        return <XCircle className="h-5 w-5" />;
      case EventLevel.ERROR:
        return <AlertTriangle className="h-5 w-5" />;
      case EventLevel.WARNING:
        return <AlertTriangle className="h-5 w-5" />;
      case EventLevel.INFO:
        return <Info className="h-5 w-5" />;
      default:
        return <Info className="h-5 w-5" />;
    }
  };

  // 获取类别图标
  const getCategoryIcon = (category: EventCategory) => {
    switch (category) {
      case EventCategory.DEVICE:
        return <Monitor className="h-5 w-5" />;
      case EventCategory.CHANNEL:
        return <Settings className="h-5 w-5" />;
      case EventCategory.USER:
        return <User className="h-5 w-5" />;
      case EventCategory.SYSTEM:
        return <Database className="h-5 w-5" />;
      default:
        return <Info className="h-5 w-5" />;
    }
  };

  // 格式化时间
  const formatTime = (timestamp: number) => {
    return new Date(timestamp).toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      weekday: 'long',
    });
  };

  // 处理确认事件
  const handleAcknowledge = () => {
    acknowledgeEvent(selectedEvent.id, '当前用户'); // 实际应用中应该从用户上下文获取
  };

  return (
    <Sheet open={!!selectedEvent} onOpenChange={(open) => !open && selectEvent(null)}>
      <SheetContent className="w-[400px] sm:w-[540px] p-6">
        <SheetHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              {getLevelIcon(selectedEvent.level)}
              <SheetTitle>事件详情</SheetTitle>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => selectEvent(null)}
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
          <SheetDescription>
            查看事件的详细信息和相关数据
          </SheetDescription>
        </SheetHeader>

        <div className="mt-6 space-y-6">
          {/* 事件基本信息 */}
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <Badge className={getLevelColor(selectedEvent.level)}>
                {t(`levels.${selectedEvent.level}`)}
              </Badge>
              <Badge variant="outline">
                {t(`categories.${selectedEvent.category}`)}
              </Badge>
              {selectedEvent.acknowledged && (
                <Badge variant="secondary">
                  <CheckCircle className="h-3 w-3 mr-1" />
                  已确认
                </Badge>
              )}
            </div>

            <div>
              <h3 className="font-semibold text-lg">{selectedEvent.title}</h3>
              <p className="text-muted-foreground mt-1">
                {selectedEvent.description}
              </p>
            </div>
          </div>

          <Separator />

          {/* 时间信息 */}
          <div className="space-y-3">
            <h4 className="font-medium flex items-center gap-2">
              <Clock className="h-4 w-4" />
              时间信息
            </h4>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-muted-foreground">发生时间:</span>
                <span>{formatTime(selectedEvent.timestamp)}</span>
              </div>
              {selectedEvent.acknowledged && selectedEvent.acknowledgedAt && (
                <div className="flex justify-between">
                  <span className="text-muted-foreground">确认时间:</span>
                  <span>{formatTime(selectedEvent.acknowledgedAt)}</span>
                </div>
              )}
            </div>
          </div>

          <Separator />

          {/* 事件源信息 */}
          <div className="space-y-3">
            <h4 className="font-medium flex items-center gap-2">
              {getCategoryIcon(selectedEvent.category)}
              事件源
            </h4>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-muted-foreground">类型:</span>
                <span>{t(`categories.${selectedEvent.category}`)}</span>
              </div>
              {selectedEvent.source.deviceName && (
                <div className="flex justify-between items-center">
                  <span className="text-muted-foreground">设备:</span>
                  <div className="flex items-center gap-2">
                    <span>{selectedEvent.source.deviceName}</span>
                    <Link
                      href={`/${locale}/monitoring?device=${encodeURIComponent(selectedEvent.source.deviceName)}`}
                      className="text-primary hover:text-primary/80 transition-colors"
                      title="查看设备监控"
                    >
                      <ExternalLink className="h-3 w-3" />
                    </Link>
                  </div>
                </div>
              )}
              {selectedEvent.source.channelNumber && (
                <div className="flex justify-between items-center">
                  <span className="text-muted-foreground">通道:</span>
                  <div className="flex items-center gap-2">
                    <span>{selectedEvent.source.channelNumber}</span>
                    <Link
                      href={`/${locale}/channels${selectedEvent.source.deviceName ? `?device=${encodeURIComponent(selectedEvent.source.deviceName)}&channel=${selectedEvent.source.channelNumber}` : `?channel=${selectedEvent.source.channelNumber}`}`}
                      className="text-primary hover:text-primary/80 transition-colors"
                      title="查看通道详情"
                    >
                      <ExternalLink className="h-3 w-3" />
                    </Link>
                  </div>
                </div>
              )}
              {selectedEvent.source.userName && (
                <div className="flex justify-between">
                  <span className="text-muted-foreground">用户:</span>
                  <span>{selectedEvent.source.userName}</span>
                </div>
              )}
              {selectedEvent.source.systemComponent && (
                <div className="flex justify-between">
                  <span className="text-muted-foreground">系统组件:</span>
                  <span>{selectedEvent.source.systemComponent}</span>
                </div>
              )}
            </div>
          </div>

          {/* 事件数据 */}
          {selectedEvent.data && Object.keys(selectedEvent.data).length > 0 && (
            <>
              <Separator />
              <div className="space-y-3">
                <h4 className="font-medium flex items-center gap-2">
                  <Database className="h-4 w-4" />
                  事件数据
                </h4>
                <div className="space-y-2 text-sm">
                  {selectedEvent.data.currentValue !== undefined && (
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">当前值:</span>
                      <span className="font-mono">{selectedEvent.data.currentValue}</span>
                    </div>
                  )}
                  {selectedEvent.data.previousValue !== undefined && (
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">之前值:</span>
                      <span className="font-mono">{selectedEvent.data.previousValue}</span>
                    </div>
                  )}
                  {selectedEvent.data.threshold !== undefined && (
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">阈值:</span>
                      <span className="font-mono">{selectedEvent.data.threshold}</span>
                    </div>
                  )}
                  {selectedEvent.data.fromStatus && (
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">从状态:</span>
                      <span>{selectedEvent.data.fromStatus}</span>
                    </div>
                  )}
                  {selectedEvent.data.toStatus && (
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">到状态:</span>
                      <span>{selectedEvent.data.toStatus}</span>
                    </div>
                  )}
                  {selectedEvent.data.errorCode && (
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">错误代码:</span>
                      <span className="font-mono">{selectedEvent.data.errorCode}</span>
                    </div>
                  )}
                  {selectedEvent.data.errorMessage && (
                    <div className="space-y-1">
                      <span className="text-muted-foreground">错误信息:</span>
                      <div className="bg-muted p-2 rounded text-xs font-mono">
                        {selectedEvent.data.errorMessage}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </>
          )}

          {/* 确认信息 */}
          {selectedEvent.acknowledged && (
            <>
              <Separator />
              <div className="space-y-3">
                <h4 className="font-medium flex items-center gap-2">
                  <CheckCircle className="h-4 w-4" />
                  确认信息
                </h4>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">确认人:</span>
                    <span>{selectedEvent.acknowledgedBy}</span>
                  </div>
                  {selectedEvent.acknowledgedAt && (
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">确认时间:</span>
                      <span>{formatTime(selectedEvent.acknowledgedAt)}</span>
                    </div>
                  )}
                </div>
              </div>
            </>
          )}

          {/* 操作按钮 */}
          <div className="flex gap-2 pt-4">
            {!selectedEvent.acknowledged && (
              <Button onClick={handleAcknowledge} className="flex-1">
                <CheckCircle className="h-4 w-4 mr-2" />
                确认事件
              </Button>
            )}
            <Button variant="outline" onClick={() => selectEvent(null)}>
              关闭
            </Button>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}

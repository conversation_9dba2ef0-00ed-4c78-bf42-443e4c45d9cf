#!/bin/bash

# 朗天PDCS服务器部署脚本
# 服务器: 192.168.31.37
# 用户: Aa880088

echo "🚀 开始部署朗天PDCS到服务器..."

# 检查环境
echo "📋 检查服务器环境..."

# 检查Node.js
if ! command -v node &> /dev/null; then
    echo "❌ Node.js未安装，开始安装..."
    
    # 安装Node.js 18
    curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
    sudo apt-get install -y nodejs
    
    # 验证安装
    node --version
    npm --version
else
    echo "✅ Node.js已安装: $(node --version)"
fi

# 配置npm镜像源
echo "🔧 配置npm镜像源..."
npm config set registry https://registry.npmmirror.com

# 安装pnpm
if ! command -v pnpm &> /dev/null; then
    echo "📦 安装pnpm..."
    npm install -g pnpm
    pnpm config set registry https://registry.npmmirror.com
else
    echo "✅ pnpm已安装: $(pnpm --version)"
fi

# 创建项目目录
echo "📁 创建项目目录..."
PROJECT_DIR="/home/<USER>/tianlang-pdcs"
mkdir -p $PROJECT_DIR
cd $PROJECT_DIR

echo "📍 当前目录: $(pwd)"

# 检查是否有项目文件
if [ -f "package.json" ]; then
    echo "✅ 发现项目文件，开始安装依赖..."
    
    # 安装依赖
    echo "📦 安装项目依赖..."
    pnpm install
    
    # 构建项目
    echo "🔨 构建生产版本..."
    pnpm build
    
    # 安装PM2
    if ! command -v pm2 &> /dev/null; then
        echo "🔧 安装PM2进程管理器..."
        npm install -g pm2
    fi
    
    # 停止现有进程
    echo "🛑 停止现有进程..."
    pm2 stop tianlang-pdcs 2>/dev/null || true
    pm2 delete tianlang-pdcs 2>/dev/null || true
    
    # 启动应用
    echo "🚀 启动应用..."
    pm2 start npm --name "tianlang-pdcs" -- start
    
    # 保存PM2配置
    pm2 save
    
    # 设置开机自启
    pm2 startup
    
    echo "✅ 部署完成！"
    echo "🌐 访问地址: http://192.168.31.37:3000"
    echo "📊 查看状态: pm2 status"
    echo "📝 查看日志: pm2 logs tianlang-pdcs"
    
else
    echo "❌ 未找到项目文件，请先上传项目代码"
    echo "💡 请将项目文件上传到: $PROJECT_DIR"
fi

# 显示系统信息
echo ""
echo "📊 系统信息:"
echo "CPU: $(nproc) 核心"
echo "内存: $(free -h | grep '^Mem:' | awk '{print $2}')"
echo "磁盘: $(df -h / | tail -1 | awk '{print $4}') 可用"
echo "端口3000状态: $(netstat -tulpn | grep :3000 || echo '未占用')"

'use client';

import { useEffect } from 'react';
import { usePathname } from 'next/navigation';
import { useNavigationStore, NavigationDirection } from '@/stores/navigationStore';

interface PageTransitionProps {
  children: React.ReactNode;
}

export function PageTransition({ children }: PageTransitionProps) {
  const pathname = usePathname();
  const { direction, updateNavigation, setCurrentPath } = useNavigationStore();

  useEffect(() => {
    // 页面首次加载时设置当前路径
    setCurrentPath(pathname);
  }, []);

  useEffect(() => {
    // 路径变化时更新导航状态
    updateNavigation(pathname);
  }, [pathname, updateNavigation]);

  // 根据导航方向确定动画类名
  const getAnimationClass = () => {
    switch (direction) {
      case NavigationDirection.FORWARD:
        return 'page-transition-forward';
      case NavigationDirection.BACKWARD:
        return 'page-transition-backward';
      default:
        return 'page-transition-none';
    }
  };

  return (
    <div className={`page-transition-container ${getAnimationClass()}`}>
      {children}
    </div>
  );
}

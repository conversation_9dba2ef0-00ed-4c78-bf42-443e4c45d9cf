'use client';

import React, { useState, useEffect } from 'react';
import { AlertTriangle, Info, CheckCircle, XCircle } from 'lucide-react';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';

export type AlertType = 'info' | 'warning' | 'error' | 'success';

interface AlertDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  title?: string;
  message: string;
  type?: AlertType;
  confirmText?: string;
  cancelText?: string;
  onConfirm?: () => void;
  onCancel?: () => void;
  showCancel?: boolean;
}

const alertIcons = {
  info: Info,
  warning: AlertTriangle,
  error: XCircle,
  success: CheckCircle
};

const alertColors = {
  info: 'text-blue-600',
  warning: 'text-yellow-600',
  error: 'text-red-600',
  success: 'text-green-600'
};

export function AlertDialog({
  open,
  onOpenChange,
  title,
  message,
  type = 'info',
  confirmText = '确定',
  cancelText = '取消',
  onConfirm,
  onCancel,
  showCancel = false
}: AlertDialogProps) {
  const Icon = alertIcons[type];
  const iconColor = alertColors[type];

  const handleConfirm = () => {
    onConfirm?.();
    onOpenChange(false);
  };

  const handleCancel = () => {
    onCancel?.();
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <div className="flex items-center space-x-3">
            <Icon className={cn('h-6 w-6', iconColor)} />
            <DialogTitle>{title || '提示'}</DialogTitle>
          </div>
          <DialogDescription className="text-base pt-2">
            {message}
          </DialogDescription>
        </DialogHeader>
        
        <div className="flex justify-end space-x-2 pt-4">
          {showCancel && (
            <Button variant="outline" onClick={handleCancel}>
              {cancelText}
            </Button>
          )}
          <Button onClick={handleConfirm}>
            {confirmText}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}

// 全局Alert管理器
interface AlertState {
  open: boolean;
  title?: string;
  message: string;
  type: AlertType;
  confirmText?: string;
  cancelText?: string;
  onConfirm?: () => void;
  onCancel?: () => void;
  showCancel?: boolean;
}

class AlertManager {
  private setState: ((state: AlertState) => void) | null = null;

  setStateHandler(handler: (state: AlertState) => void) {
    this.setState = handler;
  }

  show(options: Omit<AlertState, 'open'>) {
    if (this.setState) {
      this.setState({
        ...options,
        open: true
      });
    }
  }

  alert(message: string, title?: string) {
    this.show({
      message,
      title,
      type: 'info',
      showCancel: false
    });
  }

  confirm(message: string, onConfirm?: () => void, title?: string) {
    this.show({
      message,
      title,
      type: 'warning',
      showCancel: true,
      onConfirm
    });
  }

  error(message: string, title?: string) {
    this.show({
      message,
      title: title || '错误',
      type: 'error',
      showCancel: false
    });
  }

  success(message: string, title?: string) {
    this.show({
      message,
      title: title || '成功',
      type: 'success',
      showCancel: false
    });
  }

  warning(message: string, title?: string) {
    this.show({
      message,
      title: title || '警告',
      type: 'warning',
      showCancel: false
    });
  }
}

export const alertManager = new AlertManager();

// 全局Alert提供者组件
export function AlertProvider({ children }: { children: React.ReactNode }) {
  const [alertState, setAlertState] = useState<AlertState>({
    open: false,
    message: '',
    type: 'info',
    showCancel: false
  });

  useEffect(() => {
    alertManager.setStateHandler(setAlertState);
  }, []);

  return (
    <>
      {children}
      <AlertDialog
        {...alertState}
        onOpenChange={(open) => setAlertState(prev => ({ ...prev, open }))}
      />
    </>
  );
}

// 替换原生alert的工具函数
export const customAlert = {
  alert: (message: string, title?: string) => alertManager.alert(message, title),
  confirm: (message: string, onConfirm?: () => void, title?: string) => alertManager.confirm(message, onConfirm, title),
  error: (message: string, title?: string) => alertManager.error(message, title),
  success: (message: string, title?: string) => alertManager.success(message, title),
  warning: (message: string, title?: string) => alertManager.warning(message, title)
};

// 覆盖全局alert函数
if (typeof window !== 'undefined') {
  (window as any).alert = customAlert.alert;
  (window as any).confirm = (message: string) => {
    return new Promise<boolean>((resolve) => {
      customAlert.confirm(message, () => resolve(true));
    });
  };
}

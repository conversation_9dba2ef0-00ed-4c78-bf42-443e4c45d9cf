# 朗天PDCS系统部署指南

## 📋 部署前准备

### 系统要求
- **操作系统**: Linux (Ubuntu 20.04+推荐) / Windows Server / macOS
- **内存**: 最低2GB，推荐4GB+
- **存储**: 最低10GB可用空间
- **网络**: 稳定的网络连接

### 必需软件
1. **Node.js 18+**
2. **Docker & Docker Compose** (推荐)
3. **Git**

## 🚀 部署方式

### 方式一：Docker部署（推荐）

#### 1. 安装Docker
```bash
# Ubuntu/Debian
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER

# 安装Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
```

#### 2. 克隆项目
```bash
git clone <your-repo-url>
cd tianlang-pdcs
```

#### 3. 配置环境变量
```bash
# 复制环境变量文件
cp .env.production .env.local

# 编辑配置（根据实际情况修改）
nano .env.local
```

#### 4. 构建和启动
```bash
# 给部署脚本执行权限
chmod +x deploy.sh

# 执行部署
./deploy.sh
```

#### 5. 验证部署
```bash
# 检查容器状态
docker-compose ps

# 查看日志
docker-compose logs -f

# 访问应用
curl http://localhost
```

### 方式二：传统部署

#### 1. 安装Node.js
```bash
# 使用NodeSource仓库安装Node.js 18
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# 安装pnpm
npm install -g pnpm
```

#### 2. 构建项目
```bash
# 安装依赖
pnpm install

# 构建生产版本
pnpm build

# 启动生产服务器
pnpm start
```

#### 3. 使用PM2管理进程（推荐）
```bash
# 安装PM2
npm install -g pm2

# 启动应用
pm2 start npm --name "tianlang-pdcs" -- start

# 设置开机自启
pm2 startup
pm2 save
```

## 🔧 配置说明

### 环境变量配置
编辑 `.env.local` 文件：

```env
# 应用配置
NEXT_PUBLIC_APP_NAME=朗天PDCS
NEXT_PUBLIC_API_URL=http://your-server-ip:3001/api

# 安全配置
NEXTAUTH_SECRET=your-very-secure-secret-key
NEXTAUTH_URL=http://your-domain.com

# 数据库配置（如果使用）
DATABASE_URL=your-database-connection-string
```

### Nginx反向代理配置
如果使用Nginx作为反向代理，编辑 `nginx.conf`：

```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        proxy_pass http://localhost:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## 🛡️ 安全配置

### 1. 防火墙设置
```bash
# Ubuntu UFW
sudo ufw allow 22    # SSH
sudo ufw allow 80    # HTTP
sudo ufw allow 443   # HTTPS
sudo ufw enable
```

### 2. SSL证书配置
```bash
# 使用Let's Encrypt
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d your-domain.com
```

### 3. 定期备份
```bash
# 创建备份脚本
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
docker-compose exec app tar -czf /app/backup_$DATE.tar.gz /app/data
```

## 📊 监控和维护

### 查看应用状态
```bash
# Docker方式
docker-compose ps
docker-compose logs -f app

# PM2方式
pm2 status
pm2 logs tianlang-pdcs
```

### 更新应用
```bash
# Docker方式
git pull
docker-compose down
docker-compose build --no-cache
docker-compose up -d

# 传统方式
git pull
pnpm install
pnpm build
pm2 restart tianlang-pdcs
```

### 性能优化
```bash
# 清理Docker资源
docker system prune -f

# 查看资源使用
docker stats
htop
```

## 🔍 故障排除

### 常见问题

1. **端口被占用**
```bash
# 查看端口占用
netstat -tulpn | grep :3000
# 或
lsof -i :3000
```

2. **内存不足**
```bash
# 增加swap空间
sudo fallocate -l 2G /swapfile
sudo chmod 600 /swapfile
sudo mkswap /swapfile
sudo swapon /swapfile
```

3. **权限问题**
```bash
# 修复文件权限
sudo chown -R $USER:$USER /path/to/tianlang-pdcs
```

### 日志位置
- Docker: `docker-compose logs`
- PM2: `~/.pm2/logs/`
- Nginx: `/var/log/nginx/`

## 📞 技术支持

如遇到部署问题，请检查：
1. 系统要求是否满足
2. 网络连接是否正常
3. 端口是否被占用
4. 日志文件中的错误信息

---

**部署完成后，访问 http://your-server-ip 即可使用朗天PDCS系统！**

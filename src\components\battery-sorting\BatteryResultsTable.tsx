'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { 
  Search, 
  Eye, 
  CheckCircle, 
  XCircle, 
  RotateCcw,
  Filter,
  Download
} from 'lucide-react';
import { BatteryResult } from '@/lib/types/battery-sorting';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';

interface BatteryResultsTableProps {
  results: BatteryResult[];
  sessionName?: string;
  isLoading?: boolean;
  onViewDetail?: (result: BatteryResult) => void;
  onExport?: () => void;
  className?: string;
}

const GRADE_COLORS = {
  'A+': 'bg-green-100 text-green-800 border-green-200',
  'A': 'bg-green-100 text-green-700 border-green-200',
  'B+': 'bg-blue-100 text-blue-800 border-blue-200',
  'B': 'bg-blue-100 text-blue-700 border-blue-200',
  'C': 'bg-yellow-100 text-yellow-800 border-yellow-200',
  'D': 'bg-orange-100 text-orange-800 border-orange-200',
  'Reject': 'bg-red-100 text-red-800 border-red-200'
};

const DECISION_ICONS = {
  accept: CheckCircle,
  reject: XCircle,
  retest: RotateCcw
};

const DECISION_COLORS = {
  accept: 'text-green-600',
  reject: 'text-red-600',
  retest: 'text-orange-600'
};

export function BatteryResultsTable({
  results,
  sessionName,
  isLoading = false,
  onViewDetail,
  onExport,
  className
}: BatteryResultsTableProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [channelFilter, setChannelFilter] = useState<string>('all');
  const [gradeFilter, setGradeFilter] = useState<string>('all');
  const [errorFilter, setErrorFilter] = useState<string>('all');

  // 获取所有可用的筛选选项
  const availableChannels = Array.from(new Set(results.map(r => r.channelId))).sort();
  const availableDevices = Array.from(new Set(results.map(r => r.deviceId))).sort();
  const availableErrors = Array.from(new Set(results.flatMap(r => r.errors))).sort();

  // 过滤结果
  const filteredResults = results.filter(result => {
    const matchesSearch = result.batteryId.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesChannel = channelFilter === 'all' || 
                          result.channelId === channelFilter || 
                          result.deviceId === channelFilter;
    const matchesGrade = gradeFilter === 'all' || result.grade === gradeFilter;
    const matchesError = errorFilter === 'all' || 
                        (errorFilter === 'none' && result.errors.length === 0) ||
                        result.errors.includes(errorFilter);
    
    return matchesSearch && matchesChannel && matchesGrade && matchesError;
  });

  const getDecisionLabel = (decision: string) => {
    switch (decision) {
      case 'accept': return '合格';
      case 'reject': return '不合格';
      case 'retest': return '重测';
      default: return decision;
    }
  };

  return (
    <Card className={`${className} flex flex-col h-full`}>
      <CardHeader className="flex-shrink-0">
        <CardTitle className="flex items-center justify-between">
          电池测试结果
          <div className="flex items-center space-x-2">
            <Badge variant="outline">{filteredResults.length} / {results.length}</Badge>
          </div>
        </CardTitle>
        <CardDescription>
          {sessionName ? `${sessionName} - 电池分选结果` : '请选择测试会话查看结果'}
        </CardDescription>
      </CardHeader>
      <CardContent className="flex flex-col flex-1 overflow-hidden">
        {results.length === 0 ? (
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center">
              <Filter className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-medium mb-2">暂无测试结果</h3>
              <p className="text-muted-foreground">
                {sessionName ? '该测试会话暂无电池结果' : '请从左侧选择一个测试会话'}
              </p>
            </div>
          </div>
        ) : (
          <>
            {/* 筛选控件 */}
            <div className="flex flex-wrap gap-4 mb-4 flex-shrink-0">
              <div className="flex-1 min-w-[200px]">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="搜索电池ID..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              
              <Select value={channelFilter} onValueChange={setChannelFilter}>
                <SelectTrigger className="w-40">
                  <SelectValue placeholder="通道/设备" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">所有通道</SelectItem>
                  {availableDevices.map(device => (
                    <SelectItem key={device} value={device}>设备 {device}</SelectItem>
                  ))}
                  {availableChannels.map(channel => (
                    <SelectItem key={channel} value={channel}>通道 {channel}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
              
              <Select value={gradeFilter} onValueChange={setGradeFilter}>
                <SelectTrigger className="w-32">
                  <SelectValue placeholder="等级" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">所有等级</SelectItem>
                  <SelectItem value="A+">A+</SelectItem>
                  <SelectItem value="A">A</SelectItem>
                  <SelectItem value="B+">B+</SelectItem>
                  <SelectItem value="B">B</SelectItem>
                  <SelectItem value="C">C</SelectItem>
                  <SelectItem value="D">D</SelectItem>
                  <SelectItem value="Reject">不合格</SelectItem>
                </SelectContent>
              </Select>
              
              <Select value={errorFilter} onValueChange={setErrorFilter}>
                <SelectTrigger className="w-40">
                  <SelectValue placeholder="错误信息" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">所有结果</SelectItem>
                  <SelectItem value="none">无错误</SelectItem>
                  {availableErrors.map(error => (
                    <SelectItem key={error} value={error}>{error}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* 结果表格 */}
            <div className="flex-1 border rounded-lg overflow-hidden">
              <div className="h-full overflow-auto">
                <Table className="relative">
                <TableHeader className="sticky top-0 bg-background z-10">
                  <TableRow>
                    <TableHead className="bg-background">电池ID</TableHead>
                    <TableHead className="bg-background">通道</TableHead>
                    <TableHead className="bg-background">设备</TableHead>
                    <TableHead className="bg-background">等级</TableHead>
                    <TableHead className="bg-background">评分</TableHead>
                    <TableHead className="bg-background">决策</TableHead>
                    <TableHead className="bg-background">错误信息</TableHead>
                    <TableHead className="bg-background">完成时间</TableHead>
                    <TableHead className="bg-background">操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {isLoading ? (
                    <TableRow>
                      <TableCell colSpan={9} className="text-center py-8">
                        <div className="flex items-center justify-center">
                          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary mr-2" />
                          加载中...
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : filteredResults.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={9} className="text-center py-8 text-muted-foreground">
                        没有符合条件的结果
                      </TableCell>
                    </TableRow>
                  ) : (
                    filteredResults.map((result) => {
                      const DecisionIcon = DECISION_ICONS[result.decision];
                      const decisionColor = DECISION_COLORS[result.decision];
                      
                      return (
                        <TableRow key={result.id}>
                          <TableCell className="font-medium">
                            {result.batteryId}
                          </TableCell>
                          <TableCell>
                            <Badge variant="outline" className="text-xs">
                              {result.channelId}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <Badge variant="outline" className="text-xs">
                              {result.deviceId}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <Badge 
                              variant="outline" 
                              className={cn('font-medium', GRADE_COLORS[result.grade])}
                            >
                              {result.grade}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center space-x-2">
                              <span className="font-medium text-sm">{result.overallScore.toFixed(1)}</span>
                              <div className="w-12 h-1.5 bg-gray-200 rounded-full overflow-hidden">
                                <div 
                                  className="h-full bg-gradient-to-r from-red-500 via-yellow-500 to-green-500"
                                  style={{ width: `${result.overallScore}%` }}
                                />
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center space-x-2">
                              <DecisionIcon className={cn('h-4 w-4', decisionColor)} />
                              <span className="text-sm">{getDecisionLabel(result.decision)}</span>
                            </div>
                          </TableCell>
                          <TableCell>
                            {result.errors.length > 0 ? (
                              <div className="space-y-1">
                                {result.errors.slice(0, 2).map((error, index) => (
                                  <Badge key={index} variant="destructive" className="text-xs block">
                                    {error}
                                  </Badge>
                                ))}
                                {result.errors.length > 2 && (
                                  <span className="text-xs text-muted-foreground">
                                    +{result.errors.length - 2} 更多
                                  </span>
                                )}
                              </div>
                            ) : (
                              <span className="text-xs text-muted-foreground">无</span>
                            )}
                          </TableCell>
                          <TableCell className="text-xs text-muted-foreground">
                            {format(result.completedAt, 'MM-dd HH:mm')}
                          </TableCell>
                          <TableCell>
                            {onViewDetail && (
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => onViewDetail(result)}
                              >
                                <Eye className="h-3 w-3 mr-1" />
                                详情
                              </Button>
                            )}
                          </TableCell>
                        </TableRow>
                      );
                    })
                  )}
                </TableBody>
                </Table>
              </div>
            </div>
          </>
        )}
      </CardContent>
    </Card>
  );
}

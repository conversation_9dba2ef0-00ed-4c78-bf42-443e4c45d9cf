import { NextRequest, NextResponse } from 'next/server';
import { ProcessService } from '@/lib/process/services/processService';
import { CreateProcessData, ProcessQueryParams } from '@/lib/process/types/process';

// GET /api/processes - 获取工艺流程列表
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    
    const statusParam = searchParams.get('status');
    const queryParams: ProcessQueryParams = {
      status: statusParam ? [statusParam as any] : undefined,
      creatorId: searchParams.get('creatorId') || undefined,
      sortBy: (searchParams.get('sortBy') as any) || 'updatedAt',
      sortOrder: (searchParams.get('sortOrder') as any) || 'desc',
      limit: parseInt(searchParams.get('limit') || '50'),
      offset: parseInt(searchParams.get('offset') || '0'),
    };

    const processService = new ProcessService();
    const processes = await processService.getProcesses(queryParams);

    return NextResponse.json({
      success: true,
      data: processes
    });
  } catch (error) {
    console.error('获取工艺流程列表失败:', error);
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : '获取工艺流程列表失败'
      },
      { status: 500 }
    );
  }
}

// POST /api/processes - 创建新的工艺流程
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const createData: CreateProcessData = body;

    const processService = new ProcessService();
    const process = await processService.createProcess(createData);

    return NextResponse.json({
      success: true,
      data: process
    });
  } catch (error) {
    console.error('创建工艺流程失败:', error);
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : '创建工艺流程失败'
      },
      { status: 500 }
    );
  }
}

/**
 * 工步发送服务
 * 处理工步流程向设备通道的发送和执行
 */

import { Step, StepFormData } from '../types/step';
import { Channel, ChannelOperation, ChannelBatchOperationResult } from '../types/channel';

// 工步发送配置
export interface StepSendConfig {
  autoStart: boolean;           // 发送后自动启动
  validateBeforeSend: boolean;  // 发送前验证
  overwriteExisting: boolean;   // 覆盖现有工步
  sendTimeout: number;          // 发送超时时间（秒）
  retryCount: number;           // 重试次数
  batchSize: number;            // 批量发送大小
}

// 工步发送请求
export interface StepSendRequest {
  steps: Step[];
  channelIds: string[];
  config: StepSendConfig;
  metadata?: {
    operatorId?: string;
    description?: string;
    priority?: 'low' | 'normal' | 'high';
  };
}

// 工步发送结果
export interface StepSendResult {
  success: boolean;
  requestId: string;
  totalChannels: number;
  successChannels: number;
  failedChannels: number;
  results: Array<{
    channelId: string;
    success: boolean;
    stepCount: number;
    error?: string;
    estimatedDuration?: number; // 预计执行时间（秒）
  }>;
  message?: string;
  warnings?: string[];
}

// 工步发送状态
export interface StepSendStatus {
  requestId: string;
  status: 'pending' | 'sending' | 'completed' | 'failed' | 'cancelled';
  progress: number; // 0-100
  currentChannel?: string;
  startTime: Date;
  endTime?: Date;
  error?: string;
}

// 工步验证结果
export interface StepValidationResult {
  isValid: boolean;
  errors: Array<{
    stepId: string;
    stepNumber: number;
    field: string;
    message: string;
  }>;
  warnings: Array<{
    stepId: string;
    stepNumber: number;
    field: string;
    message: string;
  }>;
}

// 通道兼容性检查结果
export interface ChannelCompatibilityResult {
  channelId: string;
  compatible: boolean;
  issues: Array<{
    type: 'capability' | 'configuration' | 'status';
    severity: 'error' | 'warning';
    message: string;
    stepNumber?: number;
  }>;
}

/**
 * 默认发送配置
 */
export const DEFAULT_SEND_CONFIG: StepSendConfig = {
  autoStart: false,
  validateBeforeSend: true,
  overwriteExisting: true,
  sendTimeout: 30,
  retryCount: 3,
  batchSize: 10
};

/**
 * 验证工步流程
 */
export function validateSteps(steps: Step[]): StepValidationResult {
  const errors: StepValidationResult['errors'] = [];
  const warnings: StepValidationResult['warnings'] = [];

  steps.forEach((step, index) => {
    const stepNumber = index + 1;

    // 基本验证
    if (!step.name || step.name.trim().length === 0) {
      errors.push({
        stepId: step.id,
        stepNumber,
        field: 'name',
        message: '工步名称不能为空'
      });
    }

    // 电压范围验证
    if (step.parameters.electrical.upperVoltage.value < step.parameters.electrical.lowerVoltage.value) {
      errors.push({
        stepId: step.id,
        stepNumber,
        field: 'voltage',
        message: '上限电压应大于等于下限电压'
      });
    }

    // 温度范围验证
    if (step.parameters.termination.upperTemperature.value <= step.parameters.termination.lowerTemperature.value) {
      errors.push({
        stepId: step.id,
        stepNumber,
        field: 'temperature',
        message: '温度上限应大于温度下限'
      });
    }

    // 电流范围验证
    const current = step.parameters.electrical.current.value;
    if (current <= 0) {
      errors.push({
        stepId: step.id,
        stepNumber,
        field: 'current',
        message: '电流值必须大于0'
      });
    }

    // 时间格式验证
    const timeFormat = /^(\d{1,2}):(\d{1,2}):(\d{1,2})(\.\d{1,3})?$/;
    if (!timeFormat.test(step.parameters.time.duration.value)) {
      errors.push({
        stepId: step.id,
        stepNumber,
        field: 'duration',
        message: '时间格式不正确，应为 hh:mm:ss.ms'
      });
    }

    // 警告检查
    if (current > 5000) {
      warnings.push({
        stepId: step.id,
        stepNumber,
        field: 'current',
        message: '电流值较大，请确认是否正确'
      });
    }

    if (step.parameters.electrical.upperVoltage.value > 4500) {
      warnings.push({
        stepId: step.id,
        stepNumber,
        field: 'upperVoltage',
        message: '上限电压较高，请注意安全'
      });
    }
  });

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
}

/**
 * 检查通道兼容性
 */
export function checkChannelCompatibility(
  steps: Step[], 
  channels: Channel[]
): ChannelCompatibilityResult[] {
  return channels.map(channel => {
    const issues: ChannelCompatibilityResult['issues'] = [];

    // 检查通道状态
    if (channel.status === 'offline') {
      issues.push({
        type: 'status',
        severity: 'error',
        message: '通道离线，无法发送工步'
      });
    }

    if (channel.status === 'error') {
      issues.push({
        type: 'status',
        severity: 'error',
        message: '通道处于错误状态，无法发送工步'
      });
    }

    if (channel.status === 'maintenance') {
      issues.push({
        type: 'status',
        severity: 'warning',
        message: '通道处于维护状态'
      });
    }

    // 检查工步与通道能力的兼容性
    steps.forEach((step, index) => {
      const stepNumber = index + 1;

      // 检查通道是否有capabilities字段（兼容不同的通道数据结构）
      const capabilities = channel.capabilities || {
        maxCurrent: 5000,    // 默认最大电流 5A
        maxVoltage: 5000,    // 默认最大电压 5V
        maxPower: 25000,     // 默认最大功率 25W
        minVoltage: 0,       // 默认最小电压 0V
        temperatureRange: [-40, 85] as [number, number], // 默认温度范围
        supportedModes: ['cc', 'cv', 'cccv'] // 默认支持的模式
      };

      // 电流能力检查
      if (step.parameters.electrical.current.value > capabilities.maxCurrent) {
        issues.push({
          type: 'capability',
          severity: 'error',
          message: `工步${stepNumber}电流(${step.parameters.electrical.current.value}mA)超出通道最大电流(${capabilities.maxCurrent}mA)`,
          stepNumber
        });
      }

      // 电压能力检查
      if (step.parameters.electrical.upperVoltage.value > capabilities.maxVoltage) {
        issues.push({
          type: 'capability',
          severity: 'error',
          message: `工步${stepNumber}上限电压(${step.parameters.electrical.upperVoltage.value}mV)超出通道最大电压(${capabilities.maxVoltage}mV)`,
          stepNumber
        });
      }

      if (step.parameters.electrical.lowerVoltage.value < capabilities.minVoltage) {
        issues.push({
          type: 'capability',
          severity: 'error',
          message: `工步${stepNumber}下限电压(${step.parameters.electrical.lowerVoltage.value}mV)低于通道最小电压(${capabilities.minVoltage}mV)`,
          stepNumber
        });
      }

      // 功率能力检查
      const estimatedPower = step.parameters.electrical.current.value * step.parameters.electrical.upperVoltage.value / 1000;
      if (estimatedPower > capabilities.maxPower) {
        issues.push({
          type: 'capability',
          severity: 'warning',
          message: `工步${stepNumber}预计功率(${estimatedPower.toFixed(1)}mW)可能超出通道最大功率(${capabilities.maxPower}mW)`,
          stepNumber
        });
      }

      // 模式支持检查
      if (!capabilities.supportedModes.includes(step.mode)) {
        issues.push({
          type: 'capability',
          severity: 'error',
          message: `工步${stepNumber}模式(${step.mode})不被通道支持`,
          stepNumber
        });
      }
    });

    return {
      channelId: channel.id || channel.channelNumber?.toString() || `channel_${Math.random()}`,
      compatible: issues.filter(issue => issue.severity === 'error').length === 0,
      issues
    };
  });
}

/**
 * 估算工步执行时间
 */
export function estimateStepDuration(steps: Step[]): number {
  return steps.reduce((total, step) => {
    // 解析时间字符串
    const timeMatch = step.parameters.time.duration.value.match(/^(\d{1,2}):(\d{1,2}):(\d{1,2})(\.\d{1,3})?$/);
    if (!timeMatch) return total;

    const hours = parseInt(timeMatch[1], 10);
    const minutes = parseInt(timeMatch[2], 10);
    const seconds = parseInt(timeMatch[3], 10);
    const milliseconds = timeMatch[4] ? parseFloat(timeMatch[4]) * 1000 : 0;

    const stepDuration = hours * 3600 + minutes * 60 + seconds + milliseconds / 1000;
    return total + stepDuration;
  }, 0);
}

/**
 * 发送工步到通道
 */
export async function sendStepsToChannels(request: StepSendRequest): Promise<StepSendResult> {
  const { steps, channelIds, config, metadata } = request;
  const requestId = `send_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

  try {
    // 验证工步
    if (config.validateBeforeSend) {
      const validation = validateSteps(steps);
      if (!validation.isValid) {
        return {
          success: false,
          requestId,
          totalChannels: channelIds.length,
          successChannels: 0,
          failedChannels: channelIds.length,
          results: channelIds.map(channelId => ({
            channelId,
            success: false,
            stepCount: 0,
            error: '工步验证失败'
          })),
          message: '工步验证失败，请修正错误后重试'
        };
      }
    }

    // 模拟发送过程
    const results: StepSendResult['results'] = [];
    let successCount = 0;
    let failedCount = 0;

    for (const channelId of channelIds) {
      try {
        // 模拟发送延迟
        await new Promise(resolve => setTimeout(resolve, 100));

        // 模拟发送成功/失败
        const success = Math.random() > 0.1; // 90% 成功率

        if (success) {
          successCount++;
          results.push({
            channelId,
            success: true,
            stepCount: steps.length,
            estimatedDuration: estimateStepDuration(steps)
          });
        } else {
          failedCount++;
          results.push({
            channelId,
            success: false,
            stepCount: 0,
            error: '通道通信失败'
          });
        }
      } catch (error) {
        failedCount++;
        results.push({
          channelId,
          success: false,
          stepCount: 0,
          error: error instanceof Error ? error.message : '发送失败'
        });
      }
    }

    return {
      success: successCount > 0,
      requestId,
      totalChannels: channelIds.length,
      successChannels: successCount,
      failedChannels: failedCount,
      results,
      message: `成功发送到 ${successCount} 个通道，失败 ${failedCount} 个通道`
    };

  } catch (error) {
    return {
      success: false,
      requestId,
      totalChannels: channelIds.length,
      successChannels: 0,
      failedChannels: channelIds.length,
      results: channelIds.map(channelId => ({
        channelId,
        success: false,
        stepCount: 0,
        error: '发送过程中发生错误'
      })),
      message: error instanceof Error ? error.message : '发送失败'
    };
  }
}

/**
 * 获取发送状态
 */
export async function getSendStatus(requestId: string): Promise<StepSendStatus | null> {
  // 模拟状态查询
  return {
    requestId,
    status: 'completed',
    progress: 100,
    startTime: new Date(Date.now() - 5000),
    endTime: new Date()
  };
}

/**
 * 取消发送
 */
export async function cancelSend(requestId: string): Promise<boolean> {
  // 模拟取消操作
  return true;
}

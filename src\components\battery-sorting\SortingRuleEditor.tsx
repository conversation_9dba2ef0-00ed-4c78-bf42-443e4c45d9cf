'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { Save, Play, Settings, FileText } from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';

import { VariableDefinitionPanel } from './VariableDefinitionPanel';
import { GradeDefinitionPanel } from './GradeDefinitionPanel';
import { ConditionEditor } from './ConditionEditor';

import { SortingRule, SortingVariable, SortingGrade } from '@/lib/types';
import { useTranslations } from 'next-intl';

interface SortingRuleEditorProps {
  rule?: SortingRule;
  onSave: (rule: SortingRule) => void;
  onTest?: (rule: SortingRule) => void;
}

export function SortingRuleEditor({
  rule,
  onSave,
  onTest
}: SortingRuleEditorProps) {
  const t = useTranslations('batterySorting.ruleEditor');
  
  // 规则基本信息
  const [ruleName, setRuleName] = useState(rule?.name || '');
  const [ruleDescription, setRuleDescription] = useState(rule?.description || '');
  const [isActive, setIsActive] = useState(rule?.isActive ?? true);
  
  // 变量定义
  const [variables, setVariables] = useState<SortingVariable[]>(rule?.variables || []);
  
  // 等级定义
  const [grades, setGrades] = useState<SortingGrade[]>(rule?.grades || []);
  
  // 条件表达式
  const [globalCondition, setGlobalCondition] = useState('');

  // 验证规则完整性
  const validateRule = (): string[] => {
    const errors: string[] = [];
    
    if (!ruleName.trim()) {
      errors.push(t('validation.nameRequired'));
    }
    
    if (variables.length === 0) {
      errors.push(t('validation.variablesRequired'));
    }
    
    if (grades.length === 0) {
      errors.push(t('validation.gradesRequired'));
    }
    
    // 检查等级条件
    const gradesWithoutConditions = grades.filter(g => !g.conditions.trim());
    if (gradesWithoutConditions.length > 0) {
      errors.push(t('validation.gradeConditionsRequired'));
    }
    
    // 检查变量引用
    const variableNames = variables.map(v => v.name);
    for (const grade of grades) {
      const usedVariables = grade.conditions.match(/[a-zA-Z\u4e00-\u9fa5][a-zA-Z0-9\u4e00-\u9fa5]*/g) || [];
      const logicalKeywords = ['AND', 'OR', 'NOT'];
      
      for (const usedVar of usedVariables) {
        if (!logicalKeywords.includes(usedVar) && !variableNames.includes(usedVar)) {
          errors.push(t('validation.undefinedVariable', { variable: usedVar, grade: grade.name }));
        }
      }
    }
    
    return errors;
  };

  const handleSave = () => {
    const errors = validateRule();
    if (errors.length > 0) {
      alert(t('validation.errors') + '\n' + errors.join('\n'));
      return;
    }

    const newRule: SortingRule = {
      id: rule?.id || generateId(),
      name: ruleName,
      description: ruleDescription,
      variables,
      grades,
      conditionTree: {
        id: generateId(),
        type: 'logical',
        logicalOperator: 'AND' as any,
        children: []
      }, // 简化处理，实际应该解析条件表达式
      isActive,
      createdAt: rule?.createdAt || new Date(),
      updatedAt: new Date()
    };

    onSave(newRule);
  };

  const handleTest = () => {
    const errors = validateRule();
    if (errors.length > 0) {
      alert(t('validation.errors') + '\n' + errors.join('\n'));
      return;
    }

    if (onTest) {
      const testRule: SortingRule = {
        id: rule?.id || 'test-rule',
        name: ruleName,
        description: ruleDescription,
        variables,
        grades,
        conditionTree: {
          id: generateId(),
          type: 'logical',
          logicalOperator: 'AND' as any,
          children: []
        },
        isActive,
        createdAt: rule?.createdAt || new Date(),
        updatedAt: new Date()
      };
      
      onTest(testRule);
    }
  };

  const generateId = () => Date.now().toString(36) + Math.random().toString(36).substr(2);

  const validationErrors = validateRule();
  const isValid = validationErrors.length === 0;

  return (
    <div className="space-y-6">
      {/* 规则基本信息 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            {t('basicInfo.title')}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="ruleName">{t('basicInfo.ruleName')}</Label>
              <Input
                id="ruleName"
                value={ruleName}
                onChange={(e) => setRuleName(e.target.value)}
                placeholder={t('basicInfo.ruleNamePlaceholder')}
              />
            </div>
            
            <div className="flex items-center space-x-2">
              <Switch
                id="isActive"
                checked={isActive}
                onCheckedChange={setIsActive}
              />
              <Label htmlFor="isActive">{t('basicInfo.isActive')}</Label>
            </div>
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="ruleDescription">{t('basicInfo.description')}</Label>
            <Textarea
              id="ruleDescription"
              value={ruleDescription}
              onChange={(e) => setRuleDescription(e.target.value)}
              placeholder={t('basicInfo.descriptionPlaceholder')}
              rows={3}
            />
          </div>

          {/* 规则状态 */}
          <div className="flex items-center gap-4 pt-2">
            <div className="flex items-center gap-2">
              <span className="text-sm text-muted-foreground">{t('basicInfo.status')}:</span>
              <Badge variant={isValid ? 'default' : 'destructive'}>
                {isValid ? t('basicInfo.valid') : t('basicInfo.invalid')}
              </Badge>
            </div>
            
            <div className="flex items-center gap-2">
              <span className="text-sm text-muted-foreground">{t('basicInfo.variables')}:</span>
              <Badge variant="secondary">{variables.length}</Badge>
            </div>
            
            <div className="flex items-center gap-2">
              <span className="text-sm text-muted-foreground">{t('basicInfo.grades')}:</span>
              <Badge variant="secondary">{grades.length}</Badge>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 变量定义区和等级设定区 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <VariableDefinitionPanel
          variables={variables}
          onVariablesChange={setVariables}
        />
        
        <GradeDefinitionPanel
          grades={grades}
          onGradesChange={setGrades}
        />
      </div>

      {/* 条件编辑区 */}
      <ConditionEditor
        variables={variables}
        condition={globalCondition}
        onConditionChange={setGlobalCondition}
      />

      {/* 验证错误提示 */}
      {validationErrors.length > 0 && (
        <Alert variant="destructive">
          <AlertDescription>
            <div className="space-y-1">
              <p className="font-medium">{t('validation.errors')}</p>
              <ul className="text-sm space-y-1 ml-4">
                {validationErrors.map((error, index) => (
                  <li key={index}>• {error}</li>
                ))}
              </ul>
            </div>
          </AlertDescription>
        </Alert>
      )}

      {/* 操作按钮 */}
      <div className="flex justify-end gap-2">
        {onTest && (
          <Button
            variant="outline"
            onClick={handleTest}
            disabled={!isValid}
          >
            <Play className="h-4 w-4 mr-2" />
            {t('actions.test')}
          </Button>
        )}
        
        <Button
          onClick={handleSave}
          disabled={!isValid}
        >
          <Save className="h-4 w-4 mr-2" />
          {t('actions.save')}
        </Button>
      </div>
    </div>
  );
}

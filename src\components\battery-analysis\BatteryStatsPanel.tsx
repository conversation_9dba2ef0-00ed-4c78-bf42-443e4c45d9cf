'use client';

import React, { useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Separator } from '@/components/ui/separator';
import { 
  Battery, 
  Zap, 
  RotateCcw, 
  Thermometer, 
  TrendingDown,
  TrendingUp,
  AlertTriangle,
  CheckCircle2
} from 'lucide-react';
import { BatteryDataRecord, BatteryPerformanceStats } from '@/lib/types';
import { calculateBatteryPerformanceStats, predictBatteryLifetime } from '@/lib/utils/batteryAnalysis';
import { useTranslations } from 'next-intl';

interface BatteryStatsPanelProps {
  data: BatteryDataRecord[];
  className?: string;
}

export function BatteryStatsPanel({ data, className }: BatteryStatsPanelProps) {
  const t = useTranslations('batteryAnalysis.stats');

  // 计算统计数据
  const stats = useMemo(() => {
    return calculateBatteryPerformanceStats(data);
  }, [data]);

  // 预测电池寿命
  const lifetimePrediction = useMemo(() => {
    return predictBatteryLifetime(data);
  }, [data]);

  // 获取健康状态
  const getHealthStatus = (retention: number) => {
    if (retention >= 90) return { status: 'excellent', color: 'text-green-600', icon: CheckCircle2 };
    if (retention >= 80) return { status: 'good', color: 'text-blue-600', icon: CheckCircle2 };
    if (retention >= 70) return { status: 'fair', color: 'text-yellow-600', icon: AlertTriangle };
    return { status: 'poor', color: 'text-red-600', icon: AlertTriangle };
  };

  const healthStatus = getHealthStatus(stats.capacityStats.retention);
  const HealthIcon = healthStatus.icon;

  if (data.length === 0) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Battery className="h-5 w-5" />
            {t('title')}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-32 text-muted-foreground">
            {t('noData')}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* 电池健康概览 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Battery className="h-5 w-5" />
            {t('batteryHealth.title')}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <HealthIcon className={`h-5 w-5 ${healthStatus.color}`} />
              <span className="font-medium">{t(`batteryHealth.status.${healthStatus.status}`)}</span>
            </div>
            <Badge variant="outline" className="text-lg px-3 py-1">
              {stats.capacityStats.retention.toFixed(1)}%
            </Badge>
          </div>
          
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>{t('batteryHealth.capacityRetention')}</span>
              <span>{stats.capacityStats.retention.toFixed(1)}%</span>
            </div>
            <Progress value={stats.capacityStats.retention} className="h-2" />
          </div>

          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-muted-foreground">{t('batteryHealth.initialCapacity')}:</span>
              <div className="font-medium">{stats.capacityStats.initial.toFixed(4)} Ah</div>
            </div>
            <div>
              <span className="text-muted-foreground">{t('batteryHealth.currentCapacity')}:</span>
              <div className="font-medium">{stats.capacityStats.current.toFixed(4)} Ah</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 容量统计 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Battery className="h-5 w-5" />
            {t('capacity.title')}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">
                {stats.capacityStats.maxCapacity.toFixed(3)}
              </div>
              <div className="text-sm text-muted-foreground">{t('capacity.maxCapacity')} (Ah)</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {stats.capacityStats.avgCapacity.toFixed(3)}
              </div>
              <div className="text-sm text-muted-foreground">{t('capacity.avgCapacity')} (Ah)</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600">
                {stats.capacityStats.minCapacity.toFixed(3)}
              </div>
              <div className="text-sm text-muted-foreground">{t('capacity.minCapacity')} (Ah)</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-red-600 flex items-center justify-center gap-1">
                <TrendingDown className="h-5 w-5" />
                {stats.capacityStats.degradationRate.toFixed(4)}
              </div>
              <div className="text-sm text-muted-foreground">{t('capacity.fadeRate')} (%/cycle)</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 能量效率 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Zap className="h-5 w-5" />
            {t('energy.title')}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {stats.energyStats.chargeEfficiency.toFixed(1)}%
              </div>
              <div className="text-sm text-muted-foreground">{t('energy.chargeEfficiency')}</div>
              <Progress value={stats.energyStats.chargeEfficiency} className="mt-2 h-2" />
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">
                {stats.energyStats.dischargeEfficiency.toFixed(1)}%
              </div>
              <div className="text-sm text-muted-foreground">{t('energy.dischargeEfficiency')}</div>
              <Progress value={stats.energyStats.dischargeEfficiency} className="mt-2 h-2" />
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">
                {stats.energyStats.roundTripEfficiency.toFixed(1)}%
              </div>
              <div className="text-sm text-muted-foreground">{t('energy.roundTripEfficiency')}</div>
              <Progress value={stats.energyStats.roundTripEfficiency} className="mt-2 h-2" />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 循环统计 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <RotateCcw className="h-5 w-5" />
            {t('cycle.title')}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">
                {stats.cycleStats.totalCycles}
              </div>
              <div className="text-sm text-muted-foreground">{t('cycle.totalCycles')}</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {stats.cycleStats.completedCycles}
              </div>
              <div className="text-sm text-muted-foreground">{t('cycle.completedCycles')}</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600">
                {stats.cycleStats.avgCycleTime.toFixed(1)}
              </div>
              <div className="text-sm text-muted-foreground">{t('cycle.avgCycleTime')} (h)</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">
                {stats.cycleStats.maxCycleTime.toFixed(1)}
              </div>
              <div className="text-sm text-muted-foreground">{t('cycle.maxCycleTime')} (h)</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 温度统计 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Thermometer className="h-5 w-5" />
            {t('temperature.title')}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-red-600">
                {stats.temperatureStats.maxTemperature.toFixed(1)}°C
              </div>
              <div className="text-sm text-muted-foreground">{t('temperature.maxTemp')}</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">
                {stats.temperatureStats.avgTemperature.toFixed(1)}°C
              </div>
              <div className="text-sm text-muted-foreground">{t('temperature.avgTemp')}</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-cyan-600">
                {stats.temperatureStats.minTemperature.toFixed(1)}°C
              </div>
              <div className="text-sm text-muted-foreground">{t('temperature.minTemp')}</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600">
                {stats.temperatureStats.tempVariation.toFixed(1)}°C
              </div>
              <div className="text-sm text-muted-foreground">{t('temperature.variation')}</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 寿命预测 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            {t('lifetime.title')}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <span className="font-medium">{t('lifetime.predictedCycles')}</span>
            <div className="flex items-center gap-2">
              <Badge variant="outline" className="text-lg px-3 py-1">
                {lifetimePrediction.predictedCycles}
              </Badge>
              <Badge 
                variant={lifetimePrediction.confidence === 'high' ? 'default' : 
                        lifetimePrediction.confidence === 'medium' ? 'secondary' : 'outline'}
              >
                {t(`lifetime.confidence.${lifetimePrediction.confidence}`)}
              </Badge>
            </div>
          </div>

          <Separator />

          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-muted-foreground">{t('lifetime.currentRetention')}:</span>
              <div className="font-medium">{lifetimePrediction.currentRetention.toFixed(1)}%</div>
            </div>
            <div>
              <span className="text-muted-foreground">{t('lifetime.fadeRate')}:</span>
              <div className="font-medium">{lifetimePrediction.fadeRatePerCycle.toFixed(4)}%/cycle</div>
            </div>
          </div>

          <div className="text-xs text-muted-foreground">
            {t('lifetime.disclaimer')}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

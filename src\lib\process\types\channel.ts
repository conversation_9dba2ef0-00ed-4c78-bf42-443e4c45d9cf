/**
 * 通道相关类型定义
 * 用于设备通道管理和选择
 */

// 通道状态枚举
export enum ChannelStatus {
  IDLE = 'idle',           // 空闲
  RUNNING = 'running',     // 运行中
  PAUSED = 'paused',       // 暂停
  ERROR = 'error',         // 错误
  OFFLINE = 'offline',     // 离线
  MAINTENANCE = 'maintenance' // 维护中
}

// 通道类型枚举
export enum ChannelType {
  CHARGE = 'charge',       // 充电通道
  DISCHARGE = 'discharge', // 放电通道
  HYBRID = 'hybrid'        // 充放电一体通道
}

// 电池信息
export interface BatteryInfo {
  id: string;
  name: string;
  type: string;
  capacity: number; // mAh
  voltage: number;  // mV
  temperature: number; // ℃
  soc: number;      // 电量百分比 0-100
  health: number;   // 健康度百分比 0-100
  cycleCount: number; // 循环次数
}

// 通道能力
export interface ChannelCapabilities {
  maxCurrent: number;      // 最大电流 mA
  maxVoltage: number;      // 最大电压 mV
  maxPower: number;        // 最大功率 mW
  minVoltage: number;      // 最小电压 mV
  temperatureRange: [number, number]; // 温度范围 ℃
  supportedModes: string[]; // 支持的工步模式
}

// 通道实时数据
export interface ChannelRealTimeData {
  current: number;         // 当前电流 mA
  voltage: number;         // 当前电压 mV
  power: number;          // 当前功率 mW
  capacity: number;       // 当前容量 mAh
  temperature: number;    // 当前温度 ℃
  timestamp: Date;        // 数据时间戳
}

// 通道配置
export interface ChannelConfig {
  name: string;
  description?: string;
  enabled: boolean;
  autoStart: boolean;
  safetyLimits: {
    maxCurrent: number;
    maxVoltage: number;
    maxTemperature: number;
    minVoltage: number;
    minTemperature: number;
  };
}

// 通道完整信息
export interface Channel {
  id: string;
  deviceId: string;
  channelNumber: number;
  name: string;
  type: ChannelType;
  status: ChannelStatus;
  isSelected: boolean;
  
  // 硬件信息
  capabilities: ChannelCapabilities;
  config: ChannelConfig;
  
  // 电池信息（如果有电池连接）
  batteryInfo?: BatteryInfo;
  
  // 实时数据
  realTimeData?: ChannelRealTimeData;
  
  // 当前工步信息
  currentStep?: {
    stepId: string;
    stepName: string;
    stepNumber: number;
    startTime: Date;
    estimatedEndTime?: Date;
    progress: number; // 0-100
  };
  
  // 统计信息
  statistics: {
    totalRunTime: number;    // 总运行时间（秒）
    totalCycles: number;     // 总循环次数
    totalCapacity: number;   // 总容量 mAh
    lastMaintenanceDate?: Date;
    nextMaintenanceDate?: Date;
  };
  
  // 时间戳
  createdAt: Date;
  updatedAt: Date;
  lastActiveAt?: Date;
}

// 设备信息
export interface Device {
  id: string;
  name: string;
  model: string;
  serialNumber: string;
  firmwareVersion: string;
  status: 'online' | 'offline' | 'error';
  channels: Channel[];
  location?: string;
  description?: string;
  createdAt: Date;
  updatedAt: Date;
}

// 通道选择器配置
export interface ChannelSelectorConfig {
  multiSelect: boolean;           // 是否支持多选
  showBatteryInfo: boolean;       // 是否显示电池信息
  showRealTimeData: boolean;      // 是否显示实时数据
  showStatistics: boolean;        // 是否显示统计信息
  filterByStatus: ChannelStatus[]; // 按状态筛选
  filterByType: ChannelType[];    // 按类型筛选
  maxSelection?: number;          // 最大选择数量
  groupByDevice: boolean;         // 是否按设备分组
  compactMode: boolean;           // 紧凑模式
}

// 通道选择结果
export interface ChannelSelectionResult {
  selectedChannels: Channel[];
  selectedChannelIds: string[];
  selectionCount: number;
}

// 通道操作
export interface ChannelOperation {
  type: 'start' | 'stop' | 'pause' | 'resume' | 'reset';
  channelIds: string[];
  parameters?: Record<string, any>;
}

// 通道批量操作结果
export interface ChannelBatchOperationResult {
  success: boolean;
  successCount: number;
  failureCount: number;
  results: Array<{
    channelId: string;
    success: boolean;
    error?: string;
  }>;
  message?: string;
}

// 通道搜索查询
export interface ChannelSearchQuery {
  keyword?: string;
  deviceId?: string;
  status?: ChannelStatus[];
  type?: ChannelType[];
  hasBattery?: boolean;
  isRunning?: boolean;
  sortBy?: 'channelNumber' | 'name' | 'status' | 'lastActiveAt';
  sortOrder?: 'asc' | 'desc';
}

// 通道列表响应
export interface ChannelListResponse {
  channels: Channel[];
  devices: Device[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

// 通道事件
export interface ChannelEvent {
  id: string;
  channelId: string;
  type: 'status_change' | 'data_update' | 'error' | 'warning' | 'step_complete';
  message: string;
  data?: Record<string, any>;
  timestamp: Date;
  severity: 'info' | 'warning' | 'error';
}

// 通道监控配置
export interface ChannelMonitorConfig {
  enableRealTimeUpdates: boolean;
  updateInterval: number; // 更新间隔（毫秒）
  enableAlerts: boolean;
  alertThresholds: {
    temperature: { min: number; max: number };
    voltage: { min: number; max: number };
    current: { min: number; max: number };
  };
}

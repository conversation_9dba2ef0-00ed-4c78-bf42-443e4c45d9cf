/**
 * 电池分选相关数据类型定义
 */

// 测试会话（一次工艺流程的执行）
export interface TestSession {
  id: string;
  processId: string;
  processName: string;
  startTime: Date;
  endTime?: Date;
  status: 'running' | 'completed' | 'failed' | 'cancelled';
  totalBatteries: number;
  completedBatteries: number;
  passedBatteries: number;
  failedBatteries: number;
  operator: string;
  description?: string;

  // 统计信息
  statistics: {
    passRate: number;
    averageScore: number;
    gradeDistribution: Record<string, number>;
    deviceDistribution: Record<string, number>;
  };
}

// 测试参数标准
export interface TestStandard {
  parameter: string;
  minValue: number;
  maxValue: number;
  unit: string;
  weight: number; // 权重，用于综合评分
}

// 单项测试结果
export interface TestResult {
  parameter: string;
  measuredValue: number;
  standardValue: number;
  tolerance: number;
  unit: string;
  result: 'pass' | 'fail';
  deviation: number; // 偏差百分比
}

// 电池测试数据
export interface BatteryTestData {
  batteryId: string;
  batchNumber: string;
  testDate: Date;
  processId: string; // 关联的工艺流程ID
  channelId: string; // 测试通道ID
  deviceId: string; // 测试设备ID
  
  // 基本信息
  batteryInfo: {
    model: string;
    manufacturer: string;
    nominalCapacity: number;
    nominalVoltage: number;
    weight: number;
  };
  
  // 测试结果
  testResults: {
    capacity: TestResult;
    voltage: TestResult;
    internalResistance: TestResult;
    temperature: TestResult;
    cycleLife?: TestResult;
    selfDischarge?: TestResult;
  };
  
  // 工步执行记录
  stepExecutions: {
    stepId: string;
    stepName: string;
    startTime: Date;
    endTime: Date;
    status: 'completed' | 'failed' | 'skipped';
    parameters: Record<string, any>;
    measurements: Record<string, number>;
  }[];
  
  // 测试环境
  testEnvironment: {
    temperature: number;
    humidity: number;
    pressure: number;
  };
}

// 电池测试结果（单个电池在某次测试会话中的结果）
export interface BatteryResult {
  id: string;
  batteryId: string;
  sessionId: string; // 关联的测试会话ID
  channelId: string;
  deviceId: string;

  // 测试数据
  testData: BatteryTestData;

  // 分选评级
  grade: 'A+' | 'A' | 'B+' | 'B' | 'C' | 'D' | 'Reject';

  // 综合评分 (0-100)
  overallScore: number;

  // 各项评分
  scores: {
    capacity: number;
    voltage: number;
    resistance: number;
    temperature: number;
    overall: number;
  };

  // 分选决策
  decision: 'accept' | 'reject' | 'retest';

  // 错误信息
  errors: string[];

  // 建议用途
  recommendedUse: string[];

  // 测试完成时间
  completedAt: Date;

  // 备注
  notes?: string;
}

// 保持向后兼容的别名
export type SortingResult = BatteryResult;

// 分选统计数据
export interface SortingStatistics {
  totalTested: number;
  totalPassed: number;
  totalFailed: number;
  passRate: number;
  
  // 按等级分布
  gradeDistribution: {
    grade: string;
    count: number;
    percentage: number;
  }[];
  
  // 按失败原因分布
  failureReasons: {
    reason: string;
    count: number;
    percentage: number;
  }[];
  
  // 按批次统计
  batchStatistics: {
    batchNumber: string;
    totalCount: number;
    passCount: number;
    passRate: number;
    averageScore: number;
  }[];
  
  // 趋势数据
  trends: {
    date: string;
    totalTested: number;
    passRate: number;
    averageScore: number;
  }[];
}

// 分选标准配置
export interface SortingStandards {
  id: string;
  name: string;
  description: string;
  version: string;
  createdDate: Date;
  isActive: boolean;
  
  // 测试标准
  testStandards: {
    capacity: TestStandard;
    voltage: TestStandard;
    internalResistance: TestStandard;
    temperature: TestStandard;
  };
  
  // 等级划分标准
  gradeThresholds: {
    'A+': { minScore: number; requirements: string[] };
    'A': { minScore: number; requirements: string[] };
    'B+': { minScore: number; requirements: string[] };
    'B': { minScore: number; requirements: string[] };
    'C': { minScore: number; requirements: string[] };
    'D': { minScore: number; requirements: string[] };
  };
  
  // 自动分选规则
  autoSortingRules: {
    rejectIfAnyFail: boolean;
    retestThreshold: number;
    maxRetestCount: number;
  };
}

// 分选报告
export interface SortingReport {
  id: string;
  reportType: 'batch' | 'daily' | 'weekly' | 'monthly' | 'custom';
  title: string;
  generatedDate: Date;
  dateRange: {
    startDate: Date;
    endDate: Date;
  };
  
  // 报告数据
  summary: SortingStatistics;
  details: SortingResult[];
  
  // 分析结论
  analysis: {
    qualityTrend: 'improving' | 'stable' | 'declining';
    majorIssues: string[];
    recommendations: string[];
  };
  
  // 报告配置
  config: {
    includeBatchDetails: boolean;
    includeFailureAnalysis: boolean;
    includeRecommendations: boolean;
    format: 'pdf' | 'excel' | 'csv';
  };
}

// 分选查询参数
export interface SortingQueryParams {
  dateRange?: {
    startDate: Date;
    endDate: Date;
  };
  batchNumbers?: string[];
  grades?: string[];
  decisions?: ('accept' | 'reject' | 'retest')[];
  operators?: string[];
  deviceIds?: string[];
  sortBy?: 'date' | 'score' | 'grade' | 'batch';
  sortOrder?: 'asc' | 'desc';
  page?: number;
  pageSize?: number;
}

/**
 * 用户相关类型定义
 */

export interface User {
  id: string;
  username: string;
  name: string;
  email?: string;
  role: UserR<PERSON>;
  department?: string;
  avatar?: string;
  createdAt: Date;
  lastLoginAt?: Date;
  isActive: boolean;
}

export type UserRole = 
  | 'admin'      // 管理员
  | 'engineer'   // 工程师
  | 'operator'   // 操作员
  | 'viewer';    // 查看者

export interface UserPermissions {
  canCreateProcess: boolean;
  canEditProcess: boolean;
  canDeleteProcess: boolean;
  canExecuteProcess: boolean;
  canViewProcess: boolean;
  canManageUsers: boolean;
  canManageDevices: boolean;
  canViewReports: boolean;
}

export interface LoginCredentials {
  username: string;
  password: string;
}

export interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}

export interface AuthContextType extends AuthState {
  login: (credentials: LoginCredentials) => Promise<void>;
  logout: () => void;
  refreshUser: () => Promise<void>;
  hasPermission: (permission: keyof UserPermissions) => boolean;
}

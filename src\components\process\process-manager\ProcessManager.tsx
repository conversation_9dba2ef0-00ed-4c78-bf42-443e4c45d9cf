'use client';

import React, { useState, useEffect } from 'react';
import { Settings, Plus, FolderOpen, FileText, Calendar, User, Loader2, Trash2 } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { ScrollArea } from '@/components/ui/scroll-area';

import { ProcessWizard } from '../process-wizard/ProcessWizard';
import { useProcessStore, useCurrentProcess, useProcesses, useProcessLoading } from '@/lib/process/stores/processStore';
import { ProcessInfo, CreateProcessData } from '@/lib/process/types/process';

interface ProcessManagerProps {
  trigger?: React.ReactNode;
  onProcessSelected?: (processId: string) => void;
  className?: string;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
}

export function ProcessManager({
  trigger,
  onProcessSelected,
  className,
  open: externalOpen,
  onOpenChange: externalOnOpenChange
}: ProcessManagerProps) {
  const [internalOpen, setInternalOpen] = useState(false);
  const [selectedAction, setSelectedAction] = useState<'create' | 'load' | null>(null);

  // 使用外部控制的open状态，如果没有则使用内部状态
  const isOpen = externalOpen !== undefined ? externalOpen : internalOpen;
  const setIsOpen = externalOnOpenChange || setInternalOpen;
  
  const currentProcess = useCurrentProcess();
  const processes = useProcesses();
  const isLoading = useProcessLoading();
  
  const {
    loadProcesses,
    loadProcess,
    createProcess,
    setCurrentProcess,
    clearError,
    initialize,
    deleteProcess
  } = useProcessStore();

  // 初始化时加载工艺流程列表
  useEffect(() => {
    if (isOpen) {
      initialize();
    }
  }, [isOpen, initialize]);

  const handleCreateProcess = async (data: CreateProcessData) => {
    try {
      const newProcess = await createProcess(data);
      setSelectedAction(null);
      setIsOpen(false);
      
      // 通知父组件工艺流程已选择
      if (onProcessSelected) {
        onProcessSelected(newProcess.id);
      }
    } catch (error) {
      // 错误已在store中处理
      console.error('创建工艺流程失败:', error);
    }
  };

  const handleLoadProcess = async (processInfo: ProcessInfo) => {
    try {
      const process = await loadProcess(processInfo.id);
      if (process) {
        setSelectedAction(null);
        setIsOpen(false);
        
        // 通知父组件工艺流程已选择
        if (onProcessSelected) {
          onProcessSelected(process.id);
        }
      }
    } catch (error) {
      console.error('加载工艺流程失败:', error);
    }
  };

  const handleNewSteps = () => {
    // 创建新的工步（清空当前工艺流程的工步）
    if (currentProcess) {
      setCurrentProcess({
        ...currentProcess,
        steps: []
      });
    }
    setIsOpen(false);
  };

  const getStatusBadge = (status: string) => {
    const variants = {
      draft: 'secondary',
      active: 'default',
      paused: 'outline',
      completed: 'outline',
      cancelled: 'destructive'
    } as const;

    const labels = {
      draft: '草稿',
      active: '活跃',
      paused: '暂停',
      completed: '完成',
      cancelled: '取消'
    };

    return (
      <Badge variant={variants[status as keyof typeof variants] || 'secondary'}>
        {labels[status as keyof typeof labels] || status}
      </Badge>
    );
  };

  const handleDeleteProcess = async (e: React.MouseEvent, process: ProcessInfo) => {
    e.stopPropagation(); // 阻止事件冒泡，避免触发卡片点击

    if (!confirm(`确定要删除工艺流程"${process.name}"吗？此操作不可撤销。`)) {
      return;
    }

    try {
      await deleteProcess(process.id);

      // 如果删除的是当前工艺流程，清空当前选择
      if (currentProcess?.id === process.id) {
        setCurrentProcess(null);
      }

      // 重新加载工艺流程列表
      await loadProcesses();
    } catch (error) {
      console.error('删除工艺流程失败:', error);
      alert('删除工艺流程失败，请重试');
    }
  };

  const renderActionSelection = () => (
    <div className="space-y-4">
      <div className="text-center">
        <h3 className="text-lg font-medium mb-2">选择操作</h3>
        <p className="text-sm text-muted-foreground mb-6">
          您想要创建新的工艺流程还是加载已有的工艺流程？
        </p>
      </div>

      <div className="grid gap-4">
        <Card 
          className="cursor-pointer hover:shadow-md transition-shadow"
          onClick={() => setSelectedAction('create')}
        >
          <CardHeader className="pb-3">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-primary/10 rounded-lg">
                <Plus className="h-5 w-5 text-primary" />
              </div>
              <div>
                <CardTitle className="text-base">创建新工艺流程</CardTitle>
                <CardDescription>
                  创建一个全新的工艺流程，然后添加工步
                </CardDescription>
              </div>
            </div>
          </CardHeader>
        </Card>

        <Card 
          className="cursor-pointer hover:shadow-md transition-shadow"
          onClick={() => setSelectedAction('load')}
        >
          <CardHeader className="pb-3">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-blue-500/10 rounded-lg">
                <FolderOpen className="h-5 w-5 text-blue-500" />
              </div>
              <div>
                <CardTitle className="text-base">加载已有工艺流程</CardTitle>
                <CardDescription>
                  选择一个已创建的工艺流程进行编辑
                </CardDescription>
              </div>
            </div>
          </CardHeader>
        </Card>
      </div>

      {currentProcess && (
        <>
          <Separator />
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-base">当前工艺流程</CardTitle>
              <CardDescription>
                {currentProcess.name}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <div className="flex items-center space-x-2">
                    {getStatusBadge(currentProcess.status)}
                    <span className="text-sm text-muted-foreground">
                      {currentProcess.totalSteps} 个工步
                    </span>
                  </div>
                  <p className="text-xs text-muted-foreground">
                    创建者: {currentProcess.creatorName}
                  </p>
                </div>
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={handleNewSteps}
                >
                  <Plus className="h-3 w-3 mr-1" />
                  新建工步
                </Button>
              </div>
            </CardContent>
          </Card>
        </>
      )}
    </div>
  );

  const renderProcessList = () => (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium">选择工艺流程</h3>
        <Button 
          variant="ghost" 
          size="sm"
          onClick={() => setSelectedAction(null)}
        >
          返回
        </Button>
      </div>

      {isLoading ? (
        <div className="flex items-center justify-center py-8">
          <Loader2 className="h-6 w-6 animate-spin" />
          <span className="ml-2 text-sm text-muted-foreground">加载中...</span>
        </div>
      ) : processes.length === 0 ? (
        <Alert>
          <FileText className="h-4 w-4" />
          <AlertDescription>
            暂无工艺流程，请先创建一个新的工艺流程。
          </AlertDescription>
        </Alert>
      ) : (
        <ScrollArea className="h-96">
          <div className="space-y-3">
            {processes.map((process) => (
              <Card 
                key={process.id}
                className="cursor-pointer hover:shadow-md transition-shadow"
                onClick={() => handleLoadProcess(process)}
              >
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-1">
                        <h4 className="font-medium">{process.name}</h4>
                        {getStatusBadge(process.status)}
                      </div>
                      {process.description && (
                        <p className="text-sm text-muted-foreground mb-2">
                          {process.description}
                        </p>
                      )}
                      <div className="flex items-center space-x-4 text-xs text-muted-foreground">
                        <div className="flex items-center space-x-1">
                          <User className="h-3 w-3" />
                          <span>{process.creatorName}</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <Calendar className="h-3 w-3" />
                          <span>{process.createdAt.toLocaleDateString()}</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <FileText className="h-3 w-3" />
                          <span>{process.totalSteps} 工步</span>
                        </div>
                      </div>
                    </div>
                    <div className="flex-shrink-0 ml-2">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={(e) => handleDeleteProcess(e, process)}
                        className="text-red-600 hover:text-red-700 hover:bg-red-50"
                        title="删除工艺流程"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </ScrollArea>
      )}
    </div>
  );

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        {trigger || (
          <Button className={className}>
            <Settings className="mr-2 h-4 w-4" />
            工艺管理器
          </Button>
        )}
      </DialogTrigger>
      
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-hidden flex flex-col">
        <DialogHeader>
          <DialogTitle>工艺管理器</DialogTitle>
          <DialogDescription>
            管理工艺流程和工步的创建与编辑
          </DialogDescription>
        </DialogHeader>

        <div className="flex-1 overflow-y-auto">
          {selectedAction === 'create' ? (
            <ProcessWizard
              onCreateProcess={handleCreateProcess}
              trigger={null}
            />
          ) : selectedAction === 'load' ? (
            renderProcessList()
          ) : (
            renderActionSelection()
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
